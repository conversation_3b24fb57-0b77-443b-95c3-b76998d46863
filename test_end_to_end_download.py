#!/usr/bin/env python3
"""
CRITICAL TEST: End-to-End PDF Download Verification

This test verifies the complete download workflow that was NOT tested in our previous tests:
1. Navigate to a real day with documents
2. Discover actual case documents  
3. Navigate to individual case pages
4. Find and interact with PDF download buttons/links
5. Download actual PDF files
6. Verify file integrity and completeness
7. Test document viewer interactions
8. Check for authentication/restriction issues
"""

import asyncio
import hashlib
import logging
from pathlib import Path
import aiofiles
import aiohttp

from gazette_scraper.caselaw.pipeline import CaseLawPipeline
from gazette_scraper.caselaw.models import CaseLawConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_end_to_end_download():
    """CRITICAL TEST: Complete PDF download workflow verification."""
    logger.info("🚨 CRITICAL TEST: Starting End-to-End Download Verification")
    logger.info("This test will verify the complete PDF download workflow that was missing from previous tests")
    
    # Initialize configuration for real download testing
    config = CaseLawConfig(
        output_dir=Path("test_e2e_downloads"),
        headless=False,  # Use non-headless to see what's happening
        debug_mode=True,
        screenshot_on_error=True,
        max_concurrent_downloads=1,  # Conservative for testing
        browser_timeout=60000,  # Longer timeout for downloads
        max_retry_attempts=3
    )

    # Ensure output directory exists
    config.output_dir.mkdir(exist_ok=True)
    
    pipeline = CaseLawPipeline(config)
    
    try:
        await pipeline.start()
        
        # Step 1: Find a real day with documents using limited discovery
        logger.info("=" * 80)
        logger.info("STEP 1: DISCOVERING REAL CASES FOR DOWNLOAD TESTING")
        logger.info("=" * 80)
        
        # Use a timeout to get some real nodes quickly
        try:
            all_nodes = await asyncio.wait_for(
                pipeline.navigator.discover_court_tree(), 
                timeout=180  # 3 minutes to get some real nodes
            )
        except asyncio.TimeoutError:
            logger.warning("Discovery timed out - this is expected, continuing with discovered nodes")
            all_nodes = []
        
        # Find day nodes with documents
        day_nodes = [node for node in all_nodes if node.node_type.value == "day" and node.document_count > 0]
        
        if not day_nodes:
            logger.error("❌ CRITICAL: No day nodes with documents found!")
            logger.error("Cannot test PDF download workflow without real documents")
            return False
        
        # Select the first day node with documents for testing
        test_day = day_nodes[0]
        logger.info(f"✅ Selected test day: {test_day.full_path}")
        logger.info(f"   Expected documents: {test_day.document_count}")
        
        # Step 2: Discover actual cases for this day
        logger.info("=" * 80)
        logger.info("STEP 2: DISCOVERING ACTUAL CASE DOCUMENTS")
        logger.info("=" * 80)
        
        cases = await pipeline.navigator.discover_cases_for_day(test_day)
        
        if not cases:
            logger.error("❌ CRITICAL: No cases discovered for the selected day!")
            logger.error("This indicates a problem with case discovery logic")
            return False
        
        logger.info(f"✅ Discovered {len(cases)} cases")
        for i, case in enumerate(cases[:3]):  # Show first 3
            logger.info(f"   {i+1}. {case.case_title}")
            logger.info(f"      Download URL: {case.download_url}")
            logger.info(f"      Case Page: {case.case_page_url}")
        
        # Step 3: Test complete download workflow for first case
        logger.info("=" * 80)
        logger.info("STEP 3: TESTING COMPLETE PDF DOWNLOAD WORKFLOW")
        logger.info("=" * 80)
        
        test_case = cases[0]
        logger.info(f"Testing download for: {test_case.case_title}")
        
        # Test the complete download process
        download_success = await test_complete_download_process(pipeline, test_case)
        
        if not download_success:
            logger.error("❌ CRITICAL: PDF download workflow failed!")
            return False
        
        # Step 4: Verify file integrity
        logger.info("=" * 80)
        logger.info("STEP 4: VERIFYING FILE INTEGRITY")
        logger.info("=" * 80)
        
        downloaded_file = config.output_dir / test_case.filename
        if not downloaded_file.exists():
            logger.error("❌ CRITICAL: Downloaded file does not exist!")
            return False
        
        # Check file size
        file_size = downloaded_file.stat().st_size
        logger.info(f"Downloaded file size: {file_size:,} bytes")
        
        if file_size < 1000:  # Less than 1KB is suspicious
            logger.error("❌ CRITICAL: Downloaded file is too small - likely not a valid PDF!")
            return False
        
        # Verify it's actually a PDF
        with open(downloaded_file, 'rb') as f:
            header = f.read(4)
            if header != b'%PDF':
                logger.error("❌ CRITICAL: Downloaded file is not a valid PDF!")
                logger.error(f"File header: {header}")
                return False
        
        logger.info("✅ File integrity verified - valid PDF downloaded")
        
        # Step 5: Test state management with real download
        logger.info("=" * 80)
        logger.info("STEP 5: TESTING STATE MANAGEMENT WITH REAL DOWNLOAD")
        logger.info("=" * 80)
        
        # Save to state
        pipeline.state.save_case_file(test_case)
        
        # Check if marked as downloaded
        is_already_downloaded = pipeline.state.is_case_already_downloaded(test_case)
        logger.info(f"State management test: {'✅ Working' if not is_already_downloaded else '⚠️ Issue detected'}")
        
        # Step 6: Test multiple downloads (if we have more cases)
        if len(cases) > 1:
            logger.info("=" * 80)
            logger.info("STEP 6: TESTING MULTIPLE DOWNLOADS")
            logger.info("=" * 80)
            
            # Test downloading 2-3 more cases
            for i, case in enumerate(cases[1:3], 1):
                logger.info(f"Testing download {i+1}: {case.case_title}")
                success = await test_complete_download_process(pipeline, case)
                if not success:
                    logger.warning(f"⚠️ Download {i+1} failed - continuing with test")
        
        # Final verification
        logger.info("=" * 80)
        logger.info("FINAL VERIFICATION RESULTS")
        logger.info("=" * 80)
        
        downloaded_files = list(config.output_dir.glob("*.pdf"))
        logger.info(f"Total PDF files downloaded: {len(downloaded_files)}")
        
        total_size = sum(f.stat().st_size for f in downloaded_files)
        logger.info(f"Total download size: {total_size:,} bytes")
        
        # Verify all downloads are valid PDFs
        valid_pdfs = 0
        for pdf_file in downloaded_files:
            with open(pdf_file, 'rb') as f:
                if f.read(4) == b'%PDF':
                    valid_pdfs += 1
        
        logger.info(f"Valid PDF files: {valid_pdfs}/{len(downloaded_files)}")
        
        if valid_pdfs == len(downloaded_files) and len(downloaded_files) > 0:
            logger.info("🎉 END-TO-END DOWNLOAD TEST PASSED!")
            logger.info("✅ Complete PDF download workflow is functional")
            logger.info("✅ File integrity verification working")
            logger.info("✅ State management with real downloads working")
            return True
        else:
            logger.error("❌ END-TO-END DOWNLOAD TEST FAILED!")
            return False
        
    except Exception as e:
        logger.error(f"❌ CRITICAL ERROR in end-to-end test: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await pipeline.close()


async def test_complete_download_process(pipeline, case_file):
    """Test the complete download process for a single case."""
    try:
        # Use the pipeline's download method
        logger.info(f"Attempting download via pipeline method...")
        success = await pipeline._download_case_file(case_file)
        
        if success:
            logger.info("✅ Pipeline download method successful")
            return True
        else:
            logger.warning("⚠️ Pipeline download method failed - testing manual approach")
            
            # Test manual download approach
            return await test_manual_download(case_file)
            
    except Exception as e:
        logger.error(f"❌ Download process failed: {e}")
        return False


async def test_manual_download(case_file):
    """Test manual download approach to verify URL accessibility."""
    try:
        logger.info(f"Testing manual download from: {case_file.download_url}")
        
        async with aiohttp.ClientSession() as session:
            async with session.get(str(case_file.download_url)) as response:
                if response.status == 200:
                    content = await response.read()
                    
                    # Check if it's a PDF
                    if content.startswith(b'%PDF'):
                        logger.info("✅ Manual download successful - valid PDF content")
                        return True
                    else:
                        logger.error("❌ Manual download returned non-PDF content")
                        logger.error(f"Content type: {response.headers.get('content-type')}")
                        logger.error(f"Content preview: {content[:100]}")
                        return False
                else:
                    logger.error(f"❌ Manual download failed - HTTP {response.status}")
                    return False
                    
    except Exception as e:
        logger.error(f"❌ Manual download error: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(test_end_to_end_download())
    if success:
        print("\n🎉 END-TO-END DOWNLOAD VERIFICATION PASSED!")
        print("The complete PDF download workflow is functional and ready for production.")
    else:
        print("\n❌ END-TO-END DOWNLOAD VERIFICATION FAILED!")
        print("CRITICAL ISSUES found in the PDF download workflow that must be fixed before production.")
    
    exit(0 if success else 1)
