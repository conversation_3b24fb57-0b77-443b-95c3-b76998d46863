#!/usr/bin/env python3
"""
Debug script to manually check what's actually available for High Court 2024.
"""

import asyncio
import logging
from gazette_scraper.caselaw.navigator import CaseLawNavigator
from gazette_scraper.config import load_caselaw_config

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def debug_high_court_2024():
    """Debug what's actually available for High Court 2024."""
    
    # Load configuration
    config = load_caselaw_config()
    
    # Initialize navigator
    navigator = CaseLawNavigator(config)
    
    try:
        await navigator.start()
        logger.info("✅ Browser initialized successfully")
        
        # Get a page
        context = navigator.contexts[0]
        page = await context.new_page()
        
        # Navigate to case-law page
        url = f"{config.base_url}{config.caselaw_path}"
        logger.info(f"🌐 Navigating to: {url}")
        await page.goto(url, wait_until="networkidle", timeout=config.page_load_timeout)
        await asyncio.sleep(config.navigation_delay)
        
        # Find and expand High Court
        logger.info("🔍 Looking for High Court...")
        high_court_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
        
        high_court_element = None
        for element in high_court_elements:
            text = await element.text_content()
            if text and "High Court" in text and "Commercial" not in text:
                logger.info(f"📋 Found High Court: {text}")
                high_court_element = element
                break
        
        if not high_court_element:
            logger.error("❌ High Court not found!")
            return
        
        # Expand High Court
        logger.info("🔄 Expanding High Court...")
        await high_court_element.click()
        await asyncio.sleep(config.navigation_delay)
        
        # Look for 2024
        logger.info("🔍 Looking for 2024...")
        year_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
        
        year_2024_element = None
        for element in year_elements:
            text = await element.text_content()
            if text and "2024" in text:
                logger.info(f"📅 Found 2024: {text}")
                year_2024_element = element
                break
        
        if not year_2024_element:
            logger.error("❌ 2024 not found in High Court!")
            return
        
        # Expand 2024
        logger.info("🔄 Expanding 2024...")
        await year_2024_element.click()
        await asyncio.sleep(config.navigation_delay)
        
        # Now check what months are actually available
        logger.info("🔍 Checking what months are actually available...")
        month_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
        
        found_months = []
        for element in month_elements:
            text = await element.text_content()
            if text:
                # Check if this looks like a month
                import re
                month_match = re.search(r'\b(January|February|March|April|May|June|July|August|September|October|November|December|Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\b', text, re.IGNORECASE)
                if month_match:
                    found_months.append(text.strip())
                    logger.info(f"📆 Found potential month: {text.strip()}")
        
        if found_months:
            logger.info(f"✅ Found {len(found_months)} potential months:")
            for month in found_months:
                logger.info(f"  - {month}")
        else:
            logger.info("❌ No months found for High Court 2024!")
        
        # Also check all elements to see what's actually there
        logger.info("🔍 All elements after expanding High Court 2024:")
        all_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
        for i, element in enumerate(all_elements):
            text = await element.text_content()
            if text:
                logger.info(f"  [{i}] {text.strip()}")
        
    except Exception as e:
        logger.error(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await navigator.close()
        logger.info("🧹 Cleanup completed")

if __name__ == "__main__":
    asyncio.run(debug_high_court_2024())
