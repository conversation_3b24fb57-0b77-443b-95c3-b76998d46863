#!/bin/bash

# GCP Infrastructure Deployment Script for Rwanda Gazette Scraper
# This script sets up all necessary GCP resources for the application

set -euo pipefail  # Exit on error, undefined variables, pipe failures

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID="${GOOGLE_CLOUD_PROJECT:-rwandan-law-bot-440710}"
SERVICE_NAME="rwanda-gazette-scraper"
REGION="us-central1"
GCS_BUCKET="rwandan_laws"
ARTIFACT_REGISTRY_REPO="gazette-scraper"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if gcloud is installed and configured
check_gcloud() {
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI is not installed. Please install it first:"
        print_error "https://cloud.google.com/sdk/docs/install"
        exit 1
    fi

    if [[ -z "$PROJECT_ID" ]]; then
        PROJECT_ID=$(gcloud config get-value project 2>/dev/null || echo "")
        if [[ -z "$PROJECT_ID" ]]; then
            print_error "No Google Cloud project set. Please set it with:"
            print_error "gcloud config set project YOUR_PROJECT_ID"
            exit 1
        fi
    fi

    print_status "Using Google Cloud Project: $PROJECT_ID"
}

# Function to enable required APIs
enable_apis() {
    print_status "Enabling required Google Cloud APIs..."
    
    local apis=(
        "cloudbuild.googleapis.com"
        "run.googleapis.com"
        "artifactregistry.googleapis.com"
        "storage-api.googleapis.com"
        "secretmanager.googleapis.com"
        "aiplatform.googleapis.com"
        "vpcaccess.googleapis.com"
        "compute.googleapis.com"
        "iam.googleapis.com"
    )

    for api in "${apis[@]}"; do
        print_status "Enabling $api..."
        gcloud services enable "$api" --project="$PROJECT_ID"
    done

    print_success "All required APIs enabled"
}

# Function to create Artifact Registry repository
create_artifact_registry() {
    print_status "Creating Artifact Registry repository..."
    
    # Check if repository already exists
    if gcloud artifacts repositories describe "$ARTIFACT_REGISTRY_REPO" \
        --location="$REGION" --project="$PROJECT_ID" &>/dev/null; then
        print_warning "Artifact Registry repository already exists"
    else
        gcloud artifacts repositories create "$ARTIFACT_REGISTRY_REPO" \
            --repository-format=docker \
            --location="$REGION" \
            --description="Container registry for Rwanda Gazette Scraper" \
            --project="$PROJECT_ID"
        print_success "Artifact Registry repository created"
    fi

    # Configure Docker to use the repository
    gcloud auth configure-docker "${REGION}-docker.pkg.dev" --quiet
    print_success "Docker configured for Artifact Registry"
}

# Function to create GCS bucket
create_gcs_bucket() {
    print_status "Creating Google Cloud Storage bucket..."
    
    # Check if bucket already exists
    if gsutil ls -b "gs://$GCS_BUCKET" &>/dev/null; then
        print_warning "GCS bucket already exists"
    else
        gsutil mb -p "$PROJECT_ID" -c STANDARD -l "$REGION" "gs://$GCS_BUCKET"
        print_success "GCS bucket created"
    fi

    # Set up bucket versioning and lifecycle
    print_status "Configuring bucket versioning and lifecycle..."
    gsutil versioning set on "gs://$GCS_BUCKET"
    
    # Create lifecycle configuration
    cat > /tmp/lifecycle.json << EOF
{
  "lifecycle": {
    "rule": [
      {
        "action": {"type": "Delete"},
        "condition": {"age": 365, "isLive": false}
      },
      {
        "action": {"type": "SetStorageClass", "storageClass": "NEARLINE"},
        "condition": {"age": 30}
      }
    ]
  }
}
EOF
    gsutil lifecycle set /tmp/lifecycle.json "gs://$GCS_BUCKET"
    rm /tmp/lifecycle.json
    print_success "Bucket lifecycle configured"
}

# Function to create service account
create_service_account() {
    print_status "Creating service account..."
    
    local sa_email="${SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"
    
    # Check if service account already exists
    if gcloud iam service-accounts describe "$sa_email" --project="$PROJECT_ID" &>/dev/null; then
        print_warning "Service account already exists"
    else
        gcloud iam service-accounts create "$SERVICE_NAME" \
            --description="Service account for Rwanda Gazette Scraper" \
            --display-name="Rwanda Gazette Scraper Service Account" \
            --project="$PROJECT_ID"
        print_success "Service account created"
    fi

    # Grant necessary roles
    print_status "Granting IAM roles to service account..."
    local roles=(
        "roles/run.invoker"
        "roles/storage.objectAdmin"
        "roles/secretmanager.secretAccessor"
        "roles/aiplatform.user"
        "roles/logging.logWriter"
        "roles/monitoring.metricWriter"
        "roles/cloudtrace.agent"
    )

    for role in "${roles[@]}"; do
        gcloud projects add-iam-policy-binding "$PROJECT_ID" \
            --member="serviceAccount:$sa_email" \
            --role="$role" \
            --quiet
    done

    print_success "IAM roles granted to service account"
}

# Function to create secrets in Secret Manager
create_secrets() {
    print_status "Setting up Secret Manager secrets..."
    
    # Supabase Service Role Key
    if ! gcloud secrets describe "supabase-service-key" --project="$PROJECT_ID" &>/dev/null; then
        print_status "Creating Supabase service key secret..."
        gcloud secrets create "supabase-service-key" \
            --description="Supabase service role key for Rwanda Gazette Scraper" \
            --project="$PROJECT_ID"
        print_warning "Please add the Supabase service role key value:"
        print_warning "gcloud secrets versions add supabase-service-key --data-file=- --project=$PROJECT_ID"
        print_warning "Then paste your key and press Ctrl+D"
    else
        print_warning "Supabase service key secret already exists"
    fi

    # Gemini API Key
    if ! gcloud secrets describe "gemini-api-key" --project="$PROJECT_ID" &>/dev/null; then
        print_status "Creating Gemini API key secret..."
        gcloud secrets create "gemini-api-key" \
            --description="Google Gemini API key for document processing" \
            --project="$PROJECT_ID"
        print_warning "Please add the Gemini API key value:"
        print_warning "gcloud secrets versions add gemini-api-key --data-file=- --project=$PROJECT_ID"
        print_warning "Then paste your key and press Ctrl+D"
    else
        print_warning "Gemini API key secret already exists"
    fi

    # Grant service account access to secrets
    local sa_email="${SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"
    for secret in "supabase-service-key" "gemini-api-key"; do
        gcloud secrets add-iam-policy-binding "$secret" \
            --member="serviceAccount:$sa_email" \
            --role="roles/secretmanager.secretAccessor" \
            --project="$PROJECT_ID"
    done

    print_success "Secrets configured"
}

# Function to create VPC connector (optional, for private networking)
create_vpc_connector() {
    print_status "Creating VPC connector for private networking..."
    
    local connector_name="gazette-scraper-connector"
    
    # Check if connector already exists
    if gcloud compute networks vpc-access connectors describe "$connector_name" \
        --region="$REGION" --project="$PROJECT_ID" &>/dev/null; then
        print_warning "VPC connector already exists"
    else
        # Create a custom VPC network first
        if ! gcloud compute networks describe "gazette-scraper-vpc" --project="$PROJECT_ID" &>/dev/null; then
            gcloud compute networks create "gazette-scraper-vpc" \
                --subnet-mode=custom \
                --project="$PROJECT_ID"
        fi

        # Create subnet
        if ! gcloud compute networks subnets describe "gazette-scraper-subnet" \
            --region="$REGION" --project="$PROJECT_ID" &>/dev/null; then
            gcloud compute networks subnets create "gazette-scraper-subnet" \
                --network="gazette-scraper-vpc" \
                --range="10.0.0.0/24" \
                --region="$REGION" \
                --project="$PROJECT_ID"
        fi

        # Create VPC connector
        gcloud compute networks vpc-access connectors create "$connector_name" \
            --region="$REGION" \
            --subnet="gazette-scraper-subnet" \
            --subnet-project="$PROJECT_ID" \
            --min-instances=2 \
            --max-instances=10 \
            --project="$PROJECT_ID"
        print_success "VPC connector created"
    fi
}

# Function to build and deploy the application
deploy_application() {
    print_status "Building and deploying application..."
    
    # Update the service YAML with actual project ID
    sed "s/PROJECT_ID/$PROJECT_ID/g" cloud-run-service.yaml > /tmp/cloud-run-service.yaml

    # Submit Cloud Build job
    gcloud builds submit . \
        --config=cloudbuild.yaml \
        --project="$PROJECT_ID" \
        --substitutions="_SERVICE_NAME=$SERVICE_NAME,_REGION=$REGION"

    print_success "Application deployed successfully"
    
    # Get the service URL
    local service_url
    service_url=$(gcloud run services describe "$SERVICE_NAME" \
        --region="$REGION" \
        --project="$PROJECT_ID" \
        --format="value(status.url)")
    
    print_success "Service URL: $service_url"
}

# Function to set up monitoring and logging
setup_monitoring() {
    print_status "Setting up monitoring and logging..."
    
    # Create log-based metrics (optional)
    print_status "Log-based metrics can be created in the Cloud Console"
    
    # Create uptime check
    print_status "Uptime monitoring can be configured in the Cloud Console"
    
    print_success "Monitoring setup completed (manual steps may be required)"
}

# Function to display post-deployment instructions
show_post_deployment_info() {
    cat << EOF

${GREEN}===============================================${NC}
${GREEN}   Rwanda Gazette Scraper Deployment Complete${NC}
${GREEN}===============================================${NC}

${BLUE}Service Information:${NC}
- Project ID: $PROJECT_ID
- Service Name: $SERVICE_NAME
- Region: $REGION
- GCS Bucket: gs://$GCS_BUCKET

${BLUE}Next Steps:${NC}
1. Add secret values if not done already:
   ${YELLOW}gcloud secrets versions add supabase-service-key --data-file=- --project=$PROJECT_ID${NC}
   ${YELLOW}gcloud secrets versions add gemini-api-key --data-file=- --project=$PROJECT_ID${NC}

2. Test the deployment:
   ${YELLOW}curl -X GET \$(gcloud run services describe $SERVICE_NAME --region=$REGION --format='value(status.url)')/health${NC}

3. Monitor the service:
   ${YELLOW}gcloud run services logs read $SERVICE_NAME --region=$REGION --limit=50${NC}

4. Scale the service if needed:
   ${YELLOW}gcloud run services update $SERVICE_NAME --region=$REGION --max-instances=20${NC}

${BLUE}Usage:${NC}
The service provides a CLI interface for batch processing:
- Submit batch jobs via the /batch endpoint
- Monitor job status and retrieve results
- Process Rwanda Official Gazette PDFs with AI-powered extraction

${BLUE}Cost Optimization:${NC}
- Service scales to zero when not in use
- Storage lifecycle policies are configured
- Use preemptible instances where possible

EOF
}

# Main execution
main() {
    print_status "Starting GCP infrastructure deployment for Rwanda Gazette Scraper"
    
    check_gcloud
    enable_apis
    create_artifact_registry
    create_gcs_bucket
    create_service_account
    create_secrets
    create_vpc_connector
    deploy_application
    setup_monitoring
    show_post_deployment_info
}

# Run main function
main "$@"