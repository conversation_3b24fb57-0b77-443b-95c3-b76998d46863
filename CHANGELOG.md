# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [0.2.0] - 2025-01-XX

### Added
- **Quality Gates Milestone**: Established comprehensive code quality standards
  - Test coverage threshold raised to 70% (from 66%)
  - Ruff linting with TCH rules for type-checking import organization
  - Strict Mypy type checking enforcement
  - Pre-commit hooks for code quality validation

- **Enhanced Test Coverage** (+4% coverage increase):
  - Pipeline integration tests with multi-page HTML fixtures
  - Comprehensive duplicate file handling tests
  - Storage module unit tests with SHA-256 checksum validation
  - Edge error path tests (410 Gone → refresh → success scenarios)
  - Network timeout and malformed HTML recovery tests

- **Developer Experience Improvements**:
  - Updated Makefile with `cov` target that fails under 70%
  - Enhanced CI/CD with coverage gate enforcement
  - Quality Gates documentation in README.md
  - Comprehensive CONTRIBUTING.md with fixture policy
  - Dry-run mode documentation and usage examples

### Changed
- **Code Quality Standards**:
  - Moved type-checking imports to `TYPE_CHECKING` blocks (TCH rules)
  - Updated GitHub Actions workflow to enforce 70% coverage threshold
  - Enhanced lint target to include TCH import organization rules

- **Documentation**:
  - Added Quality Gates section to README.md
  - Updated development workflow documentation
  - Modernized code quality tool references (Ruff instead of Black/Flake8)

### Technical Details
- Coverage increased from 66% to 70%+ through strategic test additions
- Zero Ruff warnings baseline established and maintained
- All TCH001/TCH003 import violations resolved
- CI gate updated to fail builds under 70% coverage
- Pre-commit configuration for automated quality checks

### Development
- Enhanced test fixtures for realistic HTML parsing scenarios
- Mock-based testing with no live HTTP dependencies
- Comprehensive duplicate detection and deduplication testing
- Edge case handling for network failures and recoveries

---

## [0.1.0] - 2024-XX-XX

### Added
- Initial release of Rwanda Official Gazette scraper
- Complete discovery and download of gazette PDFs from minijust.gov.rw
- Idempotent scraping with SHA-256 deduplication
- SQLite-based state management for resumable scraping
- Rate-limited and concurrent downloading
- Exponential backoff retry logic
- CSV manifest generation with metadata
- Proxy support for Rwanda IP requirements
- Optional Google Cloud Storage integration
- CLI interface with configuration support

### Features
- Multi-level folder discovery (year → month → file)
- Robust HTML parsing with BeautifulSoup
- File size validation and checksum verification
- Comprehensive logging and error tracking
- Docker support for containerized deployment
- Configuration via TOML files and environment variables

### Technical Implementation
- Python 3.11+ with Poetry dependency management
- Pydantic models for data validation
- aiohttp-based HTTP client with retry logic
- SQLite database for persistence
- Modular architecture with separation of concerns