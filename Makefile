.PHONY: help setup install test lint format type-check clean build run-dev cov all historical-pull

help:  ## Show this help
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

install:  ## Install dependencies with poetry
	poetry install

test:  ## Run tests
	poetry run pytest -v --cov=gazette_scraper --cov-report=term-missing

test-quick:  ## Run tests without coverage
	poetry run pytest -v

lint:  ## Run linting checks (including TCH type-checking imports)
	poetry run ruff check . --select TCH
	poetry run ruff check .

format:  ## Format code with ruff
	poetry run ruff format .

format-check:  ## Check code formatting
	poetry run ruff format --check .

type-check:  ## Run type checking with mypy (strict)
	poetry run mypy --strict .

type:  ## Alias for type-check
	poetry run mypy --strict .

cov:  ## Run tests with coverage (fails under 70%)
	poetry run pytest --cov=gazette_scraper --cov-report=term-missing --cov-report=xml
	poetry run coverage report --fail-under=70

all: lint type cov build  ## Run all quality gates

check-all: format-check lint type-check test  ## Run all code quality checks

setup:  ## Set up development environment
	poetry install

clean:  ## Clean build artifacts and cache
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf .coverage
	rm -rf htmlcov/
	find . -type d -name __pycache__ -delete
	find . -type f -name "*.pyc" -delete

build:  ## Build package
	poetry build

run-dev:  ## Run scraper in development mode with verbose logging
	poetry run gazette-scraper -v fetch --max 5

run-stats:  ## Show scraping statistics
	poetry run gazette-scraper stats

run-list:  ## List downloaded files
	poetry run gazette-scraper list-files

setup-dev:  ## Set up development environment
	poetry install --with dev
	poetry run pre-commit install

docker-build:  ## Build Docker image
	docker build -t gazette-scraper .

docker-run:  ## Run in Docker container
	docker run --rm -v $(PWD)/data:/app/data gazette-scraper fetch --max 3

historical-pull:  ## Run complete historical gazette scraping (2004-present) with GCS integration
	@./scripts/historical-pull.sh