#!/usr/bin/env python3
"""
CRITICAL TEST: Direct PDF Download Verification

This test directly tests the PDF download workflow by:
1. Creating a known day node from our successful navigation tests
2. Testing case discovery for that specific day
3. Testing the complete PDF download process
4. Verifying file integrity

This bypasses the timeout issues in full tree discovery.
"""

import asyncio
import logging
from pathlib import Path
import aiohttp

from gazette_scraper.caselaw.pipeline import CaseLawPipeline
from gazette_scraper.caselaw.models import CaseLawConfig, CourtNode, NodeType

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_pdf_download_direct():
    """Test PDF download workflow directly with a known day node."""
    logger.info("🚨 CRITICAL TEST: Direct PDF Download Verification")
    
    # Initialize configuration
    config = CaseLawConfig(
        output_dir=Path("test_pdf_downloads"),
        headless=False,  # Use non-headless to see interactions
        debug_mode=True,
        screenshot_on_error=True,
        max_concurrent_downloads=1,
        browser_timeout=60000,
        max_retry_attempts=3
    )

    # Ensure output directory exists
    config.output_dir.mkdir(exist_ok=True)
    
    pipeline = CaseLawPipeline(config)
    
    try:
        await pipeline.start()
        
        # Create known day nodes from our successful navigation tests
        # We know these exist from our previous testing
        test_day_nodes = [
            CourtNode(
                court_name="Supreme Court",
                year=2025,
                month=2,  # February
                day=20,
                node_type=NodeType.DAY,
                full_path="Supreme Court/2025/February/20",
                document_count=1,
                click_selector='a[href="/laws/judgement/2"]:has-text("20 (1)")'
            ),
            CourtNode(
                court_name="Court of Appeal",
                year=2025,
                month=4,  # April
                day=15,
                node_type=NodeType.DAY,
                full_path="Court of Appeal/2025/April/15",
                document_count=2,
                click_selector='a[href="/laws/judgement/2"]:has-text("15 (2)")'
            ),
            CourtNode(
                court_name="High Court",
                year=2024,
                month=6,  # June
                day=10,
                node_type=NodeType.DAY,
                full_path="High Court/2024/June/10",
                document_count=1,
                click_selector='a[href="/laws/judgement/2"]:has-text("10 (1)")'
            )
        ]
        
        successful_downloads = 0
        total_attempts = 0
        
        for test_day in test_day_nodes:
            logger.info("=" * 80)
            logger.info(f"TESTING: {test_day.full_path}")
            logger.info("=" * 80)
            
            total_attempts += 1
            
            # Step 1: Test case discovery for this day
            logger.info(f"Step 1: Discovering cases for {test_day.full_path}")
            
            try:
                cases = await pipeline.navigator.discover_cases_for_day(test_day)
                
                if not cases:
                    logger.warning(f"⚠️ No cases found for {test_day.full_path}")
                    continue
                
                logger.info(f"✅ Found {len(cases)} cases")
                for i, case in enumerate(cases):
                    logger.info(f"   {i+1}. {case.case_title}")
                    logger.info(f"      Download URL: {case.download_url}")
                
                # Step 2: Test downloading the first case
                test_case = cases[0]
                logger.info(f"Step 2: Testing download of '{test_case.case_title}'")
                
                # Test the complete download workflow
                download_result = await test_complete_download_workflow(pipeline, test_case, config.output_dir)
                
                if download_result:
                    successful_downloads += 1
                    logger.info(f"✅ Download successful for {test_day.full_path}")
                else:
                    logger.warning(f"⚠️ Download failed for {test_day.full_path}")
                
            except Exception as e:
                logger.error(f"❌ Error testing {test_day.full_path}: {e}")
                continue
        
        # Final results
        logger.info("=" * 80)
        logger.info("FINAL TEST RESULTS")
        logger.info("=" * 80)
        
        logger.info(f"Total day nodes tested: {total_attempts}")
        logger.info(f"Successful downloads: {successful_downloads}")
        logger.info(f"Success rate: {successful_downloads/total_attempts*100:.1f}%" if total_attempts > 0 else "No tests completed")
        
        # Check downloaded files
        downloaded_files = list(config.output_dir.glob("*.pdf"))
        logger.info(f"Total PDF files downloaded: {len(downloaded_files)}")
        
        if downloaded_files:
            total_size = sum(f.stat().st_size for f in downloaded_files)
            logger.info(f"Total download size: {total_size:,} bytes")
            
            # Verify all are valid PDFs
            valid_pdfs = 0
            for pdf_file in downloaded_files:
                try:
                    with open(pdf_file, 'rb') as f:
                        if f.read(4) == b'%PDF':
                            valid_pdfs += 1
                            logger.info(f"✅ Valid PDF: {pdf_file.name} ({pdf_file.stat().st_size:,} bytes)")
                        else:
                            logger.error(f"❌ Invalid PDF: {pdf_file.name}")
                except Exception as e:
                    logger.error(f"❌ Error checking {pdf_file.name}: {e}")
            
            logger.info(f"Valid PDF files: {valid_pdfs}/{len(downloaded_files)}")
            
            if valid_pdfs > 0:
                logger.info("🎉 PDF DOWNLOAD VERIFICATION PASSED!")
                logger.info("✅ Complete PDF download workflow is functional")
                return True
            else:
                logger.error("❌ No valid PDFs downloaded")
                return False
        else:
            logger.error("❌ No PDF files downloaded")
            return False
        
    except Exception as e:
        logger.error(f"❌ CRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await pipeline.close()


async def test_complete_download_workflow(pipeline, case_file, output_dir):
    """Test the complete download workflow for a single case."""
    try:
        logger.info(f"Testing download workflow for: {case_file.case_title}")
        logger.info(f"Download URL: {case_file.download_url}")
        
        # Method 1: Test pipeline download method
        logger.info("Method 1: Testing pipeline download method...")
        
        try:
            success = await pipeline._download_case_file(case_file)
            if success:
                # Check if file was actually created
                expected_file = output_dir / case_file.filename
                if expected_file.exists() and expected_file.stat().st_size > 1000:
                    logger.info("✅ Pipeline download method successful")
                    return True
                else:
                    logger.warning("⚠️ Pipeline method claimed success but file not found/too small")
            else:
                logger.warning("⚠️ Pipeline download method returned failure")
        except Exception as e:
            logger.warning(f"⚠️ Pipeline download method error: {e}")
        
        # Method 2: Test direct HTTP download
        logger.info("Method 2: Testing direct HTTP download...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(str(case_file.download_url)) as response:
                    logger.info(f"HTTP Response: {response.status}")
                    logger.info(f"Content-Type: {response.headers.get('content-type')}")
                    
                    if response.status == 200:
                        content = await response.read()
                        logger.info(f"Downloaded {len(content):,} bytes")
                        
                        # Check if it's a PDF
                        if content.startswith(b'%PDF'):
                            # Save the file
                            test_file = output_dir / f"direct_download_{case_file.filename}"
                            with open(test_file, 'wb') as f:
                                f.write(content)
                            
                            logger.info(f"✅ Direct HTTP download successful: {test_file}")
                            return True
                        else:
                            logger.error("❌ Downloaded content is not a PDF")
                            logger.error(f"Content preview: {content[:200]}")
                            
                            # Save for inspection
                            debug_file = output_dir / f"debug_{case_file.filename}.html"
                            with open(debug_file, 'wb') as f:
                                f.write(content)
                            logger.info(f"Saved content for debugging: {debug_file}")
                            return False
                    else:
                        logger.error(f"❌ HTTP download failed: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"❌ Direct HTTP download error: {e}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Complete download workflow error: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(test_pdf_download_direct())
    if success:
        print("\n🎉 PDF DOWNLOAD VERIFICATION PASSED!")
        print("The complete PDF download workflow is functional.")
    else:
        print("\n❌ PDF DOWNLOAD VERIFICATION FAILED!")
        print("CRITICAL ISSUES found in the PDF download workflow.")
    
    exit(0 if success else 1)
