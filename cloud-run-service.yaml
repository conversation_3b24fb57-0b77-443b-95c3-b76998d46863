# Cloud Run Service Configuration
# Defines the service specification for Rwanda Gazette Scraper deployment
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: rwanda-gazette-scraper
  labels:
    cloud.googleapis.com/location: us-central1
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/ingress-status: all
    run.googleapis.com/launch-stage: GA
    run.googleapis.com/description: "Rwanda Official Gazette PDF Scraper and Processing Service"
spec:
  template:
    metadata:
      annotations:
        # Scaling configuration
        autoscaling.knative.dev/minScale: "0"  # Scale to zero for cost savings
        autoscaling.knative.dev/maxScale: "10"
        run.googleapis.com/cpu-throttling: "false"  # Don't throttle CPU when idle
        run.googleapis.com/execution-environment: gen2
        run.googleapis.com/sessionAffinity: true
        
        # Resource allocation
        run.googleapis.com/memory: "2Gi"
        run.googleapis.com/cpu: "2"
        
        # Network and security
        run.googleapis.com/vpc-access-connector: projects/PROJECT_ID/locations/us-central1/connectors/gazette-scraper-connector
        run.googleapis.com/vpc-access-egress: private-ranges-only
        
        # Service account for security
        iam.gke.io/gcp-service-account: rwanda-gazette-scraper@PROJECT_ID.iam.gserviceaccount.com
    spec:
      serviceAccountName: rwanda-gazette-scraper@PROJECT_ID.iam.gserviceaccount.com
      containerConcurrency: 1  # One request per container instance for long-running jobs
      timeoutSeconds: 3600  # 1 hour timeout for long-running scraping jobs
      containers:
      - image: us-central1-docker.pkg.dev/PROJECT_ID/gazette-scraper/rwanda-gazette-scraper:latest
        ports:
        - containerPort: 8080
          name: http1
        resources:
          limits:
            cpu: "2"
            memory: "2Gi"
          requests:
            cpu: "1"
            memory: "1Gi"
        env:
        # Google Cloud configuration
        - name: GOOGLE_CLOUD_PROJECT
          value: "PROJECT_ID"
        - name: GCS_BUCKET
          value: "rwandan_laws"
        - name: VERTEX_AI_LOCATION
          value: "us-central1"
        
        # Application configuration
        - name: SCRAPER_MAX_RETRIES
          value: "5"
        - name: SCRAPER_RATE_LIMIT
          value: "0.5"
        - name: SCRAPER_MAX_THREADS
          value: "2"
        - name: SCRAPER_SINCE_YEAR
          value: "2004"
        - name: SCRAPER_CRAWLER_DEPTH
          value: "2"
        
        # Secrets from Secret Manager
        - name: SUPABASE_SERVICE_ROLE_KEY
          valueFrom:
            secretKeyRef:
              name: supabase-service-key
              key: latest
        - name: GEMINI_API_KEY
          valueFrom:
            secretKeyRef:
              name: gemini-api-key
              key: latest
        
        # Health check endpoints
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        # Startup probe for slow-starting applications
        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30  # Allow up to 5 minutes for startup
  
  traffic:
  - percent: 100
    latestRevision: true