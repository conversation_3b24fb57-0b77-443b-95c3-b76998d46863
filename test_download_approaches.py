#!/usr/bin/env python3
"""
Test different approaches for PDF download: network monitoring vs download handling.
"""

import asyncio
import logging
from pathlib import Path
from gazette_scraper.caselaw.navigator import CaseLawNavigator
from gazette_scraper.caselaw.models import CaseLawConfig

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_download_approaches():
    """Test both network monitoring and download handling approaches."""
    
    config = CaseLawConfig(
        base_url="https://amategeko.gov.rw",
        caselaw_path="/laws/judgement/2",
        navigation_delay=1.0,
        max_retries=3
    )
    
    navigator = CaseLawNavigator(config)
    
    try:
        await navigator.start()
        context = navigator.contexts[0]
        page = await context.new_page()
        
        case_url = "https://amategeko.gov.rw/view/doc/8893/9229"
        logger.info(f"Testing download approaches for: {case_url}")
        
        # Approach 1: Network Monitoring (what we discovered)
        logger.info("\n=== APPROACH 1: Network Monitoring ===")
        
        pdf_urls = []
        def handle_request(request):
            if '.pdf' in request.url and 'files.amategeko.gov.rw' in request.url:
                pdf_urls.append(request.url)
                logger.info(f"📥 Captured PDF URL: {request.url[:100]}...")
        
        page.on("request", handle_request)
        
        await page.goto(case_url, wait_until="networkidle")
        await asyncio.sleep(3)
        
        if pdf_urls:
            logger.info(f"✅ Network monitoring found {len(pdf_urls)} PDF URL(s)")
            pdf_url = pdf_urls[0]
            logger.info(f"📋 Full PDF URL: {pdf_url}")
            
            # Test if we can download it directly
            try:
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    async with session.get(pdf_url) as response:
                        if response.status == 200:
                            content = await response.read()
                            logger.info(f"✅ Successfully downloaded PDF: {len(content)} bytes")
                            
                            # Save to test file
                            test_file = Path("test_download_network.pdf")
                            test_file.write_bytes(content)
                            logger.info(f"💾 Saved to: {test_file}")
                        else:
                            logger.error(f"❌ Download failed: HTTP {response.status}")
            except Exception as e:
                logger.error(f"❌ Network download failed: {e}")
        else:
            logger.warning("❌ No PDF URLs captured via network monitoring")
        
        # Approach 2: Playwright Download Handling
        logger.info("\n=== APPROACH 2: Playwright Download Handling ===")
        
        # Create a new page for clean test
        page2 = await context.new_page()
        
        # Set up download handling
        downloads = []
        def handle_download(download):
            downloads.append(download)
            logger.info(f"📥 Download started: {download.suggested_filename}")
        
        page2.on("download", handle_download)
        
        await page2.goto(case_url, wait_until="networkidle")
        await asyncio.sleep(3)
        
        # Look for download button and click it
        download_selectors = [
            'button:has(svg[data-icon="download"])',  # Button containing download icon
            '.rpv-toolbar button:has(svg)',  # Any button in PDF toolbar with SVG
            'button.btn-link:has(svg[data-icon="download"])',  # Specific button class
            '.text-white.btn.btn-link.btn-sm:has(svg[data-icon="download"])',  # Full class chain
        ]
        
        download_triggered = False
        for selector in download_selectors:
            try:
                logger.info(f"🔍 Trying download selector: {selector}")
                elements = await page2.query_selector_all(selector)
                logger.info(f"   Found {len(elements)} elements")
                
                for i, element in enumerate(elements):
                    try:
                        # Check if this element has download icon
                        svg = await element.query_selector('svg[data-icon="download"]')
                        if svg:
                            logger.info(f"   Element {i+1}: Found download icon, clicking...")
                            await element.click()
                            await asyncio.sleep(2)  # Wait for download to start
                            
                            if downloads:
                                download_triggered = True
                                logger.info(f"✅ Download triggered successfully!")
                                break
                            else:
                                logger.info(f"   Element {i+1}: Click didn't trigger download")
                        else:
                            logger.info(f"   Element {i+1}: No download icon found")
                    except Exception as e:
                        logger.info(f"   Element {i+1}: Click failed - {e}")
                
                if download_triggered:
                    break
                    
            except Exception as e:
                logger.debug(f"Selector {selector} failed: {e}")
        
        # Handle any downloads that were triggered
        if downloads:
            for i, download in enumerate(downloads):
                try:
                    logger.info(f"💾 Processing download {i+1}: {download.suggested_filename}")
                    
                    # Save the download
                    download_path = Path(f"test_download_playwright_{i+1}.pdf")
                    await download.save_as(download_path)
                    
                    # Verify the file
                    if download_path.exists():
                        size = download_path.stat().st_size
                        logger.info(f"✅ Download saved: {download_path} ({size} bytes)")
                        
                        # Check if it's a valid PDF
                        content = download_path.read_bytes()
                        if content.startswith(b'%PDF'):
                            logger.info(f"✅ Valid PDF file confirmed")
                        else:
                            logger.warning(f"⚠️  File doesn't appear to be a PDF")
                    else:
                        logger.error(f"❌ Download file not found: {download_path}")
                        
                except Exception as e:
                    logger.error(f"❌ Failed to process download {i+1}: {e}")
        else:
            logger.warning("❌ No downloads were triggered")
        
        await page2.close()
        
        # Approach 3: Hybrid - Get URL from network, verify with download
        logger.info("\n=== APPROACH 3: Hybrid Verification ===")
        
        if pdf_urls and downloads:
            logger.info("✅ Both approaches worked! We can use either method.")
        elif pdf_urls:
            logger.info("✅ Network monitoring works, download button approach failed")
            logger.info("💡 Recommendation: Use network monitoring approach")
        elif downloads:
            logger.info("✅ Download button works, network monitoring failed")
            logger.info("💡 Recommendation: Use Playwright download handling")
        else:
            logger.error("❌ Both approaches failed - need to investigate further")
        
        await page.close()
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        await navigator.close()

if __name__ == "__main__":
    asyncio.run(test_download_approaches())
