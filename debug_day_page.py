#!/usr/bin/env python3
"""
Debug script to investigate the day page loading issue.
This runs the scraper in non-headless mode so we can see what's happening.
"""

import asyncio
import logging
from pathlib import Path

from gazette_scraper.caselaw.models import CaseLawConfig
from gazette_scraper.caselaw.navigator import CaseLawNavigator

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def debug_day_page():
    """Debug the day page loading issue with visible browser."""
    logger.info("🔍 DEBUGGING DAY PAGE LOADING")
    
    # Create config with non-headless mode for debugging
    config = CaseLawConfig(
        output_dir=Path("debug_day_page"),
        headless=False,  # Show the browser
        debug_mode=True,
        screenshot_on_error=True,
        max_concurrent_downloads=1,
        browser_timeout=60000,
        page_load_timeout=30000,
        element_timeout=15000,
        navigation_delay=3.0,
        dry_run=True
    )
    
    # Ensure output directory exists
    config.output_dir.mkdir(exist_ok=True)
    config.screenshots_dir.mkdir(exist_ok=True)
    
    async with CaseLawNavigator(config) as navigator:
        logger.info("🌐 Browser started in visible mode")
        
        # Navigate to the specific day page
        target_path = "Supreme Court/2008/November/Day_11"
        logger.info(f"🎯 Navigating to: {target_path}")
        
        # Get a page from the navigator
        context = navigator.contexts[0]
        page = await context.new_page()
        
        try:
            # Navigate to the base URL
            logger.info(f"📍 Loading base URL: {config.base_url}{config.caselaw_path}")
            await page.goto(f"{config.base_url}{config.caselaw_path}")
            await asyncio.sleep(5)  # Wait for initial load
            
            # Take initial screenshot
            await navigator._take_screenshot(page, "01_initial_load")
            
            # Navigate through the hierarchy step by step
            logger.info("🔍 Step 1: Looking for Supreme Court")
            supreme_court_elements = await page.query_selector_all('text="Supreme Court"')
            logger.info(f"Found {len(supreme_court_elements)} elements with 'Supreme Court' text")
            
            if supreme_court_elements:
                # Click on Supreme Court
                await supreme_court_elements[0].click()
                await asyncio.sleep(3)
                await navigator._take_screenshot(page, "02_after_supreme_court_click")
                logger.info("✅ Clicked Supreme Court")
                
                # Look for 2008
                logger.info("🔍 Step 2: Looking for 2008")
                year_elements = await page.query_selector_all('text="2008"')
                logger.info(f"Found {len(year_elements)} elements with '2008' text")
                
                if year_elements:
                    await year_elements[0].click()
                    await asyncio.sleep(3)
                    await navigator._take_screenshot(page, "03_after_2008_click")
                    logger.info("✅ Clicked 2008")
                    
                    # Look for November
                    logger.info("🔍 Step 3: Looking for November")
                    month_elements = await page.query_selector_all('text="November"')
                    logger.info(f"Found {len(month_elements)} elements with 'November' text")
                    
                    if month_elements:
                        await month_elements[0].click()
                        await asyncio.sleep(3)
                        await navigator._take_screenshot(page, "04_after_november_click")
                        logger.info("✅ Clicked November")
                        
                        # Look for Day_11 and examine what days are available
                        logger.info("🔍 Step 4: Looking for Day_11")
                        day_elements = await page.query_selector_all('text="Day_11"')
                        logger.info(f"Found {len(day_elements)} elements with 'Day_11' text")

                        # If Day_11 not found, let's see what days are available
                        if not day_elements:
                            logger.info("🔍 Day_11 not found, examining available days...")

                            # Look for any elements containing "Day"
                            day_like_elements = await page.query_selector_all('*')
                            day_texts = []
                            for elem in day_like_elements:
                                try:
                                    text = await elem.text_content()
                                    if text and "Day" in text:
                                        day_texts.append(text.strip())
                                except:
                                    continue

                            unique_day_texts = list(set(day_texts))
                            logger.info(f"📅 Found {len(unique_day_texts)} elements containing 'Day': {unique_day_texts[:10]}")

                            # Also look for elements containing "11"
                            eleven_elements = await page.query_selector_all('text="11"')
                            logger.info(f"🔢 Found {len(eleven_elements)} elements with '11' text")

                            # Try to find any clickable day elements
                            all_links = await page.query_selector_all('a')
                            day_links = []
                            for link in all_links:
                                try:
                                    text = await link.text_content()
                                    if text and ("Day" in text or "11" in text):
                                        href = await link.get_attribute("href")
                                        day_links.append(f"text='{text}', href='{href}'")
                                except:
                                    continue

                            logger.info(f"🔗 Day-related links: {day_links}")

                            # Try clicking on "11" if it exists
                            if eleven_elements:
                                logger.info("🔍 Trying to click on '11'")
                                await eleven_elements[0].click()
                                await asyncio.sleep(5)
                                await navigator._take_screenshot(page, "05_after_11_click")
                                day_elements = [eleven_elements[0]]  # Pretend we found Day_11

                        if day_elements:
                            await day_elements[0].click()
                            await asyncio.sleep(5)  # Wait longer for content to load
                            await navigator._take_screenshot(page, "05_after_day_11_click")
                            logger.info("✅ Clicked Day_11")
                            
                            # Now examine the page content in detail
                            logger.info("🔍 Step 5: Examining page content after Day_11 click")
                            
                            # Check page title and URL
                            title = await page.title()
                            url = page.url
                            logger.info(f"📄 Page title: {title}")
                            logger.info(f"🔗 Page URL: {url}")
                            
                            # Look for main content area
                            main_content = await page.query_selector('div.col-md-8')
                            if main_content:
                                main_html = await main_content.inner_html()
                                logger.info(f"📝 Main content HTML length: {len(main_html)}")
                                logger.info(f"📝 Main content HTML (first 500 chars): {main_html[:500]}")
                            else:
                                logger.warning("❌ No div.col-md-8 found")
                            
                            # Look for any text containing case names
                            page_text = await page.evaluate('() => document.body.textContent')
                            if "KK Security" in page_text:
                                logger.info("✅ Found 'KK Security' in page text!")
                            elif "Harerimana" in page_text:
                                logger.info("✅ Found 'Harerimana' in page text!")
                            else:
                                logger.warning("❌ Neither 'KK Security' nor 'Harerimana' found in page text")
                            
                            # Look for all links on the page
                            all_links = await page.query_selector_all('a')
                            logger.info(f"🔗 Total links found: {len(all_links)}")
                            
                            # Log first 10 links
                            for i, link in enumerate(all_links[:10]):
                                try:
                                    href = await link.get_attribute("href")
                                    text = await link.text_content()
                                    logger.info(f"🔗 Link {i+1}: href='{href}', text='{text}'")
                                except Exception as e:
                                    logger.debug(f"Error getting link {i+1}: {e}")
                            
                            # Wait for user to examine the browser
                            logger.info("🔍 Browser is open - examine the page manually")
                            logger.info("Press Enter to continue...")
                            input()
                            
                        else:
                            logger.error("❌ Day_11 not found")
                    else:
                        logger.error("❌ November not found")
                else:
                    logger.error("❌ 2008 not found")
            else:
                logger.error("❌ Supreme Court not found")
                
        except Exception as e:
            logger.error(f"❌ Error during navigation: {e}")
            await navigator._take_screenshot(page, "error_state")
        
        finally:
            await page.close()


if __name__ == "__main__":
    asyncio.run(debug_day_page())
