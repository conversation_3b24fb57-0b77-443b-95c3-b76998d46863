#!/usr/bin/env python3
"""Validation script for GCS setup - runs the three test scenarios."""

import os
import subprocess
import sys
from pathlib import Path


def run_command(cmd, description, expect_success=True):
    """Run a command and report the result."""
    print(f"🔄 {description}")
    print(f"   Command: {cmd}")

    try:
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            text=True,
            timeout=300  # 5 minutes timeout
        )

        if result.returncode == 0 or not expect_success:
            print("✅ Success")
            if result.stdout.strip():
                print(f"   Output: {result.stdout.strip()[:200]}...")
            return True
        else:
            print(f"❌ Failed (exit code: {result.returncode})")
            if result.stderr.strip():
                print(f"   Error: {result.stderr.strip()[:200]}...")
            return False

    except subprocess.TimeoutExpired:
        print("⏰ Timeout after 5 minutes")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def check_environment():
    """Check that required environment variables are set."""
    print("🌍 Checking environment setup...")

    creds = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
    if creds and Path(creds).exists():
        print(f"✅ GOOGLE_APPLICATION_CREDENTIALS: {creds}")
        return True
    else:
        print("❌ GOOGLE_APPLICATION_CREDENTIALS not set or file doesn't exist")
        print("   Run: export GOOGLE_APPLICATION_CREDENTIALS=~/keys/minijust-scraper.json")
        return False


def main():
    """Run validation tests."""
    print("🧪 GCS Integration Validation")
    print("=" * 40)

    # Check environment
    if not check_environment():
        print("\n❌ Environment not properly configured. Run setup_gcs.sh first.")
        return 1

    # Create output directories
    os.makedirs("./runs", exist_ok=True)

    print("\n📋 Test 1: Dry run validation (month-level discovery)")
    success1 = run_command(
        "GAZETTE_CRAWLER_DEPTH=2 python -m gazette_scraper fetch --since 2025 --dry-run --out ./runs/2025-08-06",
        "Testing month-level discovery without downloads"
    )

    print("\n📋 Test 2: Single file smoke test")
    success2 = run_command(
        "python -m gazette_scraper fetch --since 2025 --max 1 --out ./",
        "Testing single file download and GCS upload"
    )

    if success2:
        print("   Check gs://rwandan_laws/gazette_pdfs/ for uploaded file")

    print("\n📋 Test 3: Configuration validation")
    success3 = run_command(
        "python test_gcs_setup.py",
        "Validating configuration"
    )

    # Summary
    print("\n" + "=" * 40)
    print("📊 Test Results:")
    print(f"   Dry run discovery: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"   Single file upload: {'✅ PASS' if success2 else '❌ FAIL'}")
    print(f"   Configuration: {'✅ PASS' if success3 else '❌ FAIL'}")

    if all([success1, success2, success3]):
        print("\n🎉 All tests passed! GCS integration is ready.")
        print("\n🚀 Ready for full historical pull:")
        print("   GAZETTE_CRAWLER_DEPTH=2 python -m gazette_scraper fetch --since 2004 --out ./state-prod")
        return 0
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
