#!/usr/bin/env python3
"""
Test script for language detection functionality.
"""

import asyncio
import logging
from pathlib import Path

from gazette_scraper.caselaw.language_detector import CaseLanguageDetector
from gazette_scraper.caselaw.models import CaseLanguage

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_language_detection():
    """Test language detection with sample case titles."""
    
    test_cases = [
        # English cases
        "RWANDA REVENUE AUTHORITY (RRA) v SOCIÉTÉ RWANDAISE",
        "MUGWANEZA v. BANK OF KIGALI LTD",
        "NSENGIYUMVA v HAYTON LTD", 
        "RUTAYISIRE v MUKANKURIKIYIMANA ET. AL",
        "Re: FAST TRUCK INTERIOR AND HARDWARE LTD",
        "SONARWA GENERAL INSURANCE COMPANY Ltd v UWIZEYIMANA",
        "IKIGO CYIMISORO N'AMAHORO v. SUGIRA LTD",
        
        # Potential Kinyarwanda cases
        "NIYONAGIZE v. DUHUZUMURIMO WA KAROTI",
        "NIYONAGIZE v KOPERATIVE DUHUZUMURIMOWA KAROTI",
        "MHAYIMANA v URUGAGA RW'ABAVOKA",
        "MHAYIMANA v RWANDA BAR ASSOCIATION",
        
        # Mixed/unclear cases
        "RUKUNDO v URUGAGA RW'ABAVOKA MU RWANDA",
        "NAYITURIKI v HAYTON Ltd",
        "Re KAMANZI (INCONST)",
    ]
    
    print("🔍 **LANGUAGE DETECTION TEST RESULTS**\n")
    print("| Case Title | Detected Language | Confidence | Language Suffix |")
    print("|------------|-------------------|------------|-----------------|")
    
    for case_title in test_cases:
        language, confidence = CaseLanguageDetector.detect_language_comprehensive(case_title)
        suffix = CaseLanguageDetector.generate_language_suffix(language)
        
        print(f"| {case_title[:40]:<40} | {language.value:<17} | {confidence:<10.2f} | {suffix:<15} |")
    
    print("\n" + "="*80)
    
    # Test duplicate detection logic
    print("\n🔄 **DUPLICATE DETECTION TEST**\n")
    
    duplicate_test_cases = [
        ("MUGWANEZA v. BANK OF KIGALI LTD", CaseLanguage.ENGLISH, 
         "MUGWANEZA v. BANK OF KIGALI LTD", CaseLanguage.KINYARWANDA),
        ("RUTAYISIRE v MUKANKURIKIYIMANA", CaseLanguage.ENGLISH,
         "RUTAYISIRE v MUKANKURIKIYIMANA", CaseLanguage.ENGLISH),
        ("NIYONAGIZE v. DUHUZUMURIMO", CaseLanguage.KINYARWANDA,
         "NIYONAGIZE v. DUHUZUMURIMO", CaseLanguage.ENGLISH),
    ]
    
    for case1_title, case1_lang, case2_title, case2_lang in duplicate_test_cases:
        should_separate = CaseLanguageDetector.should_treat_as_separate_documents(
            case1_title, case1_lang, case2_title, case2_lang
        )
        
        result = "SEPARATE" if should_separate else "DUPLICATE"
        print(f"Case 1: {case1_title} ({case1_lang.value})")
        print(f"Case 2: {case2_title} ({case2_lang.value})")
        print(f"Result: {result}\n")

def test_filename_generation():
    """Test filename generation with language suffixes."""
    
    print("📁 **FILENAME GENERATION TEST**\n")
    
    from gazette_scraper.caselaw.models import CourtNode, NodeType
    
    # Create a sample day node
    day_node = CourtNode(
        court_name="Supreme Court (911)",
        year=2023,
        month=2,
        day=17,
        node_type=NodeType.DAY,
        full_path="Supreme Court (911)/2023/February/Day_17",
        document_count=2
    )
    
    test_cases = [
        ("MUGWANEZA v. BANK OF KIGALI LTD", CaseLanguage.ENGLISH),
        ("MUGWANEZA v. BANK OF KIGALI LTD", CaseLanguage.KINYARWANDA),
        ("NIYONAGIZE v. DUHUZUMURIMO WA KAROTI", CaseLanguage.KINYARWANDA),
        ("NIYONAGIZE v. DUHUZUMURIMO WA KAROTI", CaseLanguage.ENGLISH),
    ]
    
    print("| Case Title | Language | Generated Filename |")
    print("|------------|----------|-------------------|")
    
    for case_title, language in test_cases:
        # Simulate filename generation
        import re
        clean_title = re.sub(r'[^\w\s-]', '', case_title)
        clean_title = re.sub(r'\s+', '_', clean_title)[:50]
        
        language_suffix = CaseLanguageDetector.generate_language_suffix(language)
        base_filename = f"{day_node.court_name}_{day_node.year}_{day_node.month:02d}_{day_node.day:02d}_{clean_title}"
        filename = f"{base_filename}{language_suffix}.pdf"
        
        print(f"| {case_title[:30]:<30} | {language.value:<8} | {filename} |")

def test_unique_id_generation():
    """Test unique ID generation for state management."""
    
    print("\n🆔 **UNIQUE ID GENERATION TEST**\n")
    
    test_cases = [
        ("Supreme Court (911)/2023/February/Day_17", "MUGWANEZA v. BANK OF KIGALI LTD", CaseLanguage.ENGLISH),
        ("Supreme Court (911)/2023/February/Day_17", "MUGWANEZA v. BANK OF KIGALI LTD", CaseLanguage.KINYARWANDA),
        ("Supreme Court (911)/2023/February/Day_17", "NIYONAGIZE v. DUHUZUMURIMO", CaseLanguage.KINYARWANDA),
        ("Supreme Court (911)/2023/February/Day_17", "NIYONAGIZE v. DUHUZUMURIMO", CaseLanguage.ENGLISH),
    ]
    
    print("| Navigation Path | Case Title | Language | Unique ID |")
    print("|-----------------|------------|----------|-----------|")
    
    for nav_path, case_title, language in test_cases:
        # Simulate unique ID generation
        language_suffix = language.value if hasattr(language, 'value') else str(language)
        unique_id = f"{nav_path}_{case_title}_{language_suffix}"
        
        print(f"| {nav_path[:20]:<20} | {case_title[:20]:<20} | {language.value:<8} | {unique_id[:50]:<50} |")

if __name__ == "__main__":
    print("🧪 **LANGUAGE DETECTION & DUPLICATE HANDLING TEST SUITE**\n")
    print("="*80)
    
    test_language_detection()
    test_filename_generation() 
    test_unique_id_generation()
    
    print("\n" + "="*80)
    print("✅ **All tests completed!**")
    print("\nNext steps:")
    print("1. Run the updated scraper on Supreme Court 2023")
    print("2. Verify that all 23 cases are discovered (instead of 18)")
    print("3. Check that language versions have distinct filenames")
    print("4. Confirm GCS upload preserves language distinctions")
