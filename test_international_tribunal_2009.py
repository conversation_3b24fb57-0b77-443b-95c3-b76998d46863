#!/usr/bin/env python3
"""
Test script to explore International Tribunal for Rwanda 2009 and download all documents.
"""

import asyncio
import logging
from pathlib import Path
from gazette_scraper.caselaw.navigator import CaseLawNavigator
from gazette_scraper.caselaw.models import CourtNode, NodeType
from gazette_scraper.config import load_caselaw_config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_international_tribunal_2009():
    """Test International Tribunal for Rwanda 2009 - discover and download all documents."""
    
    logger.info("🚀 Testing International Tribunal for Rwanda 2009")
    logger.info("🎯 Goal: Discover all months, days, and download all documents")
    
    # Load configuration
    config = load_caselaw_config()
    
    # Initialize navigator
    navigator = CaseLawNavigator(config)
    
    try:
        await navigator.start()
        logger.info("✅ Browser initialized successfully")
        
        # Step 1: Navigate and find International Tribunal for Rwanda
        context = navigator.contexts[0]
        page = await context.new_page()
        
        url = f"{config.base_url}{config.caselaw_path}"
        logger.info(f"🌐 Navigating to: {url}")
        await page.goto(url, wait_until="networkidle", timeout=config.page_load_timeout)
        await asyncio.sleep(config.navigation_delay)
        
        # Find International Tribunal for Rwanda
        logger.info("🔍 Looking for International Tribunal for Rwanda...")
        court_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
        
        tribunal_element = None
        for element in court_elements:
            text = await element.text_content()
            if text and "International Tribunal for Rwanda" in text:
                logger.info(f"📋 Found: {text}")
                tribunal_element = element
                break
        
        if not tribunal_element:
            logger.error("❌ International Tribunal for Rwanda not found!")
            return
        
        # Expand International Tribunal for Rwanda
        logger.info("🔄 Expanding International Tribunal for Rwanda...")
        await tribunal_element.click()
        await asyncio.sleep(config.navigation_delay)
        
        # Find 2009
        logger.info("🔍 Looking for 2009...")
        year_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
        
        year_2009_element = None
        for element in year_elements:
            text = await element.text_content()
            if text and "2009" in text:
                logger.info(f"📅 Found: {text}")
                year_2009_element = element
                break
        
        if not year_2009_element:
            logger.error("❌ 2009 not found in International Tribunal for Rwanda!")
            return
        
        # Expand 2009
        logger.info("🔄 Expanding 2009...")
        await year_2009_element.click()
        await asyncio.sleep(config.navigation_delay)
        
        # Create year node for testing
        year_node = CourtNode(
            court_name="International Tribunal for Rwanda",
            year=2009,
            node_type=NodeType.YEAR,
            full_path="International Tribunal for Rwanda/2009",
            document_count=0  # Will be updated
        )
        
        # Step 2: Discover all months
        logger.info("📅 Discovering all months in 2009...")
        discovered_months = await navigator._discover_months(page, year_node)
        
        logger.info(f"📊 MONTHS DISCOVERED: {len(discovered_months)}")
        month_names = ["", "January", "February", "March", "April", "May", "June",
                      "July", "August", "September", "October", "November", "December"]
        
        total_days = 0
        total_documents = 0
        all_day_nodes = []
        
        for month in discovered_months:
            month_name = month_names[month.month] if month.month <= 12 else f"Month{month.month}"
            logger.info(f"  📆 {month_name} ({month.document_count} documents)")
            
            # Expand this month to discover days
            logger.info(f"🔄 Expanding {month_name}...")
            
            # Find and click the month element
            month_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
            for element in month_elements:
                text = await element.text_content()
                if text and month_name.lower() in text.lower() and "(" in text:
                    await element.click()
                    await asyncio.sleep(config.navigation_delay)
                    break
            
            # Discover days for this month
            discovered_days = await navigator._discover_days(page, month)
            logger.info(f"    📄 Days in {month_name}: {len(discovered_days)}")
            
            for day in discovered_days:
                day_num = day.day
                logger.info(f"      Day {day_num}: {day.document_count} documents")
                total_documents += day.document_count
                all_day_nodes.append(day)
            
            total_days += len(discovered_days)
        
        # Step 3: Summary
        logger.info("\n" + "="*60)
        logger.info("📊 INTERNATIONAL TRIBUNAL FOR RWANDA 2009 SUMMARY")
        logger.info("="*60)
        logger.info(f"🗓️  Total Months: {len(discovered_months)}")
        logger.info(f"📅 Total Days: {total_days}")
        logger.info(f"📄 Total Documents: {total_documents}")
        
        # List all months with their days
        for month in discovered_months:
            month_name = month_names[month.month] if month.month <= 12 else f"Month{month.month}"
            month_days = [day for day in all_day_nodes if day.month == month.month]
            day_numbers = [day.day for day in month_days]
            day_numbers.sort()
            logger.info(f"📆 {month_name}: {len(month_days)} days - Days: {day_numbers}")
        
        # Step 4: Download all documents
        if total_documents > 0:
            logger.info(f"\n🚀 Starting download of {total_documents} documents...")
            
            # Create output directory
            output_dir = Path("./international_tribunal_2009_downloads")
            output_dir.mkdir(exist_ok=True)
            
            downloaded_count = 0
            
            for day_node in all_day_nodes:
                try:
                    logger.info(f"📥 Processing {day_node.full_path}...")
                    
                    # Discover cases for this day
                    cases = await navigator.discover_cases_for_day(day_node)
                    logger.info(f"  Found {len(cases)} cases")
                    
                    for case in cases:
                        try:
                            # Create filename
                            safe_filename = f"{case.court_name}_{case.year}_{case.month:02d}_{case.day:02d}_{case.title}.pdf"
                            safe_filename = "".join(c for c in safe_filename if c.isalnum() or c in "._- ")
                            file_path = output_dir / safe_filename
                            
                            # Download the PDF (this would need actual download implementation)
                            logger.info(f"    📄 Would download: {case.title}")
                            logger.info(f"    💾 To: {file_path}")
                            
                            downloaded_count += 1
                            
                        except Exception as e:
                            logger.error(f"    ❌ Error downloading case {case.title}: {e}")
                
                except Exception as e:
                    logger.error(f"❌ Error processing {day_node.full_path}: {e}")
            
            logger.info(f"✅ Download simulation completed: {downloaded_count} documents processed")
        else:
            logger.info("ℹ️  No documents found to download")
        
    except Exception as e:
        logger.error(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await navigator.close()
        logger.info("🧹 Cleanup completed")

if __name__ == "__main__":
    asyncio.run(test_international_tribunal_2009())
