#!/usr/bin/env python3
"""Test script for the improved gazette extractor."""

import sys
import logging
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

# Enable info logging
logging.basicConfig(level=logging.INFO)

from gazette_scraper.extractor.gemini_client import GeminiClient


def test_extractor():
    """Test the improved extractor with JSON repair."""
    print("🧪 Testing Improved Gazette Extractor with JSON Repair")
    print("=" * 60)
    
    # Find a test PDF
    data_dir = Path("data")
    pdf_files = list(data_dir.rglob("*.pdf"))
    
    if not pdf_files:
        print("❌ No PDF files found in data directory")
        return
    
    # Use the smallest PDF for testing
    test_pdf = min(pdf_files, key=lambda p: p.stat().st_size)
    print(f"📄 Testing with: {test_pdf.name}")
    print(f"📊 File size: {test_pdf.stat().st_size / 1024:.1f} KB")
    
    try:
        # Initialize client
        print("\n🔧 Initializing Gemini client...")
        client = GeminiClient()
        
        # Test extraction with small batch size and individual page fallback
        print("🚀 Starting extraction with JSON repair...")
        response = client.extract_pages(
            pdf_path=test_pdf,
            source_filename=test_pdf.name,
            batch_size=1  # Process one page at a time for better reliability
        )
        
        print(f"\n✅ SUCCESS! Extracted content from {len(response.pages)} pages")
        
        # Show results summary
        total_blocks = sum(len(page.blocks) for page in response.pages)
        total_errors = sum(len(page.errors or []) for page in response.pages)
        
        print(f"📊 Results Summary:")
        print(f"   • Total pages: {len(response.pages)}")
        print(f"   • Total content blocks: {total_blocks}")
        print(f"   • Total errors: {total_errors}")
        
        # Show details for each page
        for i, page in enumerate(response.pages):
            print(f"\n📄 Page {i + 1}:")
            print(f"   • Blocks: {len(page.blocks)}")
            if page.errors:
                print(f"   • Errors: {page.errors}")
            
            # Show first few blocks as sample
            for j, block in enumerate(page.blocks[:3]):
                print(f"   • Block {j + 1}: {block.section} - {block.text[:100]}...")
        
        # Save results
        output_dir = Path("test_extraction_repaired")
        output_dir.mkdir(exist_ok=True)
        
        # Save JSON
        json_file = output_dir / "extracted_content.json"
        with open(json_file, "w", encoding="utf-8") as f:
            f.write(response.model_dump_json(indent=2))
        
        print(f"\n💾 Results saved to: {json_file}")
        print("🎉 Test completed successfully!")
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_extractor()
