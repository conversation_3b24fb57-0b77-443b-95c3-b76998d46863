#!/usr/bin/env python3
"""Debug script to inspect the actual website structure."""

import asyncio
from playwright.async_api import async_playwright

async def debug_website():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()
        
        print("🌐 Navigating to the case-law page...")
        await page.goto("https://amategeko.gov.rw/laws/judgement/2", wait_until="networkidle")
        
        # Wait for dynamic content to load
        print("⏳ Waiting for dynamic content...")
        await asyncio.sleep(5)
        
        # Take a screenshot
        await page.screenshot(path="debug_page_structure.png", full_page=True)
        print("📸 Screenshot saved as debug_page_structure.png")
        
        # Get page title
        title = await page.title()
        print(f"📄 Page title: {title}")
        
        # Check for various tree-like structures
        selectors_to_check = [
            # Original selectors from navigator
            '.tree-node[data-level="0"]',
            '.court-node',
            'li:has(.expand-icon)',
            '.tree-item:first-child',
            
            # Common tree structures
            '.tree',
            '.tree-view',
            '.hierarchy',
            '.court-tree',
            '.navigation-tree',
            
            # Generic tree patterns
            'ul li',
            '.expandable',
            '[data-toggle="collapse"]',
            '.collapsible',
            
            # Look for any clickable items
            'a[href*="judgement"]',
            'button',
            '.btn',
            '.clickable',
            
            # Look for court names
            '*:contains("Court")',
            '*:contains("Supreme")',
            '*:contains("High")',
            '*:contains("Primary")',
        ]
        
        print("\n🔍 Checking for various selectors...")
        for selector in selectors_to_check:
            try:
                elements = await page.query_selector_all(selector)
                if elements:
                    print(f"✅ Found {len(elements)} elements with selector: {selector}")
                    # Get text content of first few elements
                    for i, element in enumerate(elements[:3]):
                        try:
                            text = await element.text_content()
                            if text and text.strip():
                                print(f"   [{i}]: {text.strip()[:100]}")
                        except:
                            pass
                else:
                    print(f"❌ No elements found for: {selector}")
            except Exception as e:
                print(f"⚠️  Error with selector {selector}: {e}")
        
        # Get the full page HTML to analyze
        html_content = await page.content()
        with open("debug_page_content.html", "w", encoding="utf-8") as f:
            f.write(html_content)
        print("\n💾 Full page HTML saved as debug_page_content.html")
        
        # Look for any JavaScript errors
        print("\n🐛 Checking console logs...")
        
        # Wait a bit more to see if anything loads
        print("\n⏳ Waiting additional time for any delayed loading...")
        await asyncio.sleep(10)
        
        # Take another screenshot after waiting
        await page.screenshot(path="debug_page_structure_after_wait.png", full_page=True)
        print("📸 Second screenshot saved as debug_page_structure_after_wait.png")
        
        await browser.close()
        print("\n✅ Debug complete! Check the generated files.")

if __name__ == "__main__":
    asyncio.run(debug_website())
