# Google Cloud Platform Deployment Guide

## Rwanda Gazette Scraper - Cloud Run Deployment

This guide covers the complete deployment of the Rwanda Gazette Scraper to Google Cloud Platform using Cloud Run, with Artifact Registry, Secret Manager, and other GCP services.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Cloud Build   │────│   Artifact       │────│   Cloud Run     │
│   (CI/CD)       │    │   Registry       │    │   (Container)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                                               │
         │              ┌─────────────────┐              │
         └──────────────│  Secret Manager │──────────────┘
                        │  (Credentials)  │
                        └─────────────────┘
                                 │
         ┌─────────────────┐     │     ┌─────────────────┐
         │  Cloud Storage  │─────┼─────│   Vertex AI     │
         │  (PDF Files)    │     │     │  (Batch Jobs)   │
         └─────────────────┘     │     └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │    Supabase     │
                        │   (Database)    │
                        └─────────────────┘
```

## 📋 Prerequisites

1. **Google Cloud Account** with billing enabled
2. **gcloud CLI** installed and authenticated
3. **Docker** installed (for local testing)
4. **Supabase Account** with project set up
5. **Gemini API Key** from Google AI Studio

## 🚀 Quick Deployment

### Step 1: Set Project and Region
```bash
export GOOGLE_CLOUD_PROJECT="rwandan-law-bot-440710"
export REGION="us-central1"
gcloud config set project $GOOGLE_CLOUD_PROJECT
```

### Step 2: Run Deployment Script
```bash
./deploy-gcp-infrastructure.sh
```

This script will automatically:
- Enable required APIs
- Create Artifact Registry repository
- Set up GCS bucket with lifecycle policies
- Create service accounts with proper IAM roles
- Configure Secret Manager
- Deploy the application to Cloud Run

### Step 3: Add Secrets
After deployment, add your sensitive credentials:

```bash
# Add Supabase service role key
echo "your-supabase-service-role-key" | \
  gcloud secrets versions add supabase-service-key \
  --data-file=- --project=$GOOGLE_CLOUD_PROJECT

# Add Gemini API key
echo "your-gemini-api-key" | \
  gcloud secrets versions add gemini-api-key \
  --data-file=- --project=$GOOGLE_CLOUD_PROJECT
```

## 🔧 Manual Deployment Steps

If you prefer to deploy manually or need to customize the deployment:

### 1. Enable APIs
```bash
gcloud services enable \
  cloudbuild.googleapis.com \
  run.googleapis.com \
  artifactregistry.googleapis.com \
  storage-api.googleapis.com \
  secretmanager.googleapis.com \
  aiplatform.googleapis.com \
  vpcaccess.googleapis.com \
  compute.googleapis.com \
  iam.googleapis.com
```

### 2. Create Artifact Registry Repository
```bash
gcloud artifacts repositories create gazette-scraper \
  --repository-format=docker \
  --location=$REGION \
  --description="Container registry for Rwanda Gazette Scraper"

# Configure Docker authentication
gcloud auth configure-docker ${REGION}-docker.pkg.dev
```

### 3. Create GCS Bucket
```bash
gsutil mb -p $GOOGLE_CLOUD_PROJECT -c STANDARD -l $REGION gs://rwandan_laws
gsutil versioning set on gs://rwandan_laws

# Set lifecycle policy
cat > lifecycle.json << EOF
{
  "lifecycle": {
    "rule": [
      {
        "action": {"type": "Delete"},
        "condition": {"age": 365, "isLive": false}
      },
      {
        "action": {"type": "SetStorageClass", "storageClass": "NEARLINE"},
        "condition": {"age": 30}
      }
    ]
  }
}
EOF

gsutil lifecycle set lifecycle.json gs://rwandan_laws
rm lifecycle.json
```

### 4. Create Service Account
```bash
gcloud iam service-accounts create rwanda-gazette-scraper \
  --description="Service account for Rwanda Gazette Scraper" \
  --display-name="Rwanda Gazette Scraper Service Account"

# Grant necessary roles
SA_EMAIL="rwanda-gazette-scraper@${GOOGLE_CLOUD_PROJECT}.iam.gserviceaccount.com"

for role in \
  "roles/run.invoker" \
  "roles/storage.objectAdmin" \
  "roles/secretmanager.secretAccessor" \
  "roles/aiplatform.user" \
  "roles/logging.logWriter" \
  "roles/monitoring.metricWriter" \
  "roles/cloudtrace.agent"; do
  gcloud projects add-iam-policy-binding $GOOGLE_CLOUD_PROJECT \
    --member="serviceAccount:$SA_EMAIL" \
    --role="$role"
done
```

### 5. Create Secrets
```bash
# Create secrets
gcloud secrets create supabase-service-key \
  --description="Supabase service role key for Rwanda Gazette Scraper"

gcloud secrets create gemini-api-key \
  --description="Google Gemini API key for document processing"

# Grant access to service account
for secret in supabase-service-key gemini-api-key; do
  gcloud secrets add-iam-policy-binding $secret \
    --member="serviceAccount:$SA_EMAIL" \
    --role="roles/secretmanager.secretAccessor"
done
```

### 6. Build and Deploy
```bash
# Submit build to Cloud Build
gcloud builds submit . \
  --config=cloudbuild.yaml \
  --substitutions="_SERVICE_NAME=rwanda-gazette-scraper,_REGION=$REGION"
```

## 🔍 Monitoring and Operations

### Health Checks
The service provides three endpoints for monitoring:
- `GET /health` - Liveness probe
- `GET /ready` - Readiness probe  
- `GET /` - Service information

### View Logs
```bash
gcloud run services logs read rwanda-gazette-scraper \
  --region=$REGION --limit=100 --follow
```

### Monitor Service
```bash
# Get service details
gcloud run services describe rwanda-gazette-scraper --region=$REGION

# View service metrics in Cloud Console
gcloud run services list --filter="metadata.name=rwanda-gazette-scraper"
```

### Scale Service
```bash
# Update scaling settings
gcloud run services update rwanda-gazette-scraper \
  --region=$REGION \
  --min-instances=0 \
  --max-instances=20 \
  --concurrency=1
```

## 🔧 Configuration

### Environment Variables
The service uses these environment variables (set automatically):

| Variable | Description | Source |
|----------|-------------|--------|
| `GOOGLE_CLOUD_PROJECT` | GCP project ID | Cloud Run |
| `GCS_BUCKET` | Storage bucket name | Cloud Run |
| `VERTEX_AI_LOCATION` | Vertex AI region | Cloud Run |
| `SUPABASE_SERVICE_ROLE_KEY` | Database credentials | Secret Manager |
| `GEMINI_API_KEY` | AI service key | Secret Manager |

### Resource Configuration
Default resource allocation:
- **CPU**: 2 vCPUs
- **Memory**: 2Gi
- **Timeout**: 3600s (1 hour)
- **Concurrency**: 1 request per instance
- **Min Instances**: 0 (scales to zero)
- **Max Instances**: 10

## 💰 Cost Optimization

### Automatic Cost Savings
- **Scale to Zero**: Service scales down to 0 instances when idle
- **Storage Lifecycle**: Files automatically moved to cheaper storage after 30 days
- **Request-based Billing**: Only pay for actual requests and compute time

### Manual Optimizations
- Use preemptible instances for batch processing
- Configure alerts for unexpected cost spikes
- Regular cleanup of old logs and temporary files

## 🛠️ Usage Examples

### Test Deployment
```bash
# Get service URL
SERVICE_URL=$(gcloud run services describe rwanda-gazette-scraper \
  --region=$REGION --format="value(status.url)")

# Test health endpoint
curl $SERVICE_URL/health

# Test scraping (example)
curl -X POST $SERVICE_URL/batch \
  -H "Content-Type: application/json" \
  -d '{"action": "scrape", "year": 2024}'
```

### Batch Processing
```bash
# Submit a batch job via CLI
gazette-scraper batch create ./path/to/pdfs/*.pdf \
  --project-id=$GOOGLE_CLOUD_PROJECT \
  --gcs-bucket=rwandan_laws \
  --submit --wait

# Check job status
gazette-scraper batch status <job-id>
```

## 🚨 Troubleshooting

### Common Issues

#### Build Failures
```bash
# Check build logs
gcloud builds log <BUILD_ID>

# Common solutions:
# 1. Check .gcloudignore is properly configured
# 2. Ensure all dependencies in pyproject.toml
# 3. Verify Dockerfile syntax
```

#### Service Not Starting
```bash
# Check service logs
gcloud run services logs read rwanda-gazette-scraper --region=$REGION

# Common issues:
# 1. Missing environment variables
# 2. Secret access permissions
# 3. Port configuration (should be 8080)
```

#### Secret Access Errors
```bash
# Verify secret exists and has proper IAM
gcloud secrets describe supabase-service-key
gcloud secrets get-iam-policy supabase-service-key

# Fix permissions
gcloud secrets add-iam-policy-binding supabase-service-key \
  --member="serviceAccount:rwanda-gazette-scraper@${GOOGLE_CLOUD_PROJECT}.iam.gserviceaccount.com" \
  --role="roles/secretmanager.secretAccessor"
```

### Debug Commands
```bash
# Test local container
docker build -t gazette-scraper .
docker run -p 8080:8080 gazette-scraper

# View Cloud Run revisions
gcloud run revisions list --service=rwanda-gazette-scraper --region=$REGION

# Force new deployment
gcloud run deploy rwanda-gazette-scraper \
  --image=us-central1-docker.pkg.dev/$GOOGLE_CLOUD_PROJECT/gazette-scraper/rwanda-gazette-scraper:latest \
  --region=$REGION
```

## 📚 Additional Resources

- [Cloud Run Documentation](https://cloud.google.com/run/docs)
- [Artifact Registry Documentation](https://cloud.google.com/artifact-registry/docs)
- [Secret Manager Documentation](https://cloud.google.com/secret-manager/docs)
- [Vertex AI Documentation](https://cloud.google.com/vertex-ai/docs)

## 🔒 Security Best Practices

1. **Service Account**: Uses dedicated service account with minimal permissions
2. **Secrets**: All sensitive data stored in Secret Manager
3. **Network**: Private networking with VPC connector
4. **Container**: Runs as non-root user
5. **Images**: Multi-stage builds for minimal attack surface
6. **Monitoring**: Comprehensive logging and monitoring enabled

## 📞 Support

For deployment issues:
1. Check the troubleshooting section above
2. Review Cloud Build and Cloud Run logs
3. Verify all prerequisites are met
4. Ensure proper IAM permissions are configured

---

**Note**: This deployment guide assumes familiarity with Google Cloud Platform. For production deployments, consider additional security hardening and compliance requirements specific to your organization.