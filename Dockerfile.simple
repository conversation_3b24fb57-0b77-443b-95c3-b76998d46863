# Rwanda Case-Law Scraper - Simple Dockerfile
# Minimal version for testing

FROM python:3.12-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    # Basic utilities
    curl \
    wget \
    ca-certificates \
    # Build tools for Python packages with C extensions
    build-essential \
    g++ \
    gcc \
    # Playwright browser dependencies (minimal set)
    libnss3 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libgtk-3-0 \
    libgbm1 \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set working directory
WORKDIR /app

# Copy all files
COPY . .

# Install Poetry and dependencies
RUN pip install poetry==2.1.4 \
    && poetry config virtualenvs.create false \
    && poetry install --only main --no-interaction --no-ansi

# Install Playwright browsers
RUN python -m playwright install chromium

# Create necessary directories
RUN mkdir -p /app/caselaw_data /app/screenshots /app/logs

# Expose port
EXPOSE 8080

# Set environment variables
ENV PORT=8080 \
    CASELAW_CONFIG_PATH=/app/caselaw_hybrid_config.toml \
    CASELAW_HEADLESS=true \
    CASELAW_DEBUG_MODE=false

# Default command
CMD ["python", "-m", "gazette_scraper.caselaw.cli", "serve", "--host", "0.0.0.0", "--port", "8080"]
