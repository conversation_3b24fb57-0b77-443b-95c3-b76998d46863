#!/usr/bin/env python3
"""Debug the file listing parsing."""

import requests
from bs4 import BeautifulSoup

from gazette_scraper.models import GazetteFolder
from gazette_scraper.parser import GazetteParser


def debug_folder_parsing():
    """Debug parsing of a specific folder."""
    folder_url = "https://www.minijust.gov.rw/official-gazette?tx_filelist_filelist%5Baction%5D=list&tx_filelist_filelist%5Bcontroller%5D=File&tx_filelist_filelist%5Bpath%5D=%2Fuser_upload%2FMinijust%2FOfficial_gazettes_2%2F_2024_Igazeti_ya_Leta%2F&cHash=449cf66686fe85ce5797a041dfde7975"

    print(f"Fetching: {folder_url}")

    try:
        response = requests.get(folder_url, timeout=15)
        response.raise_for_status()

        print(f"Response length: {len(response.text)} chars")
        print(f"Status code: {response.status_code}")

        # Save full HTML for inspection
        with open("debug_folder.html", "w", encoding="utf-8") as f:
            f.write(response.text)
        print("Saved full HTML to debug_folder.html")

        # Look for common file listing patterns
        soup = BeautifulSoup(response.text, "html.parser")

        print("\n=== Looking for tables ===")
        tables = soup.find_all("table")
        print(f"Found {len(tables)} tables")

        for i, table in enumerate(tables):
            print(f"\nTable {i+1}:")
            rows = table.find_all("tr")
            print(f"  {len(rows)} rows")

            # Show first few rows
            for j, row in enumerate(rows[:5]):
                cells = row.find_all(["td", "th"])
                cell_texts = [cell.get_text(strip=True) for cell in cells]
                print(f"    Row {j+1}: {cell_texts}")

                # Look for links
                links = row.find_all("a", href=True)
                for link in links:
                    href = link.get("href")
                    text = link.get_text(strip=True)
                    if (
                        "dumpFile" in href
                        or ".pdf" in href.lower()
                        or ".pdf" in text.lower()
                    ):
                        print(f"      LINK: '{text}' -> {href}")

        print("\n=== Looking for all PDF/download links ===")
        all_links = soup.find_all("a", href=True)
        pdf_links = []
        download_links = []

        for link in all_links:
            href = link.get("href")
            text = link.get_text(strip=True)

            if ".pdf" in href.lower() or ".pdf" in text.lower():
                pdf_links.append((text, href))
            if "dumpFile" in href:
                download_links.append((text, href))

        print(f"Found {len(pdf_links)} PDF links:")
        for text, href in pdf_links[:10]:
            print(f"  '{text}' -> {href}")

        print(f"Found {len(download_links)} dumpFile links:")
        for text, href in download_links[:10]:
            print(f"  '{text}' -> {href}")

        # Test our parser
        print("\n=== Testing our parser ===")
        folder = GazetteFolder(
            year=2024,
            month=1,
            folder_path="_2024_Igazeti_ya_Leta",
            folder_url=folder_url,
        )

        parser = GazetteParser()
        files = parser.parse_file_listing(response.text, folder)
        print(f"Parser found {len(files)} files:")
        for file in files:
            print(f"  {file.filename} -> {file.download_url}")

        # Look for divs that might contain file listings
        print("\n=== Looking for file listing divs ===")
        file_divs = soup.find_all(
            "div",
            class_=lambda x: x
            and ("file" in x.lower() or "list" in x.lower() or "content" in x.lower()),
        )
        print(f"Found {len(file_divs)} potential file divs")

        for i, div in enumerate(file_divs[:3]):
            print(f"\nDiv {i+1} classes: {div.get('class')}")
            links_in_div = div.find_all("a", href=True)
            print(f"  {len(links_in_div)} links in this div")
            for link in links_in_div[:5]:
                href = link.get("href")
                text = link.get_text(strip=True)
                if "dumpFile" in href or ".pdf" in text.lower():
                    print(f"    RELEVANT: '{text}' -> {href}")

    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    debug_folder_parsing()
