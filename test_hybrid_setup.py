#!/usr/bin/env python3
"""Test script for hybrid deployment setup verification."""

import os
import sys
from pathlib import Path

def test_environment_variables():
    """Test that required environment variables are set."""
    print("🧪 Testing environment variables...")
    
    required_vars = [
        "SUPABASE_URL",
        "SUPABASE_SERVICE_ROLE_KEY", 
        "CASE_LAWS_GCS_BUCKET",
        "GOOGLE_PROJECT_ID"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
        else:
            print(f"   ✅ {var}: {os.getenv(var)[:20]}...")
    
    if missing_vars:
        print(f"   ❌ Missing variables: {', '.join(missing_vars)}")
        return False
    
    print("   ✅ All environment variables set")
    return True


def test_supabase_connection():
    """Test Supabase connection."""
    print("🧪 Testing Supabase connection...")
    
    try:
        from supabase import create_client
        
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        
        if not url or not key:
            print("   ❌ Supabase credentials not found")
            return False
        
        client = create_client(url, key)
        
        # Test connection with a simple query
        result = client.table("gazette_files").select("count").limit(1).execute()
        
        print(f"   ✅ Supabase connection successful")
        print(f"   📊 Database accessible")
        return True
        
    except ImportError:
        print("   ⚠️  Supabase client not installed (pip install supabase)")
        return False
    except Exception as e:
        print(f"   ❌ Supabase connection failed: {e}")
        return False


def test_gcs_setup():
    """Test Google Cloud Storage setup."""
    print("🧪 Testing Google Cloud Storage...")
    
    try:
        from google.cloud import storage
        
        project_id = os.getenv("GOOGLE_PROJECT_ID")
        bucket_name = os.getenv("CASE_LAWS_GCS_BUCKET")
        
        if not project_id or not bucket_name:
            print("   ❌ GCS configuration not found")
            return False
        
        # Try to create client
        client = storage.Client(project=project_id)
        
        # Check if bucket exists
        bucket = client.bucket(bucket_name)
        if bucket.exists():
            print(f"   ✅ GCS bucket '{bucket_name}' exists")
        else:
            print(f"   ⚠️  GCS bucket '{bucket_name}' does not exist (will be created during deployment)")
        
        print(f"   ✅ GCS client initialized successfully")
        return True
        
    except ImportError:
        print("   ⚠️  Google Cloud Storage client not installed (pip install google-cloud-storage)")
        return False
    except Exception as e:
        print(f"   ❌ GCS setup failed: {e}")
        return False


def test_configuration_files():
    """Test that configuration files exist."""
    print("🧪 Testing configuration files...")
    
    config_files = [
        "caselaw_hybrid_config.toml",
        "deploy-hybrid.sh",
        "Dockerfile.hybrid",
        ".env"
    ]
    
    missing_files = []
    for file_path in config_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"   ❌ {file_path}")
    
    if missing_files:
        print(f"   ❌ Missing files: {', '.join(missing_files)}")
        return False
    
    print("   ✅ All configuration files present")
    return True


def test_docker_setup():
    """Test Docker setup."""
    print("🧪 Testing Docker setup...")
    
    try:
        import subprocess
        
        # Check if Docker is installed
        result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   ✅ Docker installed: {result.stdout.strip()}")
        else:
            print("   ❌ Docker not found")
            return False
        
        # Check if Docker is running
        result = subprocess.run(["docker", "info"], capture_output=True, text=True)
        if result.returncode == 0:
            print("   ✅ Docker daemon running")
        else:
            print("   ❌ Docker daemon not running")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Docker test failed: {e}")
        return False


def test_gcloud_setup():
    """Test gcloud CLI setup."""
    print("🧪 Testing gcloud CLI setup...")
    
    try:
        import subprocess
        
        # Check if gcloud is installed
        result = subprocess.run(["gcloud", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("   ✅ gcloud CLI installed")
        else:
            print("   ❌ gcloud CLI not found")
            return False
        
        # Check if authenticated
        result = subprocess.run(["gcloud", "auth", "list", "--filter=status:ACTIVE"], capture_output=True, text=True)
        if "ACTIVE" in result.stdout:
            print("   ✅ gcloud authenticated")
        else:
            print("   ❌ gcloud not authenticated (run: gcloud auth login)")
            return False
        
        # Check project
        result = subprocess.run(["gcloud", "config", "get-value", "project"], capture_output=True, text=True)
        project = result.stdout.strip()
        expected_project = os.getenv("GOOGLE_PROJECT_ID")
        
        if project == expected_project:
            print(f"   ✅ Project set correctly: {project}")
        else:
            print(f"   ⚠️  Project mismatch. Current: {project}, Expected: {expected_project}")
            print(f"   💡 Run: gcloud config set project {expected_project}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ gcloud test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Rwanda Case-Law Scraper - Hybrid Deployment Test")
    print("=" * 55)
    
    tests = [
        test_environment_variables,
        test_configuration_files,
        test_docker_setup,
        test_gcloud_setup,
        test_supabase_connection,
        test_gcs_setup,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            print()
    
    print("=" * 55)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! Ready for deployment.")
        print("💡 Run: ./deploy-hybrid.sh")
        return 0
    else:
        print("❌ Some tests failed. Please fix issues before deployment.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
