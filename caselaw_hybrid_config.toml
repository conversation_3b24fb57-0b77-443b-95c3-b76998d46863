# Rwanda Case-Law Scraper - Hybrid Configuration
# Supabase for metadata + Google Cloud Storage for PDFs

# General output settings
output_dir = "./caselaw_data"

# Browser configuration optimized for Cloud Run
[caselaw]
# Browser timeouts (milliseconds) - optimized for cloud deployment
browser_timeout = 45000
page_load_timeout = 20000
element_timeout = 15000

# Navigation settings - conservative for reliability
navigation_delay = 3.0
max_retry_attempts = 5
retry_delay = 8.0
click_retry_attempts = 5

# Concurrency settings - optimized for Cloud Run resources
max_browser_contexts = 2
max_concurrent_downloads = 2

# Error handling - production settings
screenshot_on_error = true
debug_mode = false
headless = true

# File management
screenshots_dir = "./screenshots"
verify_pdf_content = true
min_pdf_size = 1024

# Resume and state management
resume_from_state = true
state_save_interval = 5

# Rate limiting - respectful to target site
request_delay = 2.0
burst_protection = true

# URLs
base_url = "https://amategeko.gov.rw"
caselaw_path = "/laws/judgement/2"

# Google Cloud Storage configuration (for PDFs)
[gcs]
bucket = "rwandan-caselaws"
project_id = "rwandan-law-bot-440710"
prefix = "case_law_pdfs/"

# Supabase configuration (for metadata)
[supabase]
url = "https://esjiwdjofswwwmghrlaa.supabase.co"
service_role_key = "${SUPABASE_SERVICE_ROLE_KEY}"

# Cloud Run specific settings
[cloud_run]
port = 8080
service_name = "rwanda-caselaw-scraper"
region = "us-central1"
memory = "4Gi"
cpu = "2"
timeout = 3600
concurrency = 1
min_instances = 0
max_instances = 3

# Monitoring and logging
[monitoring]
enable_metrics = true
log_level = "INFO"
health_check_path = "/health"
ready_check_path = "/ready"

# Security settings
[security]
enable_cors = false
require_auth = false
allowed_origins = []

# Performance optimization
[optimization]
enable_compression = true
cache_static_assets = true
enable_request_logging = true
