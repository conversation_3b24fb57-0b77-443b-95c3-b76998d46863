#!/usr/bin/env python3
"""Test the real minijust site structure for gazette files."""

from urllib.parse import urljoin

import requests
from bs4 import BeautifulSoup


def explore_gazette_page():
    """Explore the gazette page structure."""
    base_url = "https://www.minijust.gov.rw"
    gazette_url = urljoin(base_url, "/official-gazette")

    print(f"Exploring {gazette_url}...")

    try:
        response = requests.get(gazette_url, timeout=15)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, "html.parser")

        print(f"✓ Page loaded successfully ({len(response.text)} chars)")
        print(f"Title: {soup.title.get_text() if soup.title else 'No title'}")

        # Look for links that might lead to file listings
        links = soup.find_all("a", href=True)
        gazette_links = []

        for link in links:
            href = link.get("href", "")
            text = link.get_text(strip=True)

            # Look for links that might contain gazette files
            if any(
                keyword in href.lower()
                for keyword in ["gazette", "fileadmin", "upload", "pdf", "document"]
            ) or any(
                keyword in text.lower()
                for keyword in ["gazette", "official", "pdf", "2024", "2023"]
            ):
                gazette_links.append((text, href))

        print(f"\nFound {len(gazette_links)} potential gazette-related links:")
        for text, href in gazette_links[:10]:  # Show first 10
            full_url = urljoin(base_url, href)
            print(f"  '{text}' -> {full_url}")

        # Look for file listing tables or divs
        tables = soup.find_all("table")
        if tables:
            print(f"\nFound {len(tables)} tables on the page")
            for i, table in enumerate(tables[:3]):  # Check first 3 tables
                rows = table.find_all("tr")
                print(f"  Table {i+1}: {len(rows)} rows")

                # Look for PDF links in tables
                pdf_links = table.find_all(
                    "a", href=lambda x: x and ".pdf" in x.lower()
                )
                if pdf_links:
                    print(f"    Found {len(pdf_links)} PDF links")
                    for pdf in pdf_links[:3]:
                        print(f"      {pdf.get_text(strip=True)} -> {pdf.get('href')}")

        # Look for file list divs
        file_divs = soup.find_all(
            "div", class_=lambda x: x and ("file" in x.lower() or "list" in x.lower())
        )
        if file_divs:
            print(f"\nFound {len(file_divs)} potential file listing divs")

        # Look for pagination or year/month navigation
        nav_elements = soup.find_all(
            ["nav", "div"],
            class_=lambda x: x and ("pag" in x.lower() or "nav" in x.lower()),
        )
        if nav_elements:
            print(f"\nFound {len(nav_elements)} navigation elements")

        return response.text

    except Exception as e:
        print(f"✗ Error exploring gazette page: {e}")
        return None


def test_file_listing_urls():
    """Test common file listing URL patterns."""
    base_url = "https://www.minijust.gov.rw"

    test_urls = [
        "/fileadmin/user_upload/Official%20Gazette/",
        "/fileadmin/Documents/Official_Gazette/",
        "/uploads/gazette/",
        "/official-gazette/files/",
        "/documents/gazette/",
    ]

    print("\nTesting file listing URLs:")
    for path in test_urls:
        url = urljoin(base_url, path)
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"✓ {url} - accessible")

                # Check for file listing indicators
                content_lower = response.text.lower()
                indicators = ["tx_filelist", "dumpfile", ".pdf", "gazette"]
                found = [ind for ind in indicators if ind in content_lower]
                if found:
                    print(f"  Indicators: {found}")
            else:
                print(f"  {url} - HTTP {response.status_code}")
        except Exception as e:
            print(f"  {url} - Error: {e}")


if __name__ == "__main__":
    print("=== Rwanda Minijust Gazette Structure Analysis ===\n")

    html_content = explore_gazette_page()
    test_file_listing_urls()

    if html_content:
        print("\n=== First 2000 chars of gazette page ===")
        print(html_content[:2000])

    print("\n=== Analysis completed ===")
