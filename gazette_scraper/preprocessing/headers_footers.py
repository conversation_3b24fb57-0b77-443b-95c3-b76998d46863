"""
Header and footer detection and removal for PDF documents.
Reduces token count by 10-25% and improves article segmentation accuracy.
"""

import re
import logging
from collections import Counter
from dataclasses import dataclass
from pathlib import Path
from typing import List, Set, Tuple, Optional

import fitz  # PyMuPDF

logger = logging.getLogger(__name__)


@dataclass
class PageBands:
    """Defines the top and bottom bands of a page for header/footer detection."""
    top_y: float
    bottom_y: float


class HeaderFooterStripper:
    """Detects and removes repeated headers/footers from PDF documents."""
    
    def __init__(
        self,
        top_ratio: float = 0.10,
        bottom_ratio: float = 0.10,
        min_share: float = 0.30,
        jaccard_threshold: float = 0.8
    ):
        """Initialize the header/footer stripper.
        
        Args:
            top_ratio: Fraction of page height to consider as header band
            bottom_ratio: Fraction of page height to consider as footer band
            min_share: Minimum fraction of pages that must contain text for it to be considered repeated
            jaccard_threshold: Minimum Jaccard similarity for text to be considered identical
        """
        self.top_ratio = top_ratio
        self.bottom_ratio = bottom_ratio
        self.min_share = min_share
        self.jaccard_threshold = jaccard_threshold
    
    def _normalize_line(self, text: str) -> str:
        """Normalize a line of text for comparison.
        
        Args:
            text: Raw text line
            
        Returns:
            Normalized text with spaces collapsed, page numbers removed, etc.
        """
        # Normalize spaces
        text = re.sub(r"\s+", " ", text.strip())
        
        # Remove page numbers (various formats)
        text = re.sub(r"\bPage\s*\d+\b", " ", text, flags=re.I)
        text = re.sub(r"\b\d+\s*/\s*\d+\b", " ", text)  # "1/5" format
        text = re.sub(r"\burup\s*\d+\b", " ", text, flags=re.I)  # Kinyarwanda "urup"
        
        # Remove dates (various formats)
        text = re.sub(r"\b\d{1,2}/\d{1,2}/\d{2,4}\b", " ", text)
        text = re.sub(r"\b\d{1,2}\.\d{1,2}\.\d{2,4}\b", " ", text)
        text = re.sub(r"\b\d{4}-\d{1,2}-\d{1,2}\b", " ", text)
        
        # Remove common gazette numbers and dates
        text = re.sub(r"\bn°?\s*\d+\s*(bis)?\b", " ", text, flags=re.I)
        text = re.sub(r"\bof\s+\d{1,2}/\d{1,2}/\d{4}\b", " ", text, flags=re.I)
        text = re.sub(r"\byo\s+ku\s+wa\s+\d{1,2}/\d{1,2}/\d{4}\b", " ", text, flags=re.I)
        text = re.sub(r"\bdu\s+\d{1,2}/\d{1,2}/\d{4}\b", " ", text, flags=re.I)
        
        # Collapse spaces again and convert to lowercase
        text = re.sub(r"\s+", " ", text).strip().lower()
        
        return text
    
    def _get_page_bands(self, page_height: float) -> PageBands:
        """Calculate header and footer bands for a page.
        
        Args:
            page_height: Height of the page
            
        Returns:
            PageBands with top and bottom boundaries
        """
        return PageBands(
            top_y=page_height * self.top_ratio,
            bottom_y=page_height * (1 - self.bottom_ratio)
        )
    
    def _jaccard_similarity(self, text1: str, text2: str) -> float:
        """Calculate Jaccard similarity between two text strings.
        
        Args:
            text1: First text string
            text2: Second text string
            
        Returns:
            Jaccard similarity score (0.0 to 1.0)
        """
        if not text1 and not text2:
            return 1.0
        if not text1 or not text2:
            return 0.0
        
        # Split into words for comparison
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        if not union:
            return 1.0 if not intersection else 0.0
        
        return len(intersection) / len(union)
    
    def detect_repeated_banners(self, pdf_path: Path) -> Tuple[Set[str], Set[str]]:
        """Detect repeated header and footer text across pages.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Tuple of (repeated_headers, repeated_footers) as sets of normalized text
        """
        logger.info(f"Analyzing headers/footers in {pdf_path.name}")
        
        doc = fitz.open(pdf_path)
        header_candidates = []
        footer_candidates = []
        total_pages = len(doc)
        
        try:
            for page_num in range(total_pages):
                page = doc.load_page(page_num)
                page_height = page.rect.height
                bands = self._get_page_bands(page_height)
                
                # Extract text blocks with positions
                blocks = page.get_text("blocks")
                
                page_headers = []
                page_footers = []
                
                for block in blocks:
                    if len(block) < 5:  # Skip malformed blocks
                        continue
                    
                    x0, y0, x1, y1, text, *_ = block
                    
                    if not text.strip():
                        continue
                    
                    normalized = self._normalize_line(text)
                    if not normalized:
                        continue
                    
                    # Check if block is in header or footer band
                    if y1 <= bands.top_y:
                        page_headers.append(normalized)
                    elif y0 >= bands.bottom_y:
                        page_footers.append(normalized)
                
                header_candidates.append(page_headers)
                footer_candidates.append(page_footers)
        
        finally:
            doc.close()
        
        # Find repeated headers
        repeated_headers = self._find_repeated_text(header_candidates, total_pages)
        repeated_footers = self._find_repeated_text(footer_candidates, total_pages)
        
        logger.info(f"Found {len(repeated_headers)} repeated headers, {len(repeated_footers)} repeated footers")
        
        return repeated_headers, repeated_footers
    
    def _find_repeated_text(self, candidates_by_page: List[List[str]], total_pages: int) -> Set[str]:
        """Find text that appears repeatedly across pages.
        
        Args:
            candidates_by_page: List of text candidates for each page
            total_pages: Total number of pages
            
        Returns:
            Set of repeated text strings
        """
        # Count occurrences of each normalized text
        text_counts = Counter()
        
        for page_candidates in candidates_by_page:
            # Use set to avoid counting duplicates within the same page
            unique_candidates = set(page_candidates)
            for candidate in unique_candidates:
                text_counts[candidate] += 1
        
        # Find text that appears on enough pages
        min_pages = max(1, int(total_pages * self.min_share))
        repeated_texts = set()
        
        for text, count in text_counts.items():
            if count >= min_pages:
                repeated_texts.add(text)
        
        # Group similar texts using Jaccard similarity
        final_repeated = set()
        processed = set()
        
        for text in repeated_texts:
            if text in processed:
                continue
            
            # Find all similar texts
            similar_group = {text}
            for other_text in repeated_texts:
                if other_text != text and other_text not in processed:
                    if self._jaccard_similarity(text, other_text) >= self.jaccard_threshold:
                        similar_group.add(other_text)
            
            # Use the most common text from the group
            group_counts = {t: text_counts[t] for t in similar_group}
            representative = max(group_counts, key=group_counts.get)
            final_repeated.add(representative)
            
            processed.update(similar_group)
        
        return final_repeated
    
    def strip_headers_footers(self, pdf_path: Path) -> List[str]:
        """Remove headers and footers from PDF pages and return cleaned text.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            List of cleaned text strings, one per page
        """
        # First, detect repeated banners
        repeated_headers, repeated_footers = self.detect_repeated_banners(pdf_path)
        
        if not repeated_headers and not repeated_footers:
            logger.info("No repeated headers/footers detected")
            return self._extract_all_text(pdf_path)
        
        logger.info(f"Stripping {len(repeated_headers)} headers and {len(repeated_footers)} footers")
        
        doc = fitz.open(pdf_path)
        cleaned_pages = []
        
        try:
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                page_height = page.rect.height
                bands = self._get_page_bands(page_height)
                
                # Extract text blocks with positions
                blocks = page.get_text("blocks")
                kept_blocks = []
                
                for block in blocks:
                    if len(block) < 5:
                        continue
                    
                    x0, y0, x1, y1, text, *_ = block
                    
                    if not text.strip():
                        continue
                    
                    normalized = self._normalize_line(text)
                    
                    # Check if this block should be removed
                    should_remove = False
                    
                    # Check headers
                    if y1 <= bands.top_y:
                        for header in repeated_headers:
                            if self._jaccard_similarity(normalized, header) >= self.jaccard_threshold:
                                should_remove = True
                                break
                    
                    # Check footers
                    elif y0 >= bands.bottom_y:
                        for footer in repeated_footers:
                            if self._jaccard_similarity(normalized, footer) >= self.jaccard_threshold:
                                should_remove = True
                                break
                    
                    if not should_remove:
                        kept_blocks.append((y0, x0, text.strip()))
                
                # Sort blocks by reading order (top to bottom, left to right)
                kept_blocks.sort(key=lambda block: (block[0], block[1]))
                
                # Combine text
                page_text = "\n".join(block[2] for block in kept_blocks)
                cleaned_pages.append(page_text)
        
        finally:
            doc.close()
        
        # Calculate token savings
        original_pages = self._extract_all_text(pdf_path)
        original_tokens = sum(len(page.split()) for page in original_pages)
        cleaned_tokens = sum(len(page.split()) for page in cleaned_pages)
        
        if original_tokens > 0:
            savings = (original_tokens - cleaned_tokens) / original_tokens * 100
            logger.info(f"Token reduction: {savings:.1f}% ({original_tokens} → {cleaned_tokens} tokens)")
        
        return cleaned_pages
    
    def _extract_all_text(self, pdf_path: Path) -> List[str]:
        """Extract all text from PDF without any filtering.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            List of text strings, one per page
        """
        doc = fitz.open(pdf_path)
        pages = []
        
        try:
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()
                pages.append(text)
        finally:
            doc.close()
        
        return pages
