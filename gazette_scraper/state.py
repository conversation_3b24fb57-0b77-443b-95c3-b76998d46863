"""SQLite-based state management for idempotency."""

from __future__ import annotations

import hashlib
import logging
import sqlite3
from typing import TYPE_CHECKING, Any

if TYPE_CHECKING:
    from pathlib import Path

    from .models import DiscoveryType, GazetteFile, StatsDict

logger = logging.getLogger(__name__)


class ScrapingState:
    """Manages scraping state using SQLite for idempotency."""

    def __init__(self, db_path: Path):
        self.db_path = db_path
        self._init_db()

    def _init_db(self) -> None:
        """Initialize the SQLite database."""
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        with sqlite3.connect(self.db_path) as conn:
            # Main files table
            conn.execute(
                """
                CREATE TABLE IF NOT EXISTS downloaded_files (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    filename TEXT NOT NULL,
                    sha256 TEXT NOT NULL UNIQUE,
                    download_url TEXT NOT NULL,
                    listing_url TEXT NOT NULL,
                    local_path TEXT NOT NULL,
                    year INTEGER NOT NULL,
                    month INTEGER NOT NULL,
                    title TEXT,
                    size_bytes INTEGER,
                    size_str TEXT,
                    pub_date TEXT,
                    discovered_at TIMESTAMP NOT NULL,
                    downloaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    supabase_synced BOOLEAN DEFAULT 0,
                    UNIQUE(download_url)
                )
            """
            )

            # File discoveries (can have multiple discoveries of same file)
            conn.execute(
                """
                CREATE TABLE IF NOT EXISTS file_discoveries (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    source_url TEXT NOT NULL,
                    listing_url TEXT NOT NULL,
                    title TEXT NOT NULL,
                    filename TEXT NOT NULL,
                    year INTEGER NOT NULL,
                    month INTEGER NOT NULL,
                    discovered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    sha256 TEXT,
                    UNIQUE(source_url, listing_url)
                )
            """
            )

            # Failed downloads
            conn.execute(
                """
                CREATE TABLE IF NOT EXISTS failed_downloads (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    download_url TEXT NOT NULL,
                    listing_url TEXT NOT NULL,
                    error_message TEXT,
                    http_status INTEGER,
                    attempt_count INTEGER DEFAULT 1,
                    last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(download_url)
                )
            """
            )

            # Page discoveries for tracking pagination
            conn.execute(
                """
                CREATE TABLE IF NOT EXISTS page_discoveries (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT NOT NULL,
                    page_type TEXT NOT NULL,  -- 'year', 'month', 'file'
                    page_signature TEXT NOT NULL,
                    item_count INTEGER NOT NULL,
                    discovered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(url, page_signature)
                )
            """
            )

            # Robots.txt tracking
            conn.execute(
                """
                CREATE TABLE IF NOT EXISTS robots_checks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT NOT NULL,
                    content_sha256 TEXT NOT NULL,
                    allowed BOOLEAN NOT NULL,
                    checked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(url)
                )
            """
            )

            # Create indexes
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_sha256 ON downloaded_files(sha256)",
                "CREATE INDEX IF NOT EXISTS idx_download_url ON downloaded_files(download_url)",
                "CREATE INDEX IF NOT EXISTS idx_year_month ON downloaded_files(year, month)",
                "CREATE INDEX IF NOT EXISTS idx_discovery_source ON file_discoveries(source_url)",
                "CREATE INDEX IF NOT EXISTS idx_discovery_listing ON file_discoveries(listing_url)",
                "CREATE INDEX IF NOT EXISTS idx_page_type ON page_discoveries(page_type)",
                "CREATE INDEX IF NOT EXISTS idx_failed_url ON failed_downloads(download_url)",
            ]

            for index_sql in indexes:
                conn.execute(index_sql)

    def seen_file(self, source_url: str) -> bool:
        """Check if a file has been seen before (either downloaded or discovered)."""
        with sqlite3.connect(self.db_path) as conn:
            # Check if downloaded
            cursor = conn.execute(
                "SELECT 1 FROM downloaded_files WHERE download_url = ?", (source_url,)
            )
            if cursor.fetchone():
                return True

            # Check if discovered
            cursor = conn.execute(
                "SELECT 1 FROM file_discoveries WHERE source_url = ?", (source_url,)
            )
            return cursor.fetchone() is not None

    def is_already_downloaded(self, file: GazetteFile) -> bool:
        """Check if a file has already been downloaded."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT 1 FROM downloaded_files WHERE download_url = ?",
                (str(file.download_url),),
            )
            return cursor.fetchone() is not None

    def has_sha256(self, sha256: str) -> bool:
        """Check if a SHA256 hash already exists."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT 1 FROM downloaded_files WHERE sha256 = ?", (sha256,)
            )
            return cursor.fetchone() is not None

    def record_discovery(
        self, listing_url: str, kind: DiscoveryType, count_on_page: int
    ) -> None:
        """Record discovery of a page with items."""
        with sqlite3.connect(self.db_path) as conn:
            # We don't have page signature here, so we'll use URL + count as signature
            signature = f"{listing_url}:{count_on_page}"
            conn.execute(
                """
                INSERT OR IGNORE INTO page_discoveries
                (url, page_type, page_signature, item_count)
                VALUES (?, ?, ?, ?)
            """,
                (listing_url, kind, signature, count_on_page),
            )

    def record_file_discovery(self, file: GazetteFile) -> None:
        """Record discovery of a file."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                """
                INSERT OR REPLACE INTO file_discoveries
                (source_url, listing_url, title, filename, year, month, sha256)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    str(file.download_url),
                    str(file.listing_url),
                    file.title,
                    file.filename,
                    file.year,
                    file.month,
                    file.sha256,
                ),
            )

    def record_download(self, file: GazetteFile, local_path: Path, sha256: str) -> None:
        """Record a successful download."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                """
                INSERT OR REPLACE INTO downloaded_files
                (filename, sha256, download_url, listing_url, local_path, year, month,
                 title, size_bytes, size_str, pub_date, discovered_at, supabase_synced)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0)
            """,
                (
                    file.filename,
                    sha256,
                    str(file.download_url),
                    str(file.listing_url),
                    str(local_path),
                    file.year,
                    file.month,
                    file.title,
                    file.size_bytes,
                    file.size_str,
                    file.pub_date.isoformat() if file.pub_date else None,
                    file.discovered_at.isoformat(),
                ),
            )

            # Remove from failed downloads if it was there
            conn.execute(
                "DELETE FROM failed_downloads WHERE download_url = ?",
                (str(file.download_url),),
            )

    def record_failure(
        self, file: GazetteFile, error_message: str, http_status: int | None = None
    ) -> None:
        """Record a failed download attempt."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                """
                INSERT OR REPLACE INTO failed_downloads
                (download_url, listing_url, error_message, http_status, attempt_count, last_attempt)
                VALUES (
                    ?, ?, ?, ?,
                    COALESCE((SELECT attempt_count + 1 FROM failed_downloads WHERE download_url = ?), 1),
                    CURRENT_TIMESTAMP
                )
            """,
                (
                    str(file.download_url),
                    str(file.listing_url),
                    error_message,
                    http_status,
                    str(file.download_url),
                ),
            )

    def get_failed_attempts(self, file: GazetteFile) -> int:
        """Get the number of failed attempts for a file."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT attempt_count FROM failed_downloads WHERE download_url = ?",
                (str(file.download_url),),
            )
            result = cursor.fetchone()
            return result[0] if result else 0

    def record_robots_check(self, url: str, content: str, allowed: bool) -> None:
        """Record a robots.txt check result."""
        content_hash = hashlib.sha256(content.encode()).hexdigest()

        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                """
                INSERT OR REPLACE INTO robots_checks
                (url, content_sha256, allowed)
                VALUES (?, ?, ?)
            """,
                (url, content_hash, allowed),
            )

    def get_robots_check(self, url: str) -> bool | None:
        """Get the last robots.txt check result."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT allowed FROM robots_checks WHERE url = ? ORDER BY checked_at DESC LIMIT 1",
                (url,),
            )
            result = cursor.fetchone()
            return result[0] if result else None

    def get_downloaded_files(self) -> list[dict[str, Any]]:  # type: ignore[explicit-any]
        """Get all downloaded files."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute(
                """
                SELECT filename, year, month, size_str, downloaded_at, sha256
                FROM downloaded_files
                ORDER BY year DESC, month DESC, downloaded_at DESC
            """
            )
            return [dict(row) for row in cursor.fetchall()]

    def get_stats(self) -> StatsDict:
        """Get comprehensive scraping statistics."""
        with sqlite3.connect(self.db_path) as conn:
            stats: StatsDict = {
                "total_downloaded": 0,
                "total_discovered": 0,
                "total_failed": 0,
                "downloaded_by_year": {},
                "discovered_by_year": {},
                "downloaded_by_month": {},
                "discovered_by_month": {},
                "errors_by_status": {},
                "downloaded_last_24h": 0,
                "discovered_last_24h": 0,
            }

            # Basic counts
            cursor = conn.execute("SELECT COUNT(*) FROM downloaded_files")
            stats["total_downloaded"] = cursor.fetchone()[0]

            cursor = conn.execute("SELECT COUNT(*) FROM file_discoveries")
            stats["total_discovered"] = cursor.fetchone()[0]

            cursor = conn.execute("SELECT COUNT(*) FROM failed_downloads")
            stats["total_failed"] = cursor.fetchone()[0]

            # By year
            cursor = conn.execute(
                """
                SELECT year, COUNT(*) as count
                FROM downloaded_files
                GROUP BY year
                ORDER BY year DESC
            """
            )
            stats["downloaded_by_year"] = dict(cursor.fetchall())

            cursor = conn.execute(
                """
                SELECT year, COUNT(*) as count
                FROM file_discoveries
                GROUP BY year
                ORDER BY year DESC
            """
            )
            stats["discovered_by_year"] = dict(cursor.fetchall())

            # By month
            cursor = conn.execute(
                """
                SELECT printf('%04d-%02d', year, month) as month_key, COUNT(*) as count
                FROM downloaded_files
                GROUP BY year, month
                ORDER BY year DESC, month DESC
            """
            )
            stats["downloaded_by_month"] = dict(cursor.fetchall())

            cursor = conn.execute(
                """
                SELECT printf('%04d-%02d', year, month) as month_key, COUNT(*) as count
                FROM file_discoveries
                GROUP BY year, month
                ORDER BY year DESC, month DESC
            """
            )
            stats["discovered_by_month"] = dict(cursor.fetchall())

            # Error summary (handle missing column gracefully)
            try:
                cursor = conn.execute(
                    """
                    SELECT http_status, COUNT(*) as count
                    FROM failed_downloads
                    WHERE http_status IS NOT NULL
                    GROUP BY http_status
                """
                )
                stats["errors_by_status"] = dict(cursor.fetchall())
            except sqlite3.OperationalError:
                # Column doesn't exist in older schema
                stats["errors_by_status"] = {}

            # Recent activity
            cursor = conn.execute(
                """
                SELECT COUNT(*) FROM downloaded_files
                WHERE downloaded_at > datetime('now', '-1 day')
            """
            )
            stats["downloaded_last_24h"] = cursor.fetchone()[0]

            cursor = conn.execute(
                """
                SELECT COUNT(*) FROM file_discoveries
                WHERE discovered_at > datetime('now', '-1 day')
            """
            )
            stats["discovered_last_24h"] = cursor.fetchone()[0]

            return stats

    def mark_supabase_synced(self, sha256: str) -> None:
        """Mark a file as synced to Supabase."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                "UPDATE downloaded_files SET supabase_synced = 1 WHERE sha256 = ?",
                (sha256,)
            )


def calculate_sha256(file_path: Path) -> str:
    """Calculate SHA256 hash of a file."""
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            sha256_hash.update(chunk)
    return sha256_hash.hexdigest()
