"""Supabase client wrapper for gazette metadata persistence."""

from __future__ import annotations

import logging
from datetime import datetime
from typing import TYPE_CHECKING

from .config import load_config

if TYPE_CHECKING:
    from .models import GazetteFile

logger = logging.getLogger(__name__)

# Global client instance - lazy initialized
_client = None


def get_supabase_client() -> object | None:
    """Get or create the Supabase client singleton."""
    global _client

    if _client is None:
        config = load_config()

        if not config.supabase_url or not config.supabase_key:
            logger.warning("Supabase configuration missing - operations will be skipped")
            return None

        try:
            from supabase import create_client
            _client = create_client(config.supabase_url, config.supabase_key)
            logger.info("Supabase client initialized successfully")
        except ImportError:
            logger.error("supabase-py not installed - install with: pip install supabase")
            return None
        except Exception as e:
            logger.error(f"Failed to initialize Supabase client: {e}")
            return None

    return _client


def upsert_file(file: GazetteFile, gcs_path: str, sha256: str) -> bool:
    """
    Upsert a gazette file record to Supabase.

    Args:
        file: The GazetteFile object with metadata
        gcs_path: The GCS path where the file is stored
        sha256: SHA-256 hash of the file content

    Returns:
        True if successful, False otherwise
    """
    client = get_supabase_client()
    if client is None:
        return False

    try:
        # Prepare the data for Supabase
        record = {
            "sha256": sha256,
            "year": file.year,
            "month": file.month,
            "issue_title": file.title,
            "gcs_path": gcs_path,
            "size_bytes": file.size_bytes or 0,
            "discovered_at": file.discovered_at.isoformat() if file.discovered_at else None,
            "downloaded_at": datetime.utcnow().isoformat(),
            # Additional fields for completeness
            "filename": file.filename,
            "download_url": str(file.download_url),
            "listing_url": str(file.listing_url),
            "pub_date": file.pub_date.isoformat() if file.pub_date else None,
            # Document classification fields
            "document_type": file.document_type.value,
            "subject_category": file.subject_category.value,
            "keywords": file.keywords,
        }

        # Upsert to gazette_files table (using sha256 as primary key)
        response = (
            client.table("gazette_files")  # type: ignore[attr-defined]
            .upsert(record, on_conflict="sha256")
            .execute()
        )

        if response.data:
            logger.info(f"Successfully upserted file metadata for {file.filename}")
            return True
        else:
            logger.error(f"Failed to upsert file metadata: {response}")
            return False

    except Exception as e:
        logger.error(f"Error upserting file metadata for {file.filename}: {e}")
        return False


def log_error(listing_url: str, download_url: str, error_message: str, http_status: int | None = None) -> bool:
    """
    Log a download error to Supabase.

    Args:
        listing_url: The URL where the file was discovered
        download_url: The direct download URL that failed
        error_message: Description of the error
        http_status: HTTP status code if applicable

    Returns:
        True if successful, False otherwise
    """
    client = get_supabase_client()
    if client is None:
        return False

    try:
        record = {
            "listing_url": listing_url,
            "download_url": download_url,
            "error_message": error_message,
            "http_status": http_status,
            "occurred_at": datetime.utcnow().isoformat(),
        }

        response = client.table("download_errors").insert(record).execute()  # type: ignore[attr-defined]

        if response.data:
            logger.info(f"Logged download error for {download_url}")
            return True
        else:
            logger.error(f"Failed to log download error: {response}")
            return False

    except Exception as e:
        logger.error(f"Error logging download error: {e}")
        return False


def sync_unsynced_files(db_path: str) -> dict[str, int]:
    """
    Sync files from SQLite that haven't been synced to Supabase yet.

    Args:
        db_path: Path to the SQLite database

    Returns:
        Dictionary with sync statistics
    """
    import sqlite3
    from pathlib import Path

    stats = {"synced": 0, "failed": 0, "skipped": 0}

    client = get_supabase_client()
    if client is None:
        logger.error("Supabase client not available for sync")
        return stats

    db_file = Path(db_path)
    if not db_file.exists():
        logger.warning(f"Database file {db_path} does not exist")
        return stats

    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()

            # Check if supabase_synced column exists
            cursor.execute("PRAGMA table_info(downloaded_files)")
            columns = [column[1] for column in cursor.fetchall()]

            if "supabase_synced" not in columns:
                logger.warning("supabase_synced column not found in downloaded_files table")
                stats["skipped"] += 1
                return stats

            # Get unsynced files
            cursor.execute("""
                SELECT filename, sha256, download_url, listing_url, year, month,
                       title, size_bytes, pub_date, discovered_at, downloaded_at,
                       local_path
                FROM downloaded_files
                WHERE supabase_synced = 0 OR supabase_synced IS NULL
            """)

            unsynced_files = cursor.fetchall()
            logger.info(f"Found {len(unsynced_files)} files to sync to Supabase")

            for row in unsynced_files:
                try:
                    # Extract data from row
                    (filename, sha256, download_url, listing_url, year, month,
                     title, size_bytes, pub_date, discovered_at, downloaded_at,
                     local_path) = row

                    # Construct GCS path from local path structure
                    local_path_obj = Path(local_path)
                    gcs_path = f"gazette_pdfs/{year:04d}/{month:02d}/{local_path_obj.name}"

                    # Prepare record
                    record = {
                        "sha256": sha256,
                        "year": year,
                        "month": month,
                        "issue_title": title or filename,
                        "gcs_path": gcs_path,
                        "size_bytes": size_bytes or 0,
                        "discovered_at": discovered_at,
                        "downloaded_at": downloaded_at,
                        "filename": filename,
                        "download_url": download_url,
                        "listing_url": listing_url,
                        "pub_date": pub_date,
                    }

                    # Upsert to Supabase
                    response = (
                        client.table("gazette_files")  # type: ignore[attr-defined]
                        .upsert(record, on_conflict="sha256")
                        .execute()
                    )

                    if response.data:
                        # Mark as synced in SQLite
                        cursor.execute(
                            "UPDATE downloaded_files SET supabase_synced = 1 WHERE sha256 = ?",
                            (sha256,)
                        )
                        stats["synced"] += 1
                        logger.debug(f"Synced {filename} to Supabase")
                    else:
                        stats["failed"] += 1
                        logger.error(f"Failed to sync {filename}: {response}")

                except Exception as e:
                    stats["failed"] += 1
                    logger.error(f"Error syncing file {filename}: {e}")

            conn.commit()

    except Exception as e:
        logger.error(f"Error during Supabase sync: {e}")
        stats["failed"] += len(unsynced_files or [])

    return stats
