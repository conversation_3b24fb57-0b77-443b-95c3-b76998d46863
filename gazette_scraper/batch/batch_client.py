"""
Batch processing client for gazette extraction using Vertex AI Batch Prediction.
Provides 50% cost reduction for bulk processing.
"""

import json
import logging
import os
import time
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

from google.cloud import aiplatform
from google.cloud import storage
from google.cloud.aiplatform_v1 import JobServiceClient

from .batch_job import BatchJob, BatchRequest, BatchResult, BatchJobStatus, BatchJobManager
from ..preprocessing.headers_footers import HeaderFooterStripper

logger = logging.getLogger(__name__)


class BatchProcessingClient:
    """Client for batch processing gazette documents using Vertex AI."""
    
    def __init__(
        self,
        project_id: str,
        location: str = "us-central1",
        gcs_bucket: str = None,
        jobs_dir: str = "batch_jobs"
    ):
        """Initialize the batch processing client.
        
        Args:
            project_id: Google Cloud project ID
            location: Vertex AI location (default: us-central1)
            gcs_bucket: GCS bucket for batch input/output (default: {project_id}-gazette-batch)
            jobs_dir: Directory to store job metadata
        """
        self.project_id = project_id
        self.location = location
        self.gcs_bucket = gcs_bucket or f"{project_id}-gazette-batch"
        
        # Initialize clients
        self.job_client = JobServiceClient(
            client_options={"api_endpoint": f"{location}-aiplatform.googleapis.com"}
        )
        self.storage_client = storage.Client(project=project_id)
        
        # Initialize job manager
        self.job_manager = BatchJobManager(Path(jobs_dir))
        
        # Initialize header/footer stripper
        self.header_footer_stripper = HeaderFooterStripper()
        
        # Ensure GCS bucket exists
        self._ensure_bucket_exists()
        
        logger.info(f"Initialized batch processing client for project {project_id}")
    
    def _ensure_bucket_exists(self) -> None:
        """Ensure the GCS bucket exists."""
        try:
            bucket = self.storage_client.bucket(self.gcs_bucket)
            if not bucket.exists():
                bucket = self.storage_client.create_bucket(self.gcs_bucket, location=self.location)
                logger.info(f"Created GCS bucket: {self.gcs_bucket}")
            else:
                logger.info(f"Using existing GCS bucket: {self.gcs_bucket}")
        except Exception as e:
            logger.error(f"Failed to ensure bucket exists: {e}")
            raise
    
    def create_batch_job(
        self,
        pdf_paths: List[Path],
        display_name: str = None,
        doc_type_hint: str = "other"
    ) -> BatchJob:
        """Create a new batch job for processing multiple PDFs.
        
        Args:
            pdf_paths: List of PDF file paths to process
            display_name: Human-readable name for the job
            doc_type_hint: Hint about document type
            
        Returns:
            BatchJob instance
        """
        job_id = str(uuid.uuid4())
        if not display_name:
            display_name = f"Gazette Batch {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        
        # Create batch requests
        requests = []
        for i, pdf_path in enumerate(pdf_paths):
            request_id = f"{job_id}-{i:04d}"
            
            # Apply header/footer stripping
            try:
                cleaned_pages = self.header_footer_stripper.strip_headers_footers(pdf_path)
                pages_content = "\n".join([
                    f"=== PAGE {j + 1} ===\n{page}"
                    for j, page in enumerate(cleaned_pages)
                ])
            except Exception as e:
                logger.warning(f"Header/footer stripping failed for {pdf_path}: {e}")
                # Fallback to original text
                original_pages = self.header_footer_stripper._extract_all_text(pdf_path)
                pages_content = "\n".join([
                    f"=== PAGE {j + 1} ===\n{page}"
                    for j, page in enumerate(original_pages)
                ])
            
            request = BatchRequest(
                request_id=request_id,
                pdf_path=pdf_path,
                source_filename=pdf_path.name,
                doc_type_hint=doc_type_hint,
                pages_content=pages_content,
                metadata={
                    "file_size": pdf_path.stat().st_size,
                    "created_at": datetime.utcnow().isoformat()
                }
            )
            requests.append(request)
        
        # Create batch job
        job = BatchJob(
            job_id=job_id,
            display_name=display_name,
            requests=requests
        )
        
        # Save job
        self.job_manager.save_job(job)
        
        logger.info(f"Created batch job {job_id} with {len(requests)} requests")
        return job
    
    def _prepare_batch_input(self, job: BatchJob) -> str:
        """Prepare JSONL input file for batch processing.
        
        Args:
            job: Batch job to prepare
            
        Returns:
            GCS path to input file
        """
        # Create JSONL content
        jsonl_lines = []
        
        for request in job.requests:
            # Create Vertex AI batch request format
            batch_request = {
                "contents": [
                    {
                        "role": "user",
                        "parts": [
                            {
                                "text": f"""Extract structured content from this Rwanda legal document.

Source: {request.source_filename}
Document type hint: {request.doc_type_hint}
Languages: rw, en, fr

Content:
{request.pages_content}

Return structured JSON with document metadata, sections, and extracted content."""
                            }
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": 0,
                    "maxOutputTokens": 200000,
                    "responseMimeType": "application/json",
                    "responseSchema": {
                        "type": "OBJECT",
                        "required": ["schema_version", "doc_type", "sections"],
                        "properties": {
                            "schema_version": {"type": "STRING"},
                            "doc_type": {"type": "STRING"},
                            "metadata": {
                                "type": "OBJECT",
                                "properties": {
                                    "title": {"type": "STRING"},
                                    "date_iso": {"type": "STRING"},
                                    "source": {"type": "STRING"}
                                }
                            },
                            "sections": {
                                "type": "ARRAY",
                                "items": {
                                    "type": "OBJECT",
                                    "required": ["type"],
                                    "properties": {
                                        "type": {"type": "STRING"},
                                        "language": {"type": "STRING"},
                                        "text": {"type": "STRING"},
                                        "article_no": {"type": "INTEGER"},
                                        "page": {"type": "INTEGER"}
                                    }
                                }
                            }
                        }
                    }
                },
                "request_id": request.request_id
            }
            
            jsonl_lines.append(json.dumps(batch_request, ensure_ascii=False))
        
        # Upload to GCS
        input_path = f"batch_inputs/{job.job_id}/input.jsonl"
        gcs_path = f"gs://{self.gcs_bucket}/{input_path}"
        
        bucket = self.storage_client.bucket(self.gcs_bucket)
        blob = bucket.blob(input_path)
        blob.upload_from_string("\n".join(jsonl_lines), content_type="application/jsonl")
        
        logger.info(f"Uploaded batch input to {gcs_path}")
        return gcs_path
    
    def submit_batch_job(self, job: BatchJob) -> BatchJob:
        """Submit a batch job to Vertex AI.
        
        Args:
            job: Batch job to submit
            
        Returns:
            Updated batch job with Vertex AI job information
        """
        # Prepare input
        input_gcs_path = self._prepare_batch_input(job)
        output_gcs_path = f"gs://{self.gcs_bucket}/batch_outputs/{job.job_id}/"
        
        # Create Vertex AI batch prediction job
        parent = f"projects/{self.project_id}/locations/{self.location}"
        model_name = f"{parent}/publishers/google/models/gemini-1.5-flash-001"
        
        batch_prediction_job = {
            "display_name": f"Gazette Batch: {job.display_name}",
            "model": model_name,
            "input_config": {
                "instances_format": "jsonl",
                "gcs_source": {"uris": [input_gcs_path]}
            },
            "output_config": {
                "predictions_format": "jsonl",
                "gcs_destination": {"output_uri_prefix": output_gcs_path}
            }
        }
        
        # Submit job
        try:
            operation = self.job_client.create_batch_prediction_job(
                parent=parent,
                batch_prediction_job=batch_prediction_job
            )
            
            # Update job with Vertex AI information
            job.vertex_job_name = operation.name
            job.input_gcs_path = input_gcs_path
            job.output_gcs_path = output_gcs_path
            job.status = BatchJobStatus.RUNNING
            job.started_at = datetime.utcnow()
            
            # Save updated job
            self.job_manager.save_job(job)
            
            logger.info(f"Submitted batch job {job.job_id} to Vertex AI: {operation.name}")
            return job
            
        except Exception as e:
            job.status = BatchJobStatus.FAILED
            job.error_message = str(e)
            self.job_manager.save_job(job)
            logger.error(f"Failed to submit batch job {job.job_id}: {e}")
            raise

    def check_job_status(self, job: BatchJob) -> BatchJob:
        """Check the status of a batch job.

        Args:
            job: Batch job to check

        Returns:
            Updated batch job with current status
        """
        if not job.vertex_job_name:
            logger.warning(f"Job {job.job_id} has no Vertex AI job name")
            return job

        try:
            # Get job status from Vertex AI
            vertex_job = self.job_client.get_batch_prediction_job(name=job.vertex_job_name)

            # Map Vertex AI status to our status
            vertex_state = vertex_job.state.name
            if vertex_state == "JOB_STATE_PENDING":
                job.status = BatchJobStatus.PENDING
            elif vertex_state == "JOB_STATE_RUNNING":
                job.status = BatchJobStatus.RUNNING
            elif vertex_state == "JOB_STATE_SUCCEEDED":
                job.status = BatchJobStatus.COMPLETED
                job.completed_at = datetime.utcnow()
            elif vertex_state in ["JOB_STATE_FAILED", "JOB_STATE_CANCELLED"]:
                job.status = BatchJobStatus.FAILED
                job.completed_at = datetime.utcnow()
                job.error_message = vertex_job.error.message if vertex_job.error else "Job failed"

            # Save updated status
            self.job_manager.save_job(job)

            logger.info(f"Job {job.job_id} status: {job.status.value}")
            return job

        except Exception as e:
            logger.error(f"Failed to check job status for {job.job_id}: {e}")
            return job

    def retrieve_results(self, job: BatchJob) -> BatchJob:
        """Retrieve results from a completed batch job.

        Args:
            job: Completed batch job

        Returns:
            Updated batch job with results
        """
        if job.status != BatchJobStatus.COMPLETED:
            logger.warning(f"Job {job.job_id} is not completed, cannot retrieve results")
            return job

        if not job.output_gcs_path:
            logger.error(f"Job {job.job_id} has no output GCS path")
            return job

        try:
            # List output files
            bucket_name = job.output_gcs_path.replace("gs://", "").split("/")[0]
            prefix = "/".join(job.output_gcs_path.replace("gs://", "").split("/")[1:])

            bucket = self.storage_client.bucket(bucket_name)
            blobs = list(bucket.list_blobs(prefix=prefix))

            # Find prediction results file
            results_blob = None
            for blob in blobs:
                if blob.name.endswith("predictions.jsonl"):
                    results_blob = blob
                    break

            if not results_blob:
                logger.error(f"No results file found for job {job.job_id}")
                return job

            # Download and parse results
            results_content = results_blob.download_as_text()
            results = []

            for line in results_content.strip().split("\n"):
                if not line.strip():
                    continue

                try:
                    result_data = json.loads(line)
                    request_id = result_data.get("request_id", "unknown")

                    # Check if prediction was successful
                    if "prediction" in result_data:
                        results.append(BatchResult(
                            request_id=request_id,
                            success=True,
                            extracted_data=result_data["prediction"]
                        ))
                    else:
                        error_msg = result_data.get("error", {}).get("message", "Unknown error")
                        results.append(BatchResult(
                            request_id=request_id,
                            success=False,
                            error_message=error_msg
                        ))

                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse result line: {e}")
                    continue

            job.results = results
            self.job_manager.save_job(job)

            logger.info(f"Retrieved {len(results)} results for job {job.job_id}")
            return job

        except Exception as e:
            logger.error(f"Failed to retrieve results for job {job.job_id}: {e}")
            return job

    def wait_for_completion(
        self,
        job: BatchJob,
        max_wait_seconds: int = 3600,
        poll_interval: int = 30
    ) -> BatchJob:
        """Wait for a batch job to complete.

        Args:
            job: Batch job to wait for
            max_wait_seconds: Maximum time to wait (default: 1 hour)
            poll_interval: Polling interval in seconds (default: 30s)

        Returns:
            Updated batch job
        """
        start_time = time.time()

        while time.time() - start_time < max_wait_seconds:
            job = self.check_job_status(job)

            if job.is_complete:
                if job.status == BatchJobStatus.COMPLETED:
                    job = self.retrieve_results(job)
                break

            logger.info(f"Job {job.job_id} still {job.status.value}, waiting {poll_interval}s...")
            time.sleep(poll_interval)

        if not job.is_complete:
            logger.warning(f"Job {job.job_id} did not complete within {max_wait_seconds}s")

        return job

    def list_jobs(self) -> List[BatchJob]:
        """List all batch jobs."""
        return self.job_manager.list_jobs()

    def get_job(self, job_id: str) -> Optional[BatchJob]:
        """Get a specific batch job."""
        return self.job_manager.load_job(job_id)

    def delete_job(self, job_id: str) -> bool:
        """Delete a batch job and its associated files."""
        job = self.get_job(job_id)
        if job:
            # TODO: Clean up GCS files
            pass

        return self.job_manager.delete_job(job_id)
