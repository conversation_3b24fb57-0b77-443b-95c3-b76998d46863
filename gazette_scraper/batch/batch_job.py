"""
Batch job management for gazette extraction.
Handles job submission, status tracking, and result retrieval.
"""

import json
import logging
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)


class BatchJobStatus(Enum):
    """Status of a batch processing job."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class BatchRequest:
    """Individual request within a batch job."""
    request_id: str
    pdf_path: Path
    source_filename: str
    doc_type_hint: str
    pages_content: str
    metadata: Dict[str, Any]


@dataclass
class BatchResult:
    """Result from processing a single request in a batch."""
    request_id: str
    success: bool
    extracted_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    processing_time: Optional[float] = None


@dataclass
class BatchJob:
    """Represents a batch processing job for gazette extraction."""
    
    job_id: str
    display_name: str
    requests: List[BatchRequest]
    status: BatchJobStatus = BatchJobStatus.PENDING
    vertex_job_name: Optional[str] = None
    input_gcs_path: Optional[str] = None
    output_gcs_path: Optional[str] = None
    created_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    results: List[BatchResult] = None
    error_message: Optional[str] = None
    
    def __post_init__(self):
        """Initialize default values."""
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.results is None:
            self.results = []
    
    @property
    def total_requests(self) -> int:
        """Total number of requests in this batch."""
        return len(self.requests)
    
    @property
    def completed_requests(self) -> int:
        """Number of completed requests."""
        return len(self.results)
    
    @property
    def success_rate(self) -> float:
        """Success rate of completed requests."""
        if not self.results:
            return 0.0
        successful = sum(1 for result in self.results if result.success)
        return successful / len(self.results)
    
    @property
    def is_complete(self) -> bool:
        """Whether the job is complete (success or failure)."""
        return self.status in [BatchJobStatus.COMPLETED, BatchJobStatus.FAILED, BatchJobStatus.CANCELLED]
    
    @property
    def duration(self) -> Optional[float]:
        """Job duration in seconds."""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "job_id": self.job_id,
            "display_name": self.display_name,
            "status": self.status.value,
            "vertex_job_name": self.vertex_job_name,
            "input_gcs_path": self.input_gcs_path,
            "output_gcs_path": self.output_gcs_path,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "total_requests": self.total_requests,
            "completed_requests": self.completed_requests,
            "success_rate": self.success_rate,
            "duration": self.duration,
            "error_message": self.error_message,
            "requests": [
                {
                    "request_id": req.request_id,
                    "pdf_path": str(req.pdf_path),
                    "source_filename": req.source_filename,
                    "doc_type_hint": req.doc_type_hint,
                    "metadata": req.metadata
                }
                for req in self.requests
            ],
            "results": [
                {
                    "request_id": result.request_id,
                    "success": result.success,
                    "error_message": result.error_message,
                    "processing_time": result.processing_time
                }
                for result in self.results
            ]
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "BatchJob":
        """Create from dictionary."""
        # Parse dates
        created_at = datetime.fromisoformat(data["created_at"]) if data.get("created_at") else None
        started_at = datetime.fromisoformat(data["started_at"]) if data.get("started_at") else None
        completed_at = datetime.fromisoformat(data["completed_at"]) if data.get("completed_at") else None
        
        # Parse requests
        requests = []
        for req_data in data.get("requests", []):
            requests.append(BatchRequest(
                request_id=req_data["request_id"],
                pdf_path=Path(req_data["pdf_path"]),
                source_filename=req_data["source_filename"],
                doc_type_hint=req_data["doc_type_hint"],
                pages_content="",  # Not stored in serialization
                metadata=req_data["metadata"]
            ))
        
        # Parse results
        results = []
        for result_data in data.get("results", []):
            results.append(BatchResult(
                request_id=result_data["request_id"],
                success=result_data["success"],
                error_message=result_data.get("error_message"),
                processing_time=result_data.get("processing_time")
            ))
        
        return cls(
            job_id=data["job_id"],
            display_name=data["display_name"],
            requests=requests,
            status=BatchJobStatus(data["status"]),
            vertex_job_name=data.get("vertex_job_name"),
            input_gcs_path=data.get("input_gcs_path"),
            output_gcs_path=data.get("output_gcs_path"),
            created_at=created_at,
            started_at=started_at,
            completed_at=completed_at,
            results=results,
            error_message=data.get("error_message")
        )
    
    def save_to_file(self, file_path: Path) -> None:
        """Save job to JSON file."""
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
        logger.info(f"Saved batch job {self.job_id} to {file_path}")
    
    @classmethod
    def load_from_file(cls, file_path: Path) -> "BatchJob":
        """Load job from JSON file."""
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
        job = cls.from_dict(data)
        logger.info(f"Loaded batch job {job.job_id} from {file_path}")
        return job


class BatchJobManager:
    """Manages batch job persistence and tracking."""

    def __init__(self, jobs_dir: Path):
        """Initialize the job manager.

        Args:
            jobs_dir: Directory to store job files
        """
        self.jobs_dir = Path(jobs_dir)
        self.jobs_dir.mkdir(parents=True, exist_ok=True)

    def save_job(self, job: BatchJob) -> None:
        """Save a job to disk."""
        job_file = self.jobs_dir / f"{job.job_id}.json"
        job.save_to_file(job_file)

    def load_job(self, job_id: str) -> Optional[BatchJob]:
        """Load a job from disk."""
        job_file = self.jobs_dir / f"{job_id}.json"
        if job_file.exists():
            return BatchJob.load_from_file(job_file)
        return None

    def list_jobs(self) -> List[BatchJob]:
        """List all jobs."""
        jobs = []
        for job_file in self.jobs_dir.glob("*.json"):
            try:
                job = BatchJob.load_from_file(job_file)
                jobs.append(job)
            except Exception as e:
                logger.warning(f"Failed to load job from {job_file}: {e}")
        return sorted(jobs, key=lambda j: j.created_at or datetime.min, reverse=True)

    def delete_job(self, job_id: str) -> bool:
        """Delete a job from disk."""
        job_file = self.jobs_dir / f"{job_id}.json"
        if job_file.exists():
            job_file.unlink()
            logger.info(f"Deleted job {job_id}")
            return True
        return False
