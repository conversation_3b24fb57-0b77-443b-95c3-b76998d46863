"""Main scraping pipeline with multi-level discovery and dry-run support."""

from __future__ import annotations

import logging
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from pathlib import Path

from pydantic import HttpUrl
from tqdm import tqdm

from .client import GazetteHTTPClient
from .models import (
    DiscoveryType,
    FileItem,
    Folder,
    GazetteFile,
    Manifest,
    ScrapeResult,
    ScrapingConfig,
)
from .parser import GazetteParser
from .state import ScrapingState
from .storage import GCSStorage, LocalStorage, ManifestWriter
from .supabase_client import log_error, upsert_file

logger = logging.getLogger(__name__)


class GazettePipeline:
    """Main pipeline for scraping Official Gazette files with multi-level discovery."""

    def __init__(self, config: ScrapingConfig):
        self.config = config
        self.client = GazetteHTTPClient(
            rate_limit=config.rate_limit,
            max_retries=config.max_retries,
            user_agent=config.user_agent,
            accept_language=config.accept_language,
            proxies=config.proxies,
            jitter_range=config.jitter_range,
        )
        self.parser = GazetteParser(config.base_url)
        self.state = ScrapingState(config.db_path)
        self.storage = LocalStorage(config.output_dir)
        self.manifest = ManifestWriter(config.output_dir / "gazettes.csv")

        self.gcs_storage = None
        if config.gcs_bucket:
            self.gcs_storage = GCSStorage(config.gcs_bucket)

    def run(self, dry_run: bool = False) -> ScrapeResult:
        """Run the complete scraping pipeline."""
        logger.info(f"Starting gazette scraping pipeline (dry_run={dry_run})")

        result = ScrapeResult(start_time=datetime.now())

        try:
            # Check robots.txt
            if not self._check_robots_txt():
                logger.error("Robots.txt disallows crawling")
                result.errors += 1
                result.error_messages.append("Robots.txt disallows crawling")
                return result

            # Build complete manifest
            manifest = self.build_manifest(
                dry_run, self.config.since_year, self.config.output_dir
            )

            result.total_discovered = manifest.total_discovered
            logger.info(f"Total files discovered: {manifest.total_discovered}")

            if not dry_run:
                # Download files
                downloaded, errors = self.download_files(manifest)
                result.downloaded = downloaded
                result.errors += errors
            else:
                # For dry run, all discovered files are "skipped"
                result.skipped = manifest.total_discovered
                logger.info("Dry run completed - no files downloaded")

        except Exception as e:
            logger.error(f"Pipeline error: {e}")
            result.errors += 1
            result.error_messages.append(f"Pipeline error: {e}")

        finally:
            result.end_time = datetime.now()
            self._log_summary(result)

        return result

    def build_manifest(
        self, dry_run: bool, since_year: int | None, out_dir: Path
    ) -> Manifest:
        """Build complete manifest of all discoverable files."""
        manifest = Manifest()

        # Discover all years
        year_folders = self.discover_years(f"{self.config.base_url}/official-gazette")

        # Filter by year if specified
        if since_year:
            year_folders = [f for f in year_folders if f.year and f.year >= since_year]
            logger.info(
                f"Filtered to {len(year_folders)} year folders since {since_year}"
            )

        # Conditional traversal based on crawler_depth
        if self.config.crawler_depth >= 2:
            # Full month-level traversal
            all_month_folders = []
            for year_folder in tqdm(year_folders, desc="Discovering months"):
                try:
                    month_folders = self.discover_months(year_folder)
                    all_month_folders.extend(month_folders)
                except Exception as e:
                    logger.error(f"Failed to discover months for {year_folder.name}: {e}")

            logger.info(f"Discovered {len(all_month_folders)} month folders")
            target_folders = all_month_folders
            desc = "Discovering files from months"
        else:
            # Year-level only (treat year folders as file containers)
            logger.info(f"Using year-level traversal (depth={self.config.crawler_depth})")
            target_folders = year_folders
            desc = "Discovering files from years"

        # Discover files from target folders
        all_files = []
        for folder in tqdm(target_folders, desc=desc):
            try:
                file_items = self.discover_files(folder)

                # Convert FileItems to GazetteFiles
                for item in file_items:
                    gazette_file = self._convert_file_item(item, folder)
                    if gazette_file:
                        all_files.append(gazette_file)

                        # Record discovery
                        self.state.record_file_discovery(gazette_file)

            except Exception as e:
                logger.error(f"Failed to discover files for {folder.name}: {e}")

        # Build manifest
        manifest.files = all_files
        manifest.total_discovered = len(all_files)

        # Calculate stats
        for file in all_files:
            manifest.by_year[file.year] = manifest.by_year.get(file.year, 0) + 1
            month_key = f"{file.year:04d}-{file.month:02d}"
            manifest.by_month[month_key] = manifest.by_month.get(month_key, 0) + 1

        # Write manifest to CSV
        self._write_manifest_csv(manifest, out_dir / "gazettes.csv", dry_run)

        return manifest

    def discover_years(self, start_url: str) -> list[Folder]:
        """Discover all year folders with enhanced pagination for historical data."""
        folders = []
        seen_signatures = set()
        seen_year_folder_names = set()
        consecutive_duplicates = 0
        max_consecutive_duplicates = 3  # Reduce since we have proper pagination now
        pagination_urls = {}  # Cache of extracted pagination URLs

        page_num = 1
        while page_num <= 10:  # Start with conservative limit
            try:
                if page_num == 1:
                    page_url = start_url
                else:
                    # Use extracted pagination URLs if available
                    if page_num in pagination_urls:
                        page_url = pagination_urls[page_num]
                    else:
                        logger.info(f"No pagination URL found for page {page_num}, stopping")
                        break

                logger.debug(f"Fetching year page {page_num}: {page_url}")
                response = self.client.get(page_url)

                # Extract pagination URLs from the current page for future use
                if page_num == 1:
                    pagination_urls = self.parser.extract_pagination_urls(response.text, start_url)
                    logger.debug(f"Found pagination URLs: {list(pagination_urls.keys())}")

                # Check for duplicate page signature
                page_signature = self.parser.get_page_signature(response.text)
                if page_signature in seen_signatures:
                    consecutive_duplicates += 1
                    logger.debug(f"Duplicate page signature at page {page_num} (consecutive: {consecutive_duplicates})")
                    
                    # Only stop if we've seen too many consecutive duplicates
                    if consecutive_duplicates >= max_consecutive_duplicates:
                        logger.info(f"Too many consecutive duplicates ({consecutive_duplicates}), stopping pagination")
                        break
                    
                    # Skip this page but continue searching
                    page_num += 1
                    continue
                else:
                    consecutive_duplicates = 0  # Reset counter on new content
                
                seen_signatures.add(page_signature)

                # Parse folders
                page_folders = self.parser.parse_year_page(response.text, page_url)

                if not page_folders:
                    logger.debug(f"No year folders found on page {page_num}")
                    page_num += 1
                    continue

                # Track unique year folders to detect real progress
                new_folders = []
                for folder in page_folders:
                    folder_key = f"{folder.name}_{folder.year}"
                    if folder_key not in seen_year_folder_names:
                        seen_year_folder_names.add(folder_key)
                        new_folders.append(folder)

                if new_folders:
                    logger.debug(f"Page {page_num}: Found {len(new_folders)} new year folders")
                    
                # Always add to overall list (for compatibility with existing logic)
                folders.extend(page_folders)
                self.state.record_discovery(
                    page_url, DiscoveryType.YEAR, len(page_folders)
                )

                # Check for next page (but continue even if no explicit next page indicator)
                page_num += 1

            except Exception as e:
                logger.error(f"Failed to fetch year page {page_num}: {e}")
                # Continue with next page instead of breaking immediately
                page_num += 1
                if page_num > max_pages:
                    break

        # Remove duplicates from final result based on year and name
        unique_folders = []
        seen_folders = set()
        for folder in folders:
            folder_key = f"{folder.name}_{folder.year}"
            if folder_key not in seen_folders:
                seen_folders.add(folder_key)
                unique_folders.append(folder)

        logger.info(f"Discovered {len(unique_folders)} unique year folders across {page_num-1} pages")
        return unique_folders

    def discover_months(self, year_folder: Folder) -> list[Folder]:
        """Discover all month folders for a year with pagination."""
        folders = []
        seen_signatures = set()

        page_num = 1
        while True:
            try:
                if page_num == 1:
                    page_url = year_folder.href
                else:
                    page_url = self.parser.build_next_page_url(
                        year_folder.href, page_num
                    )

                logger.debug(
                    f"Fetching month page {page_num} for {year_folder.name}: {page_url}"
                )
                response = self.client.get(
                    page_url, referer=f"{self.config.base_url}/official-gazette"
                )

                # Check for duplicate page
                page_signature = self.parser.get_page_signature(response.text)
                if page_signature in seen_signatures:
                    logger.info(
                        f"Duplicate month page detected at page {page_num}, stopping"
                    )
                    break
                seen_signatures.add(page_signature)

                # Parse folders
                page_folders = self.parser.parse_month_page(
                    response.text, page_url, year_folder.year or 0
                )

                if not page_folders:
                    logger.info(f"No more month folders found on page {page_num}")
                    break

                folders.extend(page_folders)
                self.state.record_discovery(
                    page_url, DiscoveryType.MONTH, len(page_folders)
                )

                # Check for next page
                if not self.parser.has_next_page(response.text):
                    logger.info("No more month pages")
                    break

                page_num += 1

            except Exception as e:
                logger.error(
                    f"Failed to fetch month page {page_num} for {year_folder.name}: {e}"
                )
                break

        logger.info(f"Discovered {len(folders)} month folders for {year_folder.name}")
        return folders

    def discover_files(self, month_folder: Folder) -> list[FileItem]:
        """Discover all files in a month folder with pagination."""
        files = []
        seen_signatures = set()

        page_num = 1
        while True:
            try:
                if page_num == 1:
                    page_url = month_folder.href
                else:
                    page_url = self.parser.build_next_page_url(
                        month_folder.href, page_num
                    )

                logger.debug(
                    f"Fetching file page {page_num} for {month_folder.name}: {page_url}"
                )
                response = self.client.get(page_url, referer=month_folder.href)

                # Check for duplicate page
                page_signature = self.parser.get_page_signature(response.text)
                if page_signature in seen_signatures:
                    logger.info(
                        f"Duplicate file page detected at page {page_num}, stopping"
                    )
                    break
                seen_signatures.add(page_signature)

                # Parse files
                page_files = self.parser.parse_file_page(response.text, page_url)

                if not page_files:
                    logger.info(f"No more files found on page {page_num}")
                    break

                files.extend(page_files)
                self.state.record_discovery(
                    page_url, DiscoveryType.FILE, len(page_files)
                )

                # Check for next page
                if not self.parser.has_next_page(response.text):
                    logger.info("No more file pages")
                    break

                page_num += 1

            except Exception as e:
                logger.error(
                    f"Failed to fetch file page {page_num} for {month_folder.name}: {e}"
                )
                break

        logger.info(f"Discovered {len(files)} files for {month_folder.name}")
        return files

    def download_files(self, manifest: Manifest) -> tuple[int, int]:
        """Download files from manifest with robust error handling."""
        downloaded = 0
        errors = 0

        # Filter out already downloaded files
        files_to_download = [
            f for f in manifest.files if not self.state.is_already_downloaded(f)
        ]

        logger.info(f"Will download {len(files_to_download)} new files")

        if not files_to_download:
            return 0, 0

        with ThreadPoolExecutor(max_workers=self.config.max_threads) as executor:
            future_to_file = {
                executor.submit(self._download_single_file, file): file
                for file in files_to_download
            }

            for future in tqdm(
                as_completed(future_to_file),
                total=len(files_to_download),
                desc="Downloading files",
            ):
                file = future_to_file[future]
                try:
                    success = future.result()
                    if success:
                        downloaded += 1
                    else:
                        errors += 1
                except Exception as e:
                    logger.error(f"Download error for {file.filename}: {e}")
                    errors += 1

        return downloaded, errors

    def _download_single_file(self, file: GazetteFile) -> bool:
        """Download a single file with 403/410 retry logic."""
        max_attempts = self.config.max_retries

        for attempt in range(max_attempts):
            try:
                # Check if we've failed too many times previously
                if self.state.get_failed_attempts(file) >= max_attempts:
                    logger.warning(
                        f"Skipping {file.filename} - too many previous failures"
                    )
                    return False

                # Get local file path
                local_path = self.storage.get_file_path(file)

                # Attempt download
                response = self.client.get(
                    str(file.download_url), referer=str(file.listing_url), stream=True
                )

                # Handle 403/410 errors with listing refresh
                if response.status_code in [403, 410]:
                    logger.warning(f"HTTP {response.status_code} for {file.filename}")

                    if attempt == 0:  # Only try refreshing listing once
                        logger.info("Refreshing listing page and retrying download")
                        # Refresh the listing page to get fresh links
                        try:
                            fresh_response = self.client.get(str(file.listing_url))
                            fresh_files = self.parser.parse_file_page(
                                fresh_response.text, str(file.listing_url)
                            )

                            # Try to find the same file with a fresh URL
                            for fresh_file_item in fresh_files:
                                if fresh_file_item.title == file.title:
                                    file.download_url = HttpUrl(fresh_file_item.href)
                                    logger.info(
                                        f"Updated download URL for {file.filename}"
                                    )
                                    break
                        except Exception as e:
                            logger.error(f"Failed to refresh listing: {e}")

                    # Continue to next attempt
                    continue

                elif response.status_code != 200:
                    error_msg = f"HTTP {response.status_code}"
                    logger.error(f"Download failed for {file.filename}: {error_msg}")
                    self.state.record_failure(file, error_msg, response.status_code)
                    # Log error to Supabase
                    log_error(str(file.listing_url), str(file.download_url), error_msg, response.status_code)
                    return False

                # Download successful - save file
                content = response.content
                if not content:
                    error_msg = "Empty content"
                    logger.error(f"Empty content for {file.filename}")
                    self.state.record_failure(file, error_msg)
                    # Log error to Supabase
                    log_error(str(file.listing_url), str(file.download_url), error_msg)
                    return False

                # Calculate SHA256 and save
                sha256 = self.storage.save_pdf(content, local_path)

                # Check for duplicates
                if self.state.has_sha256(sha256):
                    logger.info(
                        f"Duplicate file detected (same SHA256): {file.filename}"
                    )
                    local_path.unlink()  # Remove duplicate
                    return False

                # Record successful download
                file.sha256 = sha256
                file.local_path = local_path
                self.state.record_download(file, local_path, sha256)

                # Upload to GCS if configured
                gcs_path = None
                supabase_upserted = False
                if self.gcs_storage:
                    gcs_path = (
                        f"{self.config.gcs_prefix}{file.year}/{file.month:02d}/{local_path.name}"
                    )
                    self.gcs_storage.upload_file(local_path, gcs_path)

                    # Upsert metadata to Supabase after successful GCS upload
                    if upsert_file(file, gcs_path, sha256):
                        self.state.mark_supabase_synced(sha256)
                        supabase_upserted = True

                self.manifest.append_file(file, local_path, sha256, supabase_upserted)

                logger.info(f"Successfully downloaded: {file.filename}")
                return True

            except Exception as e:
                error_msg = f"Download attempt {attempt + 1} failed: {e}"
                logger.warning(f"{file.filename}: {error_msg}")

                if attempt == max_attempts - 1:  # Last attempt
                    self.state.record_failure(file, error_msg)
                    # Log error to Supabase on final failure
                    log_error(str(file.listing_url), str(file.download_url), error_msg)
                    return False

        return False

    def _convert_file_item(
        self, item: FileItem, month_folder: Folder
    ) -> GazetteFile | None:
        """Convert a FileItem to a GazetteFile."""
        try:
            # Extract filename from title or URL
            filename = item.title
            if not filename.lower().endswith(".pdf"):
                filename += ".pdf"

            # Normalize title
            issue_title = self.parser.normalize_title(item.title)

            # Parse publication date
            pub_date = None
            if item.modified:
                pub_date = self.parser.parse_date_from_text(item.modified)

            # If no pub_date from modified, try to extract from filename
            if not pub_date:
                pub_date = self._extract_pub_date_from_filename(
                    filename, month_folder.year or 0, month_folder.month or 1
                )

            # Classify the document
            doc_type, subject_category, keywords = self.parser.classify_document(item.title)

            return GazetteFile(
                title=item.title,
                filename=filename,
                issue_title=issue_title,
                size_bytes=item.size_bytes,
                size_str=item.size_str,
                modified_date=self.parser.parse_date_from_text(item.modified)
                if item.modified
                else None,
                pub_date=pub_date,
                download_url=HttpUrl(item.href),
                source_url=HttpUrl(item.href),  # Same as download_url for compatibility
                listing_url=HttpUrl(item.listing_url),
                year=month_folder.year or 0,
                month=month_folder.month or 1,
                month_folder=month_folder.name,
                document_type=doc_type,
                subject_category=subject_category,
                keywords=keywords,
            )

        except Exception as e:
            logger.warning(f"Failed to convert file item {item.title}: {e}")
            return None

    def _extract_pub_date_from_filename(
        self, filename: str, year: int, month: int
    ) -> datetime | None:
        """Try to extract publication date from filename."""
        # Look for date patterns in filename
        date_match = re.search(r"(\d{1,2})[/-](\d{1,2})[/-](\d{2,4})", filename)
        if date_match:
            try:
                day, mon, yr = date_match.groups()
                if len(yr) == 2:
                    yr = f"20{yr}" if int(yr) < 50 else f"19{yr}"
                return datetime(int(yr), int(mon), int(day))
            except ValueError:
                pass

        # Fallback to first day of the folder month
        try:
            return datetime(year, month, 1)
        except ValueError:
            return None

    def _write_manifest_csv(
        self, manifest: Manifest, csv_path: Path, dry_run: bool
    ) -> None:
        """Write manifest to CSV file."""
        import csv

        csv_path.parent.mkdir(parents=True, exist_ok=True)

        with open(csv_path, "w", newline="", encoding="utf-8") as f:
            fieldnames = [
                "year",
                "month",
                "issue_title",
                "pub_date",
                "filename",
                "size_bytes",
                "sha256",
                "source_url",
                "listing_url",
                "discovered_at",
                "document_type",
                "subject_category",
                "keywords",
            ]

            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()

            for file in manifest.files:
                writer.writerow(
                    {
                        "year": file.year,
                        "month": file.month,
                        "issue_title": file.issue_title,
                        "pub_date": file.pub_date.isoformat() if file.pub_date else "",
                        "filename": file.filename,
                        "size_bytes": file.size_bytes or "",
                        "sha256": file.sha256
                        if not dry_run
                        else "",  # Empty in dry run
                        "source_url": str(file.source_url),
                        "listing_url": str(file.listing_url),
                        "discovered_at": file.discovered_at.isoformat(),
                        "document_type": file.document_type.value,
                        "subject_category": file.subject_category.value,
                        "keywords": "|".join(file.keywords),
                    }
                )

        logger.info(f"Wrote manifest to {csv_path}")

    def _check_robots_txt(self) -> bool:
        """Check robots.txt for crawling permissions."""
        try:
            robots_url = f"{self.config.base_url}/robots.txt"

            # Check cache first
            cached_result = self.state.get_robots_check(robots_url)
            if cached_result is not None:
                logger.info(
                    f"Using cached robots.txt result: {'allowed' if cached_result else 'disallowed'}"
                )
                return cached_result

            response = self.client.get(robots_url)
            robots_content = response.text.lower()

            # Check for disallow rules that might affect us
            lines = robots_content.split("\n")
            for line in lines:
                line = line.strip().lower()
                if line.startswith("disallow:"):
                    disallow_path = line.replace("disallow:", "").strip()
                    # Check if this would block our paths
                    blocked_paths = ["/official-gazette", "/fileadmin/user_upload/"]
                    for blocked_path in blocked_paths:
                        if disallow_path == blocked_path or (
                            disallow_path.endswith("*")
                            and blocked_path.startswith(disallow_path[:-1])
                        ):
                            logger.warning(
                                f"robots.txt disallows {disallow_path} which blocks {blocked_path}"
                            )
                            self.state.record_robots_check(
                                robots_url, response.text, False
                            )
                            return False

            # Record as allowed
            self.state.record_robots_check(robots_url, response.text, True)
            return True

        except Exception as e:
            logger.warning(f"Could not check robots.txt: {e}")
            # Assume allowed if we can't check
            return True

    def _log_summary(self, result: ScrapeResult) -> None:
        """Log scraping summary."""
        duration = (result.end_time - result.start_time) if result.end_time else None

        logger.info("=== SCRAPING SUMMARY ===")
        logger.info(f"Total discovered: {result.total_discovered}")
        logger.info(f"Downloaded: {result.downloaded}")
        logger.info(f"Skipped: {result.skipped}")
        logger.info(f"Errors: {result.errors}")
        if duration:
            logger.info(f"Duration: {duration}")

        if result.error_messages:
            logger.error("Error messages:")
            for msg in result.error_messages[:10]:  # Show first 10 errors
                logger.error(f"  - {msg}")

        # Show database stats
        stats = self.state.get_stats()
        logger.info(f"Total files in database: {stats['total_downloaded']}")
        logger.info(f"Total discovered: {stats['total_discovered']}")
        if stats["discovered_by_year"]:
            logger.info(f"Discovery by year: {stats['discovered_by_year']}")
        if stats["discovered_by_month"]:
            logger.info(
                f"Recent months: {dict(list(stats['discovered_by_month'].items())[:5])}"
            )
