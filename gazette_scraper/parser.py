"""HTML parsing for multi-level gazette discovery."""

from __future__ import annotations

import logging
import re
from datetime import datetime
from urllib.parse import urljoin

from bs4 import BeautifulSoup, Tag

from .models import DocumentType, FileItem, Folder, FolderType, SubjectCategory

logger = logging.getLogger(__name__)


class GazetteParser:
    """Parser for minijust.gov.rw gazette pages with multi-level discovery."""

    def __init__(self, base_url: str = "https://www.minijust.gov.rw"):
        self.base_url = base_url

    def parse_year_page(self, html_content: str, page_url: str) -> list[Folder]:
        """Parse year-level folders from the main gazette page."""
        soup = BeautifulSoup(html_content, "html.parser")
        folders = []

        # Look for links with tx_filelist_filelist parameters
        links = soup.find_all("a", href=True)

        for link in links:
            if not isinstance(link, Tag):
                continue
            href = link.get("href", "")
            if not isinstance(href, str):
                continue

            # Look for tx_filelist_filelist parameters (URL encoded or not)
            if any(
                pattern in href for pattern in ["tx_filelist_filelist", "tx_filelist"]
            ):
                folder_info = self._extract_year_folder_info(href, page_url, link)
                if folder_info:
                    folders.append(folder_info)

        logger.info(f"Found {len(folders)} year folders")
        return folders

    def parse_month_page(
        self, html_content: str, page_url: str, year: int
    ) -> list[Folder]:
        """Parse month-level folders from a year folder page."""
        soup = BeautifulSoup(html_content, "html.parser")
        folders = []

        # Look for table rows containing folder links
        tables = soup.find_all("table")

        for table in tables:
            if not isinstance(table, Tag):
                continue
            rows = table.find_all("tr")

            for row in rows:
                if not isinstance(row, Tag):
                    continue
                # Skip header rows
                if row.find("th"):
                    continue

                # Look for folder links in this row
                links = row.find_all("a", href=True)

                for link in links:
                    if not isinstance(link, Tag):
                        continue
                    href = link.get("href", "")
                    if not isinstance(href, str):
                        continue
                    text = link.get_text(strip=True)

                    # Skip empty text or ".." parent directory links
                    if not text or text == ".." or not href:
                        continue

                    # Look for tx_filelist_filelist parameters indicating a folder
                    if "tx_filelist_filelist" in href:
                        # Check if this is a folder (has folder icon or is a directory link)
                        is_folder = self._is_folder_link(row, link)

                        if is_folder:
                            folder_info = self._extract_month_folder_info(
                                href, page_url, link, year
                            )
                            if folder_info:
                                folders.append(folder_info)

        logger.info(f"Found {len(folders)} month folders for year {year}")
        return folders

    def parse_month_page_with_pagination(
        self, initial_html: str, page_url: str, year: int, client: object
    ) -> list[Folder]:
        """Parse month folders from all pages (handles pagination)."""
        all_folders = []
        current_url = page_url
        current_html = initial_html
        seen_signatures = set()
        page_count = 0
        max_pages = 50  # Safety limit

        while page_count < max_pages:
            page_count += 1

            # Check for duplicate content (infinite loop protection)
            signature = self.get_page_signature(current_html)
            if signature in seen_signatures:
                logger.warning(f"Duplicate content detected at {current_url}, stopping pagination")
                break
            seen_signatures.add(signature)

            # Parse folders from current page
            page_folders = self.parse_month_page(current_html, current_url, year)
            all_folders.extend(page_folders)

            logger.debug(f"Page {page_count}: found {len(page_folders)} month folders")

            # Check for next page
            if not self.has_next_page(current_html):
                break

            next_url = self.get_next_page_url(current_html, current_url)
            if not next_url:
                break

            # Fetch next page
            try:
                response = client.get(next_url, referer=current_url)  # type: ignore[attr-defined]
                response.raise_for_status()
                current_html = response.text
                current_url = next_url
            except Exception as e:
                logger.warning(f"Failed to fetch next page {next_url}: {e}")
                break

        logger.info(f"Discovered {len(all_folders)} month folders across {page_count} pages for year {year}")
        return all_folders

    def parse_file_page(self, html_content: str, page_url: str) -> list[FileItem]:
        """Parse file listings from a month folder page."""
        soup = BeautifulSoup(html_content, "html.parser")
        files = []

        # Look for table rows containing file information
        tables = soup.find_all("table")

        for table in tables:
            if not isinstance(table, Tag):
                continue
            rows = table.find_all("tr")

            for row in rows:
                if not isinstance(row, Tag):
                    continue
                # Skip header rows
                if row.find("th"):
                    continue

                file_info = self._extract_file_info(row, page_url)
                if file_info:
                    files.append(file_info)

        logger.info(f"Found {len(files)} files in folder")
        return files

    def _extract_year_folder_info(
        self, href: str, base_url: str, link_element: Tag
    ) -> Folder | None:
        """Extract year folder information from a link with enhanced historical year support."""
        try:
            text = link_element.get_text(strip=True)

            # Enhanced year extraction patterns
            # Try different patterns to catch various historical naming conventions
            year_patterns = [
                r"(\d{4})",  # Standard 4-digit year
                r"year\s*(\d{4})",  # "year 2004" format
                r"(\d{4})\s*year",  # "2004 year" format
                r"(\d{4})\s*gazette",  # "2004 gazette" format
                r"gazette\s*(\d{4})",  # "gazette 2004" format
            ]
            
            year = None
            for pattern in year_patterns:
                year_match = re.search(pattern, text, re.IGNORECASE)
                if year_match:
                    candidate_year = int(year_match.group(1))
                    # Validate year range (reasonable bounds for legal documents)
                    if 2000 <= candidate_year <= 2030:
                        year = candidate_year
                        break

            # Also try to extract year from the href path as backup
            if year is None:
                href_year_match = re.search(r"(\d{4})", href)
                if href_year_match:
                    candidate_year = int(href_year_match.group(1))
                    if 2000 <= candidate_year <= 2030:
                        year = candidate_year

            if year is None:
                logger.debug(f"No valid year found in '{text}' or '{href}'")
                return None

            folder_url = urljoin(base_url, href)

            return Folder(
                name=text, href=folder_url, folder_type=FolderType.YEAR, year=year
            )

        except Exception as e:
            logger.warning(f"Failed to parse year folder link {href}: {e}")

        return None

    def _extract_month_folder_info(
        self, href: str, base_url: str, link_element: Tag, year: int
    ) -> Folder | None:
        """Extract month folder information from a link."""
        try:
            text = link_element.get_text(strip=True)

            # Infer month from various patterns
            month = self._infer_month_from_name(text)

            folder_url = urljoin(base_url, href)

            return Folder(
                name=text,
                href=folder_url,
                folder_type=FolderType.MONTH,
                year=year,
                month=month,
            )

        except Exception as e:
            logger.warning(f"Failed to parse month folder link {href}: {e}")

        return None

    def _extract_file_info(self, row: Tag, page_url: str) -> FileItem | None:
        """Extract file information from a table row."""
        try:
            cells = row.find_all(["td", "th"])
            if len(cells) < 2:
                return None

            # Look for download links with dumpFile pattern
            download_links = row.find_all("a", href=lambda x: x and "dumpFile" in x)
            if not download_links:
                return None

            # Find the link with actual text content (not just an image)
            download_link = None
            href = ""
            title = ""

            for link in download_links:
                if not isinstance(link, Tag):
                    continue
                link_text = link.get_text(strip=True)
                if link_text:  # Found link with actual text
                    download_link = link
                    href = str(link.get("href", ""))
                    title = link_text
                    break

            if not download_link or not href:
                return None

            # Skip if not a PDF or if title is empty
            if not title or not self._looks_like_pdf(title, href):
                return None

            # Extract file size and date from adjacent cells
            size_bytes = None
            size_str = None
            modified = None

            for cell in cells:
                cell_text = cell.get_text(strip=True)

                # Look for size pattern (e.g., "1.2 MB", "456 KB")
                if re.match(r"\d+\.?\d*\s*(KB|MB|GB)", cell_text, re.IGNORECASE):
                    size_str = cell_text
                    size_bytes = self._parse_size_to_bytes(cell_text)

                # Look for date pattern
                elif self._is_date_like(cell_text):
                    modified = cell_text

            # Build full download URL
            download_url = urljoin(page_url, href)

            return FileItem(
                title=title,
                href=download_url,
                size_bytes=size_bytes,
                size_str=size_str,
                modified=modified,
                listing_url=page_url,
            )

        except Exception as e:
            logger.warning(f"Failed to parse file row: {e}")

        return None

    def _is_folder_link(self, row: Tag, link: Tag) -> bool:
        """Determine if a link represents a folder."""
        # Check for folder icon
        folder_icons = row.find_all("img", src=lambda x: x and "folder" in x.lower())
        if folder_icons:
            return True

        # Check if link text suggests a folder (no file extension)
        text = link.get_text(strip=True)
        if text and not re.search(r"\.[a-zA-Z]{2,4}$", text):
            return True

        return False

    def _looks_like_pdf(self, title: str, href: str) -> bool:  # noqa: ARG002
        """Check if this looks like a PDF file."""
        # Check title
        if title.lower().endswith(".pdf"):
            return True

        # Check for common gazette keywords
        gazette_keywords = ["gazette", "official", "igazeti", "og_", "og-"]
        title_lower = title.lower()

        return any(keyword in title_lower for keyword in gazette_keywords)

    def _infer_month_from_name(self, name: str) -> int | None:
        """Infer month number from folder name."""
        name_lower = name.lower()

        # Month mappings for various languages
        month_mappings = {
            # English
            "january": 1,
            "february": 2,
            "march": 3,
            "april": 4,
            "may": 5,
            "june": 6,
            "july": 7,
            "august": 8,
            "september": 9,
            "october": 10,
            "november": 11,
            "december": 12,
            # French
            "janvier": 1,
            "fevrier": 2,
            "mars": 3,
            "avril": 4,
            "mai": 5,
            "juin": 6,
            "juillet": 7,
            "aout": 8,
            "septembre": 9,
            "octobre": 10,
            "novembre": 11,
            "decembre": 12,
            # Kinyarwanda (common patterns observed)
            "mutarama": 1,
            "gashyantare": 2,
            "werurwe": 3,
            "mata": 4,
            "gicurasi": 5,
            "kamena": 6,
            "nyakanga": 7,
            "kanama": 8,
            "nzeli": 9,
            "ukwakira": 10,
            "ugushyingo": 11,
            "ukuboza": 12,
        }

        # Try exact matches first
        for month_name, month_num in month_mappings.items():
            if month_name in name_lower:
                return month_num

        # Try to extract numeric month
        month_match = re.search(r"\b(\d{1,2})\b", name)
        if month_match:
            month_num = int(month_match.group(1))
            if 1 <= month_num <= 12:
                return month_num

        # Fallback: try to infer from position or other patterns
        # This could be enhanced based on observed patterns
        return None

    def _parse_size_to_bytes(self, size_str: str) -> int | None:
        """Convert size string to bytes."""
        try:
            match = re.match(r"(\d+\.?\d*)\s*(KB|MB|GB)", size_str, re.IGNORECASE)
            if not match:
                return None

            value = float(match.group(1))
            unit = match.group(2).upper()

            multipliers = {"KB": 1024, "MB": 1024 * 1024, "GB": 1024 * 1024 * 1024}

            return int(value * multipliers.get(unit, 1))

        except Exception:
            return None

    def _is_date_like(self, text: str) -> bool:
        """Check if text looks like a date."""
        date_patterns = [
            r"\d{1,2}[/-]\d{1,2}[/-]\d{2,4}",
            r"\d{2,4}[/-]\d{1,2}[/-]\d{1,2}",
            r"\d{1,2}\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec|January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{2,4}",
        ]
        return any(re.search(pattern, text, re.IGNORECASE) for pattern in date_patterns)

    def has_next_page(self, html_content: str) -> bool:
        """Check if there's a next page in pagination."""
        soup = BeautifulSoup(html_content, "html.parser")

        # Look for pagination controls
        next_links = soup.find_all("a", string=re.compile(r"next|›|»", re.IGNORECASE))
        return len(next_links) > 0

    def get_next_page_url(self, html_content: str, current_url: str) -> str | None:
        """Get the URL for the next page."""
        soup = BeautifulSoup(html_content, "html.parser")

        # Look for next page link
        next_link = soup.find("a", string=re.compile(r"next|›|»", re.IGNORECASE))
        if next_link and isinstance(next_link, Tag):
            href = next_link.get("href")
            if href and isinstance(href, str):
                return urljoin(current_url, href)

        return None

    def extract_current_page(self, url: str) -> int:
        """Extract current page number from URL."""
        try:
            # Look for currentPage parameter
            if "currentPage=" in url:
                match = re.search(r"currentPage=(\d+)", url)
                if match:
                    return int(match.group(1))
            return 1
        except Exception:
            return 1

    def extract_pagination_urls(self, html_content: str, base_url: str) -> dict[int, str]:
        """Extract pagination URLs from the page content."""
        soup = BeautifulSoup(html_content, "html.parser")
        pagination_urls = {}
        
        # Look for pagination links with currentPage parameter
        for link in soup.find_all("a", href=True):
            href = link.get("href", "")
            if "currentPage" in href and "cHash" in href:
                # Extract page number
                page_match = re.search(r"currentPage.*?=(\d+)", href)
                if page_match:
                    page_num = int(page_match.group(1))
                    full_url = urljoin(base_url, href)
                    pagination_urls[page_num] = full_url
        
        return pagination_urls

    def build_next_page_url(self, base_url: str, page_num: int) -> str:
        """Build URL for specific page number using correct TYPO3 format."""
        separator = "&" if "?" in base_url else "?"

        # Remove existing currentPage parameter if present (both formats)
        clean_url = re.sub(r"[&?]currentPage=\d+", "", base_url)
        clean_url = re.sub(r"[&?]tx_filelist_filelist%5BcurrentPage%5D=\d+", "", clean_url)

        # Use the correct TYPO3 filelist format
        return f"{clean_url}{separator}tx_filelist_filelist%5BcurrentPage%5D={page_num}"

    def get_page_signature(self, html_content: str) -> str:
        """Get a signature for a page to detect duplicates."""
        soup = BeautifulSoup(html_content, "html.parser")

        # Extract all hrefs from links as a stable signature
        links = soup.find_all("a", href=True)
        hrefs: list[str] = []
        for link in links:
            if isinstance(link, Tag):
                href = link.get("href", "")
                if isinstance(href, str) and href:
                    hrefs.append(href)
        hrefs = sorted(hrefs)

        # Create a hash of the sorted hrefs
        import hashlib

        signature_content = "|".join(hrefs[:50])  # Use first 50 links
        return hashlib.md5(signature_content.encode()).hexdigest()

    def parse_date_from_text(self, date_text: str) -> datetime | None:
        """Parse various date formats."""
        if not date_text:
            return None

        formats = [
            "%d/%m/%Y",
            "%m/%d/%Y",
            "%Y/%m/%d",
            "%d-%m-%Y",
            "%m-%d-%Y",
            "%Y-%m-%d",
            "%d %b %Y",
            "%d %B %Y",
            "%Y-%m-%d %H:%M:%S",
        ]

        for fmt in formats:
            try:
                return datetime.strptime(date_text.strip(), fmt)
            except ValueError:
                continue

        return None

    def normalize_title(self, title: str) -> str:
        """Normalize issue title by removing redundant language variants."""
        # Remove bracketed language variants when redundant
        # e.g., "Official Gazette [Igazeti ya Leta]" -> "Official Gazette"
        normalized = re.sub(r"\s*\[[^\]]*\]\s*", " ", title)

        # Clean up extra spaces
        normalized = re.sub(r"\s+", " ", normalized).strip()

        return normalized

    def classify_document(self, title: str) -> tuple[DocumentType, SubjectCategory, list[str]]:
        """
        Classify document based on title content.
        
        Returns:
            tuple of (document_type, subject_category, keywords)
        """
        title_lower = title.lower()
        keywords = []
        
        # Document type classification based on title patterns
        if any(keyword in title_lower for keyword in ["amazina", "names"]):
            doc_type = DocumentType.NAMES_REGISTRATION
            subject = SubjectCategory.CIVIL_REGISTRATION
            keywords.extend(["names", "registration"])
            
        elif any(keyword in title_lower for keyword in ["cooperative", "amakoperative"]):
            doc_type = DocumentType.COOPERATIVE
            subject = SubjectCategory.CORPORATE
            keywords.extend(["cooperative", "business"])
            
        elif any(keyword in title_lower for keyword in ["abayobozi", "appointments", "leadership"]):
            doc_type = DocumentType.APPOINTMENTS
            subject = SubjectCategory.ADMINISTRATIVE
            keywords.extend(["appointments", "leadership"])
            
        elif any(keyword in title_lower for keyword in ["bnr", "financial statement", "bank"]):
            doc_type = DocumentType.FINANCIAL_REGULATIONS
            subject = SubjectCategory.FINANCIAL
            keywords.extend(["banking", "financial"])
            
        elif any(keyword in title_lower for keyword in ["igishushanyo mbonera", "land use", "master plan"]):
            doc_type = DocumentType.LAND_USE_PLANS
            subject = SubjectCategory.LAND_PLANNING
            keywords.extend(["land_use", "planning"])
            
        elif "idasanzwe" in title_lower:
            doc_type = DocumentType.SPECIAL_DECREE
            subject = SubjectCategory.REGULATORY
            keywords.extend(["special", "decree"])
            
        elif any(keyword in title_lower for keyword in ["industrial property", "rdb", "ip journal"]):
            doc_type = DocumentType.INDUSTRIAL_PROPERTY
            subject = SubjectCategory.INTELLECTUAL_PROPERTY
            keywords.extend(["intellectual_property", "patents"])
            
        elif any(keyword in title_lower for keyword in ["ingengo y'imari", "budget", "state budget"]):
            doc_type = DocumentType.BUDGET
            subject = SubjectCategory.FINANCIAL
            keywords.extend(["budget", "state_finance"])
            
        elif any(keyword in title_lower for keyword in ["fbo", "association", "ishyirahamwe"]):
            doc_type = DocumentType.ORGANIZATIONS
            subject = SubjectCategory.CORPORATE
            keywords.extend(["organizations", "associations"])
            
        elif any(keyword in title_lower for keyword in ["tva", "tax", "umusoro", "amahoro"]):
            doc_type = DocumentType.TAX_REGULATIONS
            subject = SubjectCategory.FINANCIAL
            keywords.extend(["tax", "revenue"])
            
        elif any(keyword in title_lower for keyword in ["sitati", "statute", "regulations"]):
            doc_type = DocumentType.STATUTES
            subject = SubjectCategory.REGULATORY
            keywords.extend(["statutes", "regulations"])
            
        else:
            doc_type = DocumentType.GENERAL
            subject = SubjectCategory.OTHER
            keywords.append("general")
        
        # Additional keyword extraction
        if "amasezerano" in title_lower:
            keywords.append("agreements")
        if "iteka" in title_lower:
            keywords.append("decree")
        if "komite" in title_lower:
            keywords.append("committee")
        if "pansiyo" in title_lower:
            keywords.append("pension")
        if "ubwishingizi" in title_lower:
            keywords.append("insurance")
            
        # Extract year from title if present
        year_match = re.search(r"20\d{2}", title)
        if year_match:
            keywords.append(f"year_{year_match.group()}")
            
        return doc_type, subject, list(set(keywords))  # Remove duplicates
