"""CLI commands for case-law scraping."""

from __future__ import annotations

import asyncio
import json
import logging
from pathlib import Path
from typing import Optional

import click
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

from ..caselaw.models import CaseLawConfig
from ..caselaw.pipeline import CaseLawPipeline

console = Console()
logger = logging.getLogger(__name__)


@click.group(name="caselaw")
@click.pass_context
def caselaw_cli(ctx: click.Context) -> None:
    """Case-law scraping commands for Amategeko portal."""
    pass


@caselaw_cli.command()
@click.option(
    "--output-dir",
    type=click.Path(path_type=Path),
    default="./caselaw_data",
    help="Output directory for downloaded files",
)
@click.option(
    "--max-concurrent",
    type=int,
    default=2,
    help="Maximum concurrent downloads",
)
@click.option(
    "--browser-timeout",
    type=int,
    default=30000,
    help="Browser timeout in milliseconds",
)
@click.option(
    "--headless/--no-headless",
    default=True,
    help="Run browser in headless mode",
)
@click.option(
    "--debug",
    is_flag=True,
    help="Enable debug mode with screenshots",
)
@click.option(
    "--resume",
    is_flag=True,
    help="Resume from previous state",
)
@click.option(
    "--dry-run",
    is_flag=True,
    help="Discover structure without downloading",
)
def scrape(
    output_dir: Path,
    max_concurrent: int,
    browser_timeout: int,
    headless: bool,
    debug: bool,
    resume: bool,
    dry_run: bool,
) -> None:
    """Start case-law scraping process."""
    console.print("[bold green]Starting Case-Law Scraping[/bold green]")

    # Create configuration
    config = CaseLawConfig(
        output_dir=output_dir,
        max_concurrent_downloads=max_concurrent,
        browser_timeout=browser_timeout,
        headless=headless,
        debug_mode=debug,
        screenshot_on_error=debug,
    )

    # Ensure output directory exists
    output_dir.mkdir(parents=True, exist_ok=True)

    if dry_run:
        console.print("[yellow]Running in dry-run mode - no files will be downloaded[/yellow]")

    try:
        # Run async pipeline
        result = asyncio.run(_run_scraping_pipeline(config, resume, dry_run))

        # Display results
        _display_scraping_results(result)

    except KeyboardInterrupt:
        console.print("\n[yellow]Scraping interrupted by user[/yellow]")
    except Exception as e:
        console.print(f"[red]Error during scraping: {e}[/red]")
        raise


@caselaw_cli.command()
@click.option(
    "--output-dir",
    type=click.Path(path_type=Path),
    default="./caselaw_data",
    help="Case-law data directory",
)
def status(output_dir: Path) -> None:
    """Show current scraping status and progress."""
    console.print("[bold blue]Case-Law Scraping Status[/bold blue]")

    try:
        # Initialize state to read progress
        from ..caselaw.state import CaseLawState

        state = CaseLawState(output_dir / "caselaw_state.db")
        progress = state.get_navigation_progress()

        # Create status table
        table = Table(title="Navigation Progress")
        table.add_column("Category", style="cyan")
        table.add_column("Status", style="white")
        table.add_column("Count", style="green", justify="right")

        # Node statistics
        for status, count in progress["nodes"].items():
            table.add_row("Nodes", status.title(), str(count))

        table.add_row("", "", "")  # Separator

        # Case statistics
        for status, count in progress["cases"].items():
            table.add_row("Cases", status.title(), str(count))

        table.add_row("", "", "")  # Separator

        # Document statistics
        table.add_row("Documents", "Expected", str(progress["expected_documents"]))
        table.add_row("Documents", "Discovered", str(progress["discovered_documents"]))
        table.add_row("Documents", "Downloaded", str(progress["downloaded_documents"]))

        console.print(table)

        # Show resume information
        resume_info = state.get_resume_position()
        if resume_info:
            path, node_type = resume_info
            console.print(f"\n[yellow]Can resume from:[/yellow] {path} (type: {node_type})")
        else:
            console.print("\n[green]No resume position found - scraping may be complete[/green]")

    except Exception as e:
        console.print(f"[red]Error reading status: {e}[/red]")


@caselaw_cli.command()
@click.option(
    "--output-dir",
    type=click.Path(path_type=Path),
    default="./caselaw_data",
    help="Case-law data directory",
)
@click.option(
    "--format",
    type=click.Choice(["json", "table"]),
    default="table",
    help="Output format",
)
def validate(output_dir: Path, format: str) -> None:
    """Validate downloaded PDF files."""
    console.print("[bold blue]Validating Downloaded Files[/bold blue]")

    try:
        # Create minimal config for validation
        config = CaseLawConfig(output_dir=output_dir)

        # Run validation
        validation_stats = asyncio.run(_validate_downloads(config))

        if format == "json":
            console.print(json.dumps(validation_stats, indent=2))
        else:
            _display_validation_results(validation_stats)

    except Exception as e:
        console.print(f"[red]Error during validation: {e}[/red]")


@caselaw_cli.command()
@click.option(
    "--output-dir",
    type=click.Path(path_type=Path),
    default="./caselaw_data",
    help="Case-law data directory",
)
@click.option(
    "--backup-file",
    type=click.Path(path_type=Path),
    required=True,
    help="Backup file path",
)
def export_state(output_dir: Path, backup_file: Path) -> None:
    """Export scraping state for backup."""
    console.print("[bold blue]Exporting Scraping State[/bold blue]")

    try:
        from ..caselaw.state import CaseLawState

        state = CaseLawState(output_dir / "caselaw_state.db")
        state.export_navigation_state(backup_file)

        console.print(f"[green]State exported to:[/green] {backup_file}")

    except Exception as e:
        console.print(f"[red]Error exporting state: {e}[/red]")


@caselaw_cli.command()
@click.option(
    "--output-dir",
    type=click.Path(path_type=Path),
    default="./caselaw_data",
    help="Case-law data directory",
)
@click.option(
    "--backup-file",
    type=click.Path(path_type=Path),
    required=True,
    help="Backup file path",
)
def import_state(output_dir: Path, backup_file: Path) -> None:
    """Import scraping state from backup."""
    console.print("[bold blue]Importing Scraping State[/bold blue]")

    if not backup_file.exists():
        console.print(f"[red]Backup file not found:[/red] {backup_file}")
        return

    try:
        from ..caselaw.state import CaseLawState

        state = CaseLawState(output_dir / "caselaw_state.db")
        state.import_navigation_state(backup_file)

        console.print(f"[green]State imported from:[/green] {backup_file}")

    except Exception as e:
        console.print(f"[red]Error importing state: {e}[/red]")


@caselaw_cli.command()
@click.option(
    "--output-dir",
    type=click.Path(path_type=Path),
    default="./caselaw_data",
    help="Case-law data directory",
)
@click.option(
    "--court",
    help="Filter by court name",
)
@click.option(
    "--year",
    type=int,
    help="Filter by year",
)
@click.option(
    "--limit",
    type=int,
    default=20,
    help="Maximum number of results",
)
def list_cases(output_dir: Path, court: Optional[str], year: Optional[int], limit: int) -> None:
    """List discovered/downloaded cases."""
    console.print("[bold blue]Case List[/bold blue]")

    try:
        from ..caselaw.state import CaseLawState

        state = CaseLawState(output_dir / "caselaw_state.db")
        cases = state.get_case_files_for_download()

        # Apply filters
        if court:
            cases = [c for c in cases if court.lower() in c.court_name.lower()]
        if year:
            cases = [c for c in cases if c.year == year]

        # Limit results
        cases = cases[:limit]

        if not cases:
            console.print("[yellow]No cases found matching criteria[/yellow]")
            return

        # Create table
        table = Table(title=f"Cases ({len(cases)} shown)")
        table.add_column("Court", style="cyan")
        table.add_column("Date", style="white")
        table.add_column("Case Title", style="green")
        table.add_column("Status", style="yellow")

        for case in cases:
            status = "Downloaded" if state.is_case_already_downloaded(case) else "Pending"
            date_str = f"{case.year}-{case.month:02d}-{case.day:02d}"

            table.add_row(
                case.court_name[:20] + "..." if len(case.court_name) > 23 else case.court_name,
                date_str,
                case.case_title[:40] + "..." if len(case.case_title) > 43 else case.case_title,
                status,
            )

        console.print(table)

    except Exception as e:
        console.print(f"[red]Error listing cases: {e}[/red]")


@caselaw_cli.command()
@click.option(
    "--output-dir",
    type=click.Path(path_type=Path),
    default="./caselaw_data",
    help="Case-law data directory",
)
def generate_manifest(output_dir: Path) -> None:
    """Generate case-law manifest file."""
    console.print("[bold blue]Generating Case-Law Manifest[/bold blue]")

    try:
        config = CaseLawConfig(output_dir=output_dir)

        # Run manifest generation
        manifest = asyncio.run(_generate_manifest(config))

        manifest_path = output_dir / "caselaw_manifest.json"
        console.print(f"[green]Manifest generated:[/green] {manifest_path}")
        console.print(f"[white]Total files:[/white] {len(manifest.files)}")
        console.print(f"[white]By court:[/white] {dict(manifest.by_court)}")

    except Exception as e:
        console.print(f"[red]Error generating manifest: {e}[/red]")


# Helper functions

async def _run_scraping_pipeline(config: CaseLawConfig, resume: bool, dry_run: bool):
    """Run the scraping pipeline with progress display."""
    async with CaseLawPipeline(config) as pipeline:
        if resume:
            return await pipeline.resume_from_state()
        elif dry_run:
            # For dry run, only discover structure
            court_nodes = await pipeline.discover_tree_structure(resume=False)
            case_files = await pipeline.discover_all_cases(court_nodes)

            # Create a result without downloads
            from ..models import ScrapeResult
            from datetime import datetime

            result = ScrapeResult(start_time=datetime.now())
            result.total_discovered = len(case_files)
            result.skipped = len(case_files)
            result.end_time = datetime.now()
            return result
        else:
            return await pipeline.run_full_scrape(resume=False)


async def _validate_downloads(config: CaseLawConfig):
    """Run download validation."""
    async with CaseLawPipeline(config) as pipeline:
        return await pipeline.validate_downloads()


async def _generate_manifest(config: CaseLawConfig):
    """Generate manifest."""
    async with CaseLawPipeline(config) as pipeline:
        return await pipeline.generate_manifest()


def _display_scraping_results(result) -> None:
    """Display scraping results in a nice format."""
    # Create results table
    table = Table(title="Scraping Results")
    table.add_column("Metric", style="cyan")
    table.add_column("Count", style="green", justify="right")

    table.add_row("Total Discovered", str(result.total_discovered))
    table.add_row("Downloaded", str(result.downloaded))
    table.add_row("Skipped", str(result.skipped))
    table.add_row("Errors", str(result.errors))

    if result.end_time:
        duration = result.end_time - result.start_time
        table.add_row("Duration", str(duration).split(".")[0])  # Remove microseconds

    console.print(table)

    if result.error_messages:
        console.print("\n[red]Errors encountered:[/red]")
        for error in result.error_messages[:5]:  # Show first 5 errors
            console.print(f"  • {error}")


def _display_validation_results(stats: dict) -> None:
    """Display validation results."""
    table = Table(title="File Validation Results")
    table.add_column("Status", style="cyan")
    table.add_column("Count", style="green", justify="right")

    for key, value in stats.items():
        status_name = key.replace("_", " ").title()
        table.add_row(status_name, str(value))

    console.print(table)

    # Calculate percentages
    total = stats["total_files"]
    if total > 0:
        valid_pct = (stats["valid_files"] / total) * 100
        console.print(f"\n[green]Success rate:[/green] {valid_pct:.1f}%")

        if stats["invalid_files"] > 0 or stats["missing_files"] > 0:
            console.print("[yellow]Some files need attention - check logs for details[/yellow]")


# Add to main CLI
def add_caselaw_commands(cli_group) -> None:
    """Add case-law commands to main CLI group."""
    cli_group.add_command(caselaw_cli)