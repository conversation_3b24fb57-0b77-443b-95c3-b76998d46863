"""
CLI interface for batch processing gazette documents.
Provides commands for submitting, monitoring, and retrieving batch jobs.
"""

import json
import logging
import os
import sys
from pathlib import Path
from typing import List

import click

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from gazette_scraper.batch.batch_client import BatchProcessingClient
from gazette_scraper.batch.batch_job import BatchJobStatus

logger = logging.getLogger(__name__)


@click.group()
@click.option("--project-id", envvar="GOOGLE_CLOUD_PROJECT", required=True, help="Google Cloud project ID")
@click.option("--location", default="us-central1", help="Vertex AI location")
@click.option("--gcs-bucket", help="GCS bucket for batch processing")
@click.option("--jobs-dir", default="batch_jobs", help="Directory to store job metadata")
@click.option("--verbose", "-v", is_flag=True, help="Enable verbose logging")
@click.pass_context
def batch(ctx, project_id, location, gcs_bucket, jobs_dir, verbose):
    """Batch processing commands for gazette extraction."""
    if verbose:
        logging.basicConfig(level=logging.INFO)
    
    # Initialize batch client
    ctx.ensure_object(dict)
    ctx.obj["client"] = BatchProcessingClient(
        project_id=project_id,
        location=location,
        gcs_bucket=gcs_bucket,
        jobs_dir=jobs_dir
    )


@batch.command()
@click.argument("pdf_paths", nargs=-1, required=True, type=click.Path(exists=True, path_type=Path))
@click.option("--name", help="Display name for the batch job")
@click.option("--doc-type", default="other", help="Document type hint")
@click.option("--submit", is_flag=True, help="Submit job immediately after creation")
@click.option("--wait", is_flag=True, help="Wait for job completion (implies --submit)")
@click.pass_context
def create(ctx, pdf_paths: List[Path], name, doc_type, submit, wait):
    """Create a new batch job for processing PDF files."""
    client = ctx.obj["client"]
    
    click.echo(f"Creating batch job for {len(pdf_paths)} PDF files...")
    
    # Create job
    job = client.create_batch_job(
        pdf_paths=list(pdf_paths),
        display_name=name,
        doc_type_hint=doc_type
    )
    
    click.echo(f"✅ Created batch job: {job.job_id}")
    click.echo(f"   Display name: {job.display_name}")
    click.echo(f"   Total requests: {job.total_requests}")
    
    # Submit if requested
    if submit or wait:
        click.echo("\n📤 Submitting job to Vertex AI...")
        try:
            job = client.submit_batch_job(job)
            click.echo(f"✅ Submitted job: {job.vertex_job_name}")
            click.echo(f"   Input: {job.input_gcs_path}")
            click.echo(f"   Output: {job.output_gcs_path}")
        except Exception as e:
            click.echo(f"❌ Failed to submit job: {e}")
            return
    
    # Wait if requested
    if wait:
        click.echo("\n⏳ Waiting for job completion...")
        try:
            job = client.wait_for_completion(job, max_wait_seconds=3600)
            
            if job.status == BatchJobStatus.COMPLETED:
                click.echo(f"✅ Job completed successfully!")
                click.echo(f"   Success rate: {job.success_rate:.1%}")
                click.echo(f"   Duration: {job.duration:.1f}s")
            else:
                click.echo(f"❌ Job failed or timed out: {job.status.value}")
                if job.error_message:
                    click.echo(f"   Error: {job.error_message}")
        except Exception as e:
            click.echo(f"❌ Error waiting for job: {e}")


@batch.command()
@click.argument("job_id")
@click.pass_context
def submit(ctx, job_id):
    """Submit a created batch job to Vertex AI."""
    client = ctx.obj["client"]
    
    job = client.get_job(job_id)
    if not job:
        click.echo(f"❌ Job {job_id} not found")
        return
    
    if job.status != BatchJobStatus.PENDING:
        click.echo(f"❌ Job {job_id} is not pending (status: {job.status.value})")
        return
    
    click.echo(f"📤 Submitting job {job_id} to Vertex AI...")
    
    try:
        job = client.submit_batch_job(job)
        click.echo(f"✅ Submitted job: {job.vertex_job_name}")
        click.echo(f"   Input: {job.input_gcs_path}")
        click.echo(f"   Output: {job.output_gcs_path}")
    except Exception as e:
        click.echo(f"❌ Failed to submit job: {e}")


@batch.command()
@click.argument("job_id", required=False)
@click.option("--all", is_flag=True, help="Show all jobs")
@click.option("--status", help="Filter by status")
@click.pass_context
def status(ctx, job_id, all, status):
    """Check the status of batch jobs."""
    client = ctx.obj["client"]
    
    if job_id:
        # Show specific job
        job = client.get_job(job_id)
        if not job:
            click.echo(f"❌ Job {job_id} not found")
            return
        
        # Update status if running
        if job.status in [BatchJobStatus.PENDING, BatchJobStatus.RUNNING]:
            job = client.check_job_status(job)
        
        _display_job_details(job)
    
    else:
        # List all jobs
        jobs = client.list_jobs()
        
        if status:
            jobs = [j for j in jobs if j.status.value == status]
        
        if not jobs:
            click.echo("No batch jobs found")
            return
        
        # Display summary table
        click.echo(f"{'Job ID':<36} {'Name':<30} {'Status':<12} {'Requests':<8} {'Success':<8} {'Created':<20}")
        click.echo("-" * 120)
        
        for job in jobs[:20 if not all else None]:  # Limit to 20 unless --all
            created = job.created_at.strftime("%Y-%m-%d %H:%M") if job.created_at else "Unknown"
            success_rate = f"{job.success_rate:.1%}" if job.results else "N/A"
            
            click.echo(f"{job.job_id:<36} {job.display_name[:29]:<30} {job.status.value:<12} {job.total_requests:<8} {success_rate:<8} {created:<20}")
        
        if len(jobs) > 20 and not all:
            click.echo(f"\n... and {len(jobs) - 20} more jobs (use --all to see all)")


@batch.command()
@click.argument("job_id")
@click.option("--wait", is_flag=True, help="Wait for completion if not finished")
@click.option("--output-dir", type=click.Path(path_type=Path), help="Directory to save results")
@click.pass_context
def results(ctx, job_id, wait, output_dir):
    """Retrieve results from a completed batch job."""
    client = ctx.obj["client"]
    
    job = client.get_job(job_id)
    if not job:
        click.echo(f"❌ Job {job_id} not found")
        return
    
    # Wait for completion if requested
    if wait and not job.is_complete:
        click.echo("⏳ Waiting for job completion...")
        job = client.wait_for_completion(job)
    
    # Check if job is completed
    if job.status != BatchJobStatus.COMPLETED:
        click.echo(f"❌ Job {job_id} is not completed (status: {job.status.value})")
        if job.error_message:
            click.echo(f"   Error: {job.error_message}")
        return
    
    # Retrieve results if not already done
    if not job.results:
        click.echo("📥 Retrieving results...")
        job = client.retrieve_results(job)
    
    # Display results summary
    click.echo(f"✅ Job {job_id} completed successfully!")
    click.echo(f"   Total requests: {job.total_requests}")
    click.echo(f"   Successful: {sum(1 for r in job.results if r.success)}")
    click.echo(f"   Failed: {sum(1 for r in job.results if not r.success)}")
    click.echo(f"   Success rate: {job.success_rate:.1%}")
    
    # Save results if output directory specified
    if output_dir:
        output_dir.mkdir(parents=True, exist_ok=True)
        
        for result in job.results:
            if result.success and result.extracted_data:
                result_file = output_dir / f"{result.request_id}.json"
                with open(result_file, "w", encoding="utf-8") as f:
                    json.dump(result.extracted_data, f, indent=2, ensure_ascii=False)
        
        click.echo(f"💾 Saved {sum(1 for r in job.results if r.success)} results to {output_dir}")


@batch.command()
@click.argument("job_id")
@click.option("--force", is_flag=True, help="Force deletion without confirmation")
@click.pass_context
def delete(ctx, job_id, force):
    """Delete a batch job."""
    client = ctx.obj["client"]
    
    job = client.get_job(job_id)
    if not job:
        click.echo(f"❌ Job {job_id} not found")
        return
    
    if not force:
        click.confirm(f"Delete job {job_id} ({job.display_name})?", abort=True)
    
    if client.delete_job(job_id):
        click.echo(f"✅ Deleted job {job_id}")
    else:
        click.echo(f"❌ Failed to delete job {job_id}")


def _display_job_details(job):
    """Display detailed information about a job."""
    click.echo(f"Job ID: {job.job_id}")
    click.echo(f"Display Name: {job.display_name}")
    click.echo(f"Status: {job.status.value}")
    click.echo(f"Total Requests: {job.total_requests}")
    
    if job.created_at:
        click.echo(f"Created: {job.created_at.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    
    if job.started_at:
        click.echo(f"Started: {job.started_at.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    
    if job.completed_at:
        click.echo(f"Completed: {job.completed_at.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    
    if job.duration:
        click.echo(f"Duration: {job.duration:.1f} seconds")
    
    if job.vertex_job_name:
        click.echo(f"Vertex AI Job: {job.vertex_job_name}")
    
    if job.input_gcs_path:
        click.echo(f"Input: {job.input_gcs_path}")
    
    if job.output_gcs_path:
        click.echo(f"Output: {job.output_gcs_path}")
    
    if job.results:
        successful = sum(1 for r in job.results if r.success)
        failed = sum(1 for r in job.results if not r.success)
        click.echo(f"Results: {successful} successful, {failed} failed ({job.success_rate:.1%} success rate)")
    
    if job.error_message:
        click.echo(f"Error: {job.error_message}")


if __name__ == "__main__":
    batch()
