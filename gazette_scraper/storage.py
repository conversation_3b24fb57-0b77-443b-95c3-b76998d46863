"""Storage management for local filesystem and optional GCS."""

from __future__ import annotations

import csv
import logging
from typing import TYPE_CHECKING, Any

if TYPE_CHECKING:
    from pathlib import Path

    from .models import GazetteFile

logger = logging.getLogger(__name__)


class LocalStorage:
    """Local filesystem storage manager."""

    def __init__(self, output_dir: Path):
        self.output_dir = output_dir
        self.output_dir.mkdir(parents=True, exist_ok=True)

    def get_file_path(self, file: GazetteFile) -> Path:
        """Generate local file path for a gazette file."""
        # Create year/month subdirectory
        year_month_dir = self.output_dir / str(file.year) / f"{file.month:02d}"
        year_month_dir.mkdir(parents=True, exist_ok=True)

        # Generate filename with date prefix if available
        if file.pub_date:
            date_prefix = file.pub_date.strftime("%Y-%m-%d")
            filename = f"{date_prefix}_{file.filename}"
        else:
            filename = file.filename

        # Ensure unique filename
        base_path = year_month_dir / filename
        counter = 1
        while base_path.exists():
            name_parts = filename.rsplit(".", 1)
            if len(name_parts) == 2:
                base_name, ext = name_parts
                new_filename = f"{base_name}_{counter}.{ext}"
            else:
                new_filename = f"{filename}_{counter}"
            base_path = year_month_dir / new_filename
            counter += 1

        return base_path

    def file_exists(self, file_path: Path) -> bool:
        """Check if a file exists locally."""
        return file_path.exists()

    def ensure_dirs(self, out_dir: Path) -> None:
        """Ensure output directory exists."""
        out_dir.mkdir(parents=True, exist_ok=True)

    def save_pdf(self, content: bytes, dst_path: Path) -> str:
        """Save PDF content and return SHA256."""
        import hashlib

        # Ensure directory exists
        dst_path.parent.mkdir(parents=True, exist_ok=True)

        # Calculate SHA256 while writing
        sha256_hash = hashlib.sha256()
        sha256_hash.update(content)

        # Write file
        with open(dst_path, "wb") as f:
            f.write(content)

        return sha256_hash.hexdigest()


class ManifestWriter:
    """Manages the CSV manifest of downloaded files."""

    def __init__(self, manifest_path: Path):
        self.manifest_path = manifest_path
        self.fieldnames = [
            "year",
            "month",
            "title",
            "filename",
            "pub_date",
            "sha256",
            "source_url",
            "download_url",
            "local_path",
            "size",
            "modified_date",
            "supabase_upserted",
            "document_type",
            "subject_category",
            "keywords",
        ]

    def write_header(self) -> None:
        """Write CSV header if file doesn't exist."""
        if not self.manifest_path.exists():
            with open(self.manifest_path, "w", newline="", encoding="utf-8") as f:
                writer = csv.DictWriter(f, fieldnames=self.fieldnames)
                writer.writeheader()

    def append_file(self, file: GazetteFile, local_path: Path, sha256: str, supabase_upserted: bool = False) -> None:
        """Append a file record to the manifest."""
        self.write_header()

        record = {
            "year": file.year,
            "month": file.month,
            "title": file.title,
            "filename": file.filename,
            "pub_date": file.pub_date.isoformat() if file.pub_date else "",
            "sha256": sha256,
            "source_url": str(file.source_url),
            "download_url": str(file.download_url),
            "local_path": str(local_path),
            "size": file.size_str or "",
            "modified_date": file.modified_date.isoformat()
            if file.modified_date
            else "",
            "supabase_upserted": supabase_upserted,
            "document_type": file.document_type.value,
            "subject_category": file.subject_category.value,
            "keywords": "|".join(file.keywords),
        }

        with open(self.manifest_path, "a", newline="", encoding="utf-8") as f:
            writer = csv.DictWriter(f, fieldnames=self.fieldnames)
            writer.writerow(record)

        logger.debug(f"Added to manifest: {file.filename}")


class GCSStorage:
    """Google Cloud Storage manager (optional)."""

    def __init__(self, bucket_name: str, credentials_path: Path | None = None):
        self.bucket_name = bucket_name
        self.credentials_path = credentials_path
        self._client = None
        self._bucket = None

    @property
    def client(self) -> Any:  # type: ignore[explicit-any]
        """Lazy load GCS client."""
        if self._client is None:
            try:
                from google.cloud import storage

                if self.credentials_path:
                    self._client = storage.Client.from_service_account_json(
                        str(self.credentials_path)
                    )
                else:
                    self._client = storage.Client()
            except ImportError as err:
                raise ImportError(
                    "google-cloud-storage is required for GCS support. "
                    "Install with: pip install google-cloud-storage"
                ) from err
        return self._client

    @property
    def bucket(self) -> Any:  # type: ignore[explicit-any]
        """Lazy load GCS bucket."""
        if self._bucket is None:
            self._bucket = self.client.bucket(self.bucket_name)
        return self._bucket

    def upload_file(self, local_path: Path, remote_path: str) -> bool:
        """Upload a file to GCS."""
        try:
            blob = self.bucket.blob(remote_path)
            blob.upload_from_filename(str(local_path))
            logger.info(f"Uploaded to GCS: {remote_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to upload {local_path} to GCS: {e}")
            return False

    def file_exists(self, remote_path: str) -> bool:
        """Check if a file exists in GCS."""
        try:
            blob = self.bucket.blob(remote_path)
            return bool(blob.exists())
        except Exception as e:
            logger.warning(f"Error checking GCS file existence: {e}")
            return False
