"""Data models for the gazette scraper."""

from __future__ import annotations

import re
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import TypedDict

from pydantic import BaseModel, Field, HttpUrl, field_validator


class FileStatsDict(TypedDict):
    """Type definition for file statistics dictionary."""

    filename: str
    year: int
    month: int
    size_str: str | None
    downloaded_at: str | None


class StatsDict(TypedDict):
    """Type definition for scraping statistics dictionary."""

    total_downloaded: int
    total_discovered: int
    total_failed: int
    downloaded_by_year: dict[int, int]
    discovered_by_year: dict[int, int]
    downloaded_by_month: dict[str, int]
    discovered_by_month: dict[str, int]
    errors_by_status: dict[int, int]
    downloaded_last_24h: int
    discovered_last_24h: int


class FolderType(str, Enum):
    """Types of folders in the gazette hierarchy."""

    YEAR = "year"
    MONTH = "month"


class DiscoveryType(str, Enum):
    """Types of items that can be discovered."""

    YEAR = "year"
    MONTH = "month"
    FILE = "file"


class DocumentType(str, Enum):
    """Types of legal documents in the gazette."""

    NAMES_REGISTRATION = "names_registration"  # Amazina
    COOPERATIVE = "cooperative"  # Cooperative registrations
    APPOINTMENTS = "appointments"  # Abayobozi (leadership appointments)
    FINANCIAL_REGULATIONS = "financial_regulations"  # BNR, financial statements
    LAND_USE_PLANS = "land_use_plans"  # Igishushanyo mbonera cy'imikoreshereze
    SPECIAL_DECREE = "special_decree"  # Idasanzwe (special/extraordinary)
    INDUSTRIAL_PROPERTY = "industrial_property"  # RDB Industrial Property Journal
    BUDGET = "budget"  # Ingengo y'imari (budget)
    ORGANIZATIONS = "organizations"  # FBO, associations
    TAX_REGULATIONS = "tax_regulations"  # TVA, tax-related documents
    STATUTES = "statutes"  # Sitati (organizational statutes)
    GENERAL = "general"  # Other/unclassified gazette content


class SubjectCategory(str, Enum):
    """Subject matter categories for legal documents."""

    CORPORATE = "corporate"  # Business, cooperatives, company registrations
    ADMINISTRATIVE = "administrative"  # Government appointments, organizational matters
    FINANCIAL = "financial"  # Banking, taxation, budget, financial regulations
    LAND_PLANNING = "land_planning"  # Land use, urban planning, zoning
    INTELLECTUAL_PROPERTY = "intellectual_property"  # Patents, trademarks, IP
    CIVIL_REGISTRATION = "civil_registration"  # Names, civil status
    REGULATORY = "regulatory"  # General regulations and rules
    JUDICIAL = "judicial"  # Court-related, legal procedures
    INTERNATIONAL = "international"  # Treaties, international agreements
    CASELAW = "caselaw"  # Court decisions and judgments
    OTHER = "other"  # Miscellaneous or unclassified


class CaseLawCategory(str, Enum):
    """Specific categories for case-law documents."""

    CIVIL = "civil"  # Civil cases
    CRIMINAL = "criminal"  # Criminal cases
    COMMERCIAL = "commercial"  # Commercial disputes
    ADMINISTRATIVE = "administrative"  # Administrative law cases
    CONSTITUTIONAL = "constitutional"  # Constitutional law cases
    FAMILY = "family"  # Family law cases
    LABOR = "labor"  # Labor and employment cases
    TAX = "tax"  # Tax law cases
    LAND = "land"  # Land and property cases
    APPEAL = "appeal"  # Appeal cases
    OTHER = "other"  # Other case types


class Folder(BaseModel):
    """Generic folder representation for multi-level traversal."""

    name: str
    href: str
    folder_type: FolderType
    year: int | None = None
    month: int | None = None

    @field_validator("year")
    @classmethod
    def validate_year(cls, v: int | None) -> int | None:
        if v is not None and (v < 1990 or v > 2030):
            raise ValueError("Year must be between 1990 and 2030")
        return v

    @field_validator("month")
    @classmethod
    def validate_month(cls, v: int | None) -> int | None:
        if v is not None and (v < 1 or v > 12):
            raise ValueError("Month must be between 1 and 12")
        return v


class FileItem(BaseModel):
    """Raw file item from parsing before processing into GazetteFile."""

    title: str
    href: str
    size_bytes: int | None = None
    size_str: str | None = None  # Raw size string from HTML
    modified: str | None = None  # Raw modified date string
    listing_url: str  # URL where this file was discovered


class GazetteFile(BaseModel):
    """Represents a single gazette PDF file."""

    # Schema versioning
    schema_version: str = Field(default="1.0.0", description="Schema version")

    title: str
    filename: str
    issue_title: str  # Normalized title
    size_bytes: int | None = None
    size_str: str | None = None  # Original size string
    modified_date: datetime | None = None
    pub_date: datetime | None = None
    download_url: HttpUrl  # dumpFile URL
    source_url: HttpUrl  # Same as download_url for compatibility
    listing_url: HttpUrl  # Page where file was discovered
    year: int
    month: int
    month_folder: str | None = None  # Name of the month folder for grouping
    sha256: str | None = None
    local_path: Path | None = None
    discovered_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Document classification fields
    document_type: DocumentType = Field(default=DocumentType.GENERAL, description="Type of legal document")
    subject_category: SubjectCategory = Field(default=SubjectCategory.OTHER, description="Subject matter category")
    keywords: list[str] = Field(default_factory=list, description="Extracted keywords from title")

    @field_validator("issue_title")
    @classmethod
    def validate_issue_title(cls, v: str) -> str:
        if not v or not v.strip():
            raise ValueError("issue_title cannot be empty")
        # Normalize whitespace
        return " ".join(v.split())

    @field_validator("listing_url")
    @classmethod
    def validate_listing_url(cls, v: HttpUrl) -> HttpUrl:
        url_str = str(v)
        if not (
            "minijust.gov.rw" in url_str
            or "localhost" in url_str
            or "example.com" in url_str
        ):
            raise ValueError(
                "listing_url must be from minijust.gov.rw domain (or test domains)"
            )
        return v

    @field_validator("source_url")
    @classmethod
    def validate_source_url(cls, v: HttpUrl) -> HttpUrl:
        url_str = str(v)
        if not (
            "minijust.gov.rw" in url_str
            or "localhost" in url_str
            or "example.com" in url_str
        ):
            raise ValueError(
                "source_url must be from minijust.gov.rw domain (or test domains)"
            )
        return v

    @field_validator("year")
    @classmethod
    def validate_year(cls, v: int) -> int:
        if v < 1990 or v > 2030:
            raise ValueError("Year must be between 1990 and 2030")
        return v

    @field_validator("month")
    @classmethod
    def validate_month(cls, v: int) -> int:
        if v < 1 or v > 12:
            raise ValueError("Month must be between 1 and 12")
        return v

    @field_validator("sha256")
    @classmethod
    def validate_sha256(cls, v: str | None) -> str | None:
        if v is not None and not re.match(r"^[a-f0-9]{64}$", v):
            raise ValueError("sha256 must be a 64-character hexadecimal string")
        return v


class GazetteFolder(BaseModel):
    """Represents a folder containing gazette files."""

    year: int
    month: int
    folder_path: str
    folder_url: HttpUrl
    files: list[GazetteFile] = Field(default_factory=list)


class Manifest(BaseModel):
    """Complete manifest of discovered files."""

    files: list[GazetteFile] = Field(default_factory=list)
    total_discovered: int = 0
    by_year: dict[int, int] = Field(default_factory=dict)
    by_month: dict[str, int] = Field(default_factory=dict)  # "YYYY-MM" -> count
    discovered_at: datetime = Field(default_factory=datetime.utcnow)


class ScrapingConfig(BaseModel):
    """Configuration for the scraper."""

    base_url: str = "https://www.minijust.gov.rw"
    rate_limit: float = 1.0  # requests per second
    max_retries: int = 3
    output_dir: Path = Path("./data")
    db_path: Path = Path("./scrape_state.db")
    log_dir: Path = Path("./logs")
    max_threads: int = 4
    user_agent: str = (
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
    )
    accept_language: str = "rw,en,fr"
    proxies: list[str] | None = None
    since_year: int | None = None
    gcs_bucket: str | None = None
    gcs_project_id: str | None = None
    gcs_prefix: str = "gazette_pdfs/"  # folder prefix inside the bucket
    dry_run: bool = False
    jitter_range: float = 0.5  # Random jitter factor for rate limiting
    crawler_depth: int = 1  # 1=year-level, 2=month-level traversal
    # Supabase configuration
    supabase_url: str | None = None
    supabase_key: str | None = None


class ScrapeResult(BaseModel):
    """Result of a scraping operation."""

    total_discovered: int = 0
    downloaded: int = 0
    skipped: int = 0
    errors: int = 0
    start_time: datetime
    end_time: datetime | None = None
    error_messages: list[str] = Field(default_factory=list)
