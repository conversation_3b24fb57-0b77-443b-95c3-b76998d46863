"""GraphRAG integration module for Rwanda Gazette processing."""

from .byog_mapper import <PERSON>Y<PERSON><PERSON><PERSON>per
from .embeddings import VoyageEmbedder

__all__ = ["BYOGMapper", "VoyageEmbedder"]

# GraphRAG query service will be available after GraphRAG is installed
def get_query_service():
    """Get GraphRAG query service if available."""
    try:
        from .query_service import GraphRAGQueryService
        return GraphRAGQueryService
    except ImportError:
        return None

GraphRAGQueryService = get_query_service()
