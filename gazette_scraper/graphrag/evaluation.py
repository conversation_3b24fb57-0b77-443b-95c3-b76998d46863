"""RAGAS evaluation for GraphRAG query quality assessment."""

from __future__ import annotations

import asyncio
import json
import logging
from pathlib import Path
from typing import Any

import pandas as pd

logger = logging.getLogger(__name__)

try:
    from ragas import evaluate
    from ragas.metrics import (
        answer_relevancy,
        context_precision,
        context_recall,
        faithfulness,
    )
    from datasets import Dataset
    RAGAS_AVAILABLE = True
except ImportError:
    RAGAS_AVAILABLE = False
    logger.warning("RAGAS not available. Install with: pip install ragas")


class GraphRAGEvaluator:
    """Evaluates GraphRAG query performance using RAGAS metrics."""
    
    def __init__(self, query_service: Any):
        """Initialize evaluator.
        
        Args:
            query_service: GraphRAGQueryService instance
        """
        if not RAGAS_AVAILABLE:
            raise ImportError("RAGAS is required for evaluation. Install with: pip install ragas")
        
        self.query_service = query_service
        
    async def evaluate_queries(
        self,
        test_queries: list[dict[str, Any]],
        search_types: list[str] = ["local", "global", "drift"],
        output_path: Path | None = None
    ) -> dict[str, Any]:
        """Evaluate a set of test queries.
        
        Args:
            test_queries: List of test queries with expected answers
            search_types: Types of search to evaluate
            output_path: Path to save evaluation results
            
        Returns:
            Evaluation results
        """
        logger.info(f"Evaluating {len(test_queries)} queries with {len(search_types)} search types")
        
        all_results = {}
        
        for search_type in search_types:
            logger.info(f"Evaluating {search_type} search...")
            
            # Prepare data for RAGAS
            evaluation_data = await self._prepare_evaluation_data(test_queries, search_type)
            
            if not evaluation_data:
                logger.warning(f"No evaluation data for {search_type} search")
                continue
            
            # Run RAGAS evaluation
            results = self._run_ragas_evaluation(evaluation_data)
            all_results[search_type] = results
            
            logger.info(f"Completed {search_type} evaluation")
        
        # Save results if path provided
        if output_path:
            self._save_results(all_results, output_path)
        
        return all_results
    
    async def _prepare_evaluation_data(
        self,
        test_queries: list[dict[str, Any]],
        search_type: str
    ) -> list[dict[str, Any]]:
        """Prepare evaluation data for RAGAS.
        
        Args:
            test_queries: Test queries
            search_type: Type of search to perform
            
        Returns:
            Evaluation data in RAGAS format
        """
        evaluation_data = []
        
        for query_data in test_queries:
            query = query_data["question"]
            expected_answer = query_data.get("expected_answer", "")
            
            try:
                # Perform search
                if search_type == "global":
                    result = await self.query_service.global_search(query)
                elif search_type == "local":
                    result = await self.query_service.local_search(
                        query,
                        language=query_data.get("language"),
                        max_results=10
                    )
                elif search_type == "drift":
                    result = await self.query_service.drift_search(query)
                else:
                    continue
                
                # Extract context and answer
                answer = result.get("response", "")
                contexts = self._extract_contexts(result)
                
                if answer and contexts:
                    evaluation_data.append({
                        "question": query,
                        "answer": answer,
                        "contexts": contexts,
                        "ground_truth": expected_answer,
                        "search_type": search_type,
                        "metadata": query_data.get("metadata", {})
                    })
                
            except Exception as e:
                logger.error(f"Error processing query '{query}': {e}")
                continue
        
        return evaluation_data
    
    def _extract_contexts(self, search_result: dict[str, Any]) -> list[str]:
        """Extract context strings from search result.
        
        Args:
            search_result: Search result from query service
            
        Returns:
            List of context strings
        """
        contexts = []
        
        # Extract from different result types
        if "context" in search_result:
            context_data = search_result["context"]
            
            if isinstance(context_data, list):
                for item in context_data:
                    if isinstance(item, dict) and "text" in item:
                        contexts.append(item["text"])
                    elif isinstance(item, str):
                        contexts.append(item)
        
        # Extract from global context
        if "global_context" in search_result:
            global_context = search_result["global_context"]
            if isinstance(global_context, list):
                contexts.extend([str(item) for item in global_context])
        
        # Extract from local context
        if "local_context" in search_result:
            local_context = search_result["local_context"]
            if isinstance(local_context, list):
                for item in local_context:
                    if isinstance(item, dict) and "text" in item:
                        contexts.append(item["text"])
        
        return contexts[:10]  # Limit to top 10 contexts
    
    def _run_ragas_evaluation(self, evaluation_data: list[dict[str, Any]]) -> dict[str, Any]:
        """Run RAGAS evaluation on prepared data.
        
        Args:
            evaluation_data: Prepared evaluation data
            
        Returns:
            RAGAS evaluation results
        """
        if not evaluation_data:
            return {"error": "No evaluation data"}
        
        try:
            # Convert to RAGAS dataset format
            dataset_dict = {
                "question": [item["question"] for item in evaluation_data],
                "answer": [item["answer"] for item in evaluation_data],
                "contexts": [item["contexts"] for item in evaluation_data],
                "ground_truth": [item["ground_truth"] for item in evaluation_data],
            }
            
            dataset = Dataset.from_dict(dataset_dict)
            
            # Define metrics
            metrics = [
                context_precision,
                context_recall,
                faithfulness,
                answer_relevancy,
            ]
            
            # Run evaluation
            result = evaluate(
                dataset=dataset,
                metrics=metrics,
            )
            
            # Convert to dictionary
            results = {
                "scores": dict(result),
                "num_queries": len(evaluation_data),
                "metadata": {
                    "search_types": list(set(item["search_type"] for item in evaluation_data)),
                    "languages": list(set(
                        item["metadata"].get("language", "unknown") 
                        for item in evaluation_data
                    )),
                }
            }
            
            return results
            
        except Exception as e:
            logger.error(f"RAGAS evaluation failed: {e}")
            return {"error": str(e)}
    
    def _save_results(self, results: dict[str, Any], output_path: Path) -> None:
        """Save evaluation results to file.
        
        Args:
            results: Evaluation results
            output_path: Output file path
        """
        try:
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Evaluation results saved to {output_path}")
            
        except Exception as e:
            logger.error(f"Failed to save results: {e}")


def create_test_queries() -> list[dict[str, Any]]:
    """Create a set of test queries for evaluation.
    
    Returns:
        List of test queries with expected answers
    """
    return [
        {
            "question": "What are the requirements for cooperative registration in Rwanda?",
            "expected_answer": "Cooperatives must submit registration documents including articles of association, member lists, and meet minimum membership requirements.",
            "language": "en",
            "metadata": {"topic": "cooperative_law", "difficulty": "medium"}
        },
        {
            "question": "Quelles sont les procédures d'enregistrement foncier au Rwanda?",
            "expected_answer": "L'enregistrement foncier nécessite la soumission de documents de propriété, l'évaluation du terrain, et l'approbation des autorités locales.",
            "language": "fr",
            "metadata": {"topic": "land_law", "difficulty": "medium"}
        },
        {
            "question": "Ni iki gikenewe mu kwandikisha ubwiyunge mu Rwanda?",
            "expected_answer": "Kwandikisha ubwiyunge bisaba inyandiko z'ubwiyunge, urutonde rw'abanyamuryango, n'ibisabwa by'ibanze.",
            "language": "rw",
            "metadata": {"topic": "cooperative_law", "difficulty": "medium"}
        },
        {
            "question": "Who was appointed as Minister of Justice in 2023?",
            "expected_answer": "Information about ministerial appointments should be found in official gazette announcements.",
            "language": "en",
            "metadata": {"topic": "appointments", "difficulty": "easy"}
        },
        {
            "question": "What legal changes were made to land use regulations in recent years?",
            "expected_answer": "Recent changes include updates to land registration procedures, zoning regulations, and environmental protection requirements.",
            "language": "en",
            "metadata": {"topic": "land_law", "difficulty": "hard"}
        },
        {
            "question": "How has cooperative law evolved in Rwanda since 2020?",
            "expected_answer": "Cooperative law has seen updates in registration procedures, governance requirements, and financial reporting standards.",
            "language": "en",
            "metadata": {"topic": "cooperative_law", "difficulty": "hard"}
        }
    ]


async def run_evaluation_example():
    """Example of running GraphRAG evaluation."""
    from ..embeddings import VoyageEmbedder
    from ..query_service import GraphRAGQueryService
    
    # Initialize services
    embedder = VoyageEmbedder()
    query_service = GraphRAGQueryService(
        graphrag_project_root=Path("./ailex_rwanda"),
        voyage_embedder=embedder
    )
    
    # Create evaluator
    evaluator = GraphRAGEvaluator(query_service)
    
    # Get test queries
    test_queries = create_test_queries()
    
    # Run evaluation
    results = await evaluator.evaluate_queries(
        test_queries=test_queries,
        search_types=["local", "global"],
        output_path=Path("./evaluation_results.json")
    )
    
    # Print summary
    print("Evaluation Results Summary:")
    print("=" * 40)
    
    for search_type, result in results.items():
        if "error" in result:
            print(f"{search_type}: Error - {result['error']}")
            continue
        
        scores = result.get("scores", {})
        print(f"\n{search_type.upper()} Search:")
        print(f"  Context Precision: {scores.get('context_precision', 'N/A'):.3f}")
        print(f"  Context Recall: {scores.get('context_recall', 'N/A'):.3f}")
        print(f"  Faithfulness: {scores.get('faithfulness', 'N/A'):.3f}")
        print(f"  Answer Relevancy: {scores.get('answer_relevancy', 'N/A'):.3f}")
        print(f"  Queries Evaluated: {result.get('num_queries', 0)}")
    
    # Cleanup
    embedder.close()


if __name__ == "__main__":
    asyncio.run(run_evaluation_example())
