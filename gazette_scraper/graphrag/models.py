"""Data models for GraphRAG BYOG integration."""

from __future__ import annotations

import uuid
from datetime import datetime
from typing import Any

from pydantic import BaseModel, Field


def generate_id() -> str:
    """Generate a unique ID for GraphRAG entities."""
    return str(uuid.uuid4())


class TextUnit(BaseModel):
    """Text unit for GraphRAG BYOG."""
    
    id: str = Field(default_factory=generate_id)
    text: str
    n_tokens: int
    document_ids: list[str]
    metadata: dict[str, Any] = Field(default_factory=dict)


class Entity(BaseModel):
    """Entity for GraphRAG BYOG."""
    
    id: str = Field(default_factory=generate_id)
    title: str
    description: str
    type: str  # Act, Article, Institution, Person, Topic, Date, etc.
    text_unit_ids: list[str] = Field(default_factory=list)
    metadata: dict[str, Any] = Field(default_factory=dict)


class Relationship(BaseModel):
    """Relationship for GraphRAG BYOG."""
    
    id: str = Field(default_factory=generate_id)
    source: str  # source entity id
    target: str  # target entity id
    description: str
    weight: float = 1.0
    text_unit_ids: list[str] = Field(default_factory=list)
    metadata: dict[str, Any] = Field(default_factory=dict)


class BYOGData(BaseModel):
    """Complete BYOG dataset."""
    
    text_units: list[TextUnit] = Field(default_factory=list)
    entities: list[Entity] = Field(default_factory=list)
    relationships: list[Relationship] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=datetime.utcnow)


class ChunkEmbedding(BaseModel):
    """Chunk embedding for MongoDB Atlas."""
    
    chunk_id: str
    tenant_id: str
    doc_id: str
    text: str
    vector: list[float]
    language: str
    gazette_date: str
    law_type: str | None = None
    article_id: str | None = None
    metadata: dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.utcnow)


# Legal relationship weights for GraphRAG community detection
RELATIONSHIP_WEIGHTS = {
    "repeals": 3.0,
    "amends": 2.0,
    "same_as_translation": 2.0,
    "cites": 1.0,
    "references": 1.0,
    "defines": 1.5,
    "establishes": 1.5,
    "regulates": 1.0,
    "applies_to": 0.8,
    "related_to": 0.5,
}

# Entity types for legal documents
ENTITY_TYPES = {
    "Act": "Legal act or law",
    "Article": "Article within a legal document",
    "Institution": "Government institution or organization",
    "Person": "Individual person (minister, official, etc.)",
    "Court": "Judicial court or tribunal",
    "Topic": "Legal topic or subject matter",
    "Date": "Important date or deadline",
    "Location": "Geographic location",
    "Procedure": "Legal procedure or process",
    "Penalty": "Legal penalty or sanction",
    "Requirement": "Legal requirement or obligation",
}
