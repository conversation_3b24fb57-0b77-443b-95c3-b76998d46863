"""GraphRAG configuration and setup utilities."""

from __future__ import annotations

import os
import shutil
import yaml
from pathlib import Path
from typing import Any

import logging

logger = logging.getLogger(__name__)


class GraphRAGConfig:
    """Manages GraphRAG configuration and project setup."""
    
    def __init__(self, project_root: Path):
        """Initialize GraphRAG configuration.
        
        Args:
            project_root: Root directory for GraphRAG project
        """
        self.project_root = Path(project_root)
        self.settings_path = self.project_root / "settings.yaml"
        self.env_path = self.project_root / ".env"
        
    def initialize_project(self, force: bool = False) -> None:
        """Initialize GraphRAG project structure.
        
        Args:
            force: Whether to overwrite existing configuration
        """
        if self.project_root.exists() and not force:
            logger.info(f"GraphRAG project already exists at {self.project_root}")
            return
        
        # Create project directory
        self.project_root.mkdir(parents=True, exist_ok=True)
        
        # Create settings.yaml
        self._create_settings_file()
        
        # Create .env file
        self._create_env_file()
        
        # Create output directory
        (self.project_root / "output").mkdir(exist_ok=True)
        
        logger.info(f"Initialized GraphRAG project at {self.project_root}")
    
    def _create_settings_file(self) -> None:
        """Create GraphRAG settings.yaml file."""
        settings = {
            "models": {
                "default_chat_model": {
                    "type": "openai_chat",
                    "model": "gpt-4o",
                    "api_key": "${GRAPHRAG_API_KEY}",
                    "temperature": 0,
                    "max_tokens": 4000,
                }
            },
            "workflows": [
                "create_communities",
                "create_community_reports"
            ],
            "output": {
                "type": "file",
                "base_dir": "output"
            },
            "snapshots": {
                "graphml": True
            },
            "community_reports": {
                "max_length": 2000,
                "strategy": {
                    "type": "graph_intelligence",
                }
            },
            "entity_extraction": {
                "strategy": {
                    "type": "graph_intelligence",
                }
            },
            "summarize_descriptions": {
                "strategy": {
                    "type": "graph_intelligence",
                }
            },
            "claim_extraction": {
                "enabled": False
            },
            "embed_graph": {
                "enabled": False
            },
            "umap": {
                "enabled": False
            }
        }
        
        with open(self.settings_path, 'w') as f:
            yaml.dump(settings, f, default_flow_style=False, indent=2)
        
        logger.info(f"Created settings file: {self.settings_path}")
    
    def _create_env_file(self) -> None:
        """Create GraphRAG .env file."""
        env_content = f"""# GraphRAG Environment Configuration
GRAPHRAG_API_KEY={os.getenv('OPENAI_API_KEY', 'your-openai-api-key-here')}
"""
        
        with open(self.env_path, 'w') as f:
            f.write(env_content)
        
        logger.info(f"Created environment file: {self.env_path}")
    
    def enable_vector_store(self, vector_store_type: str = "lancedb") -> None:
        """Enable vector store in GraphRAG configuration.
        
        Args:
            vector_store_type: Type of vector store (lancedb, etc.)
        """
        if not self.settings_path.exists():
            raise FileNotFoundError(f"Settings file not found: {self.settings_path}")
        
        with open(self.settings_path, 'r') as f:
            settings = yaml.safe_load(f)
        
        # Add vector store configuration
        if vector_store_type == "lancedb":
            settings["vector_store"] = {
                "type": "lancedb",
                "db_uri": "storage/lancedb",
                "container_name": "default"
            }
            
            # Add text embedding workflow
            if "generate_text_embeddings" not in settings["workflows"]:
                settings["workflows"].append("generate_text_embeddings")
        
        with open(self.settings_path, 'w') as f:
            yaml.dump(settings, f, default_flow_style=False, indent=2)
        
        logger.info(f"Enabled {vector_store_type} vector store")
    
    def copy_byog_files(self, byog_output_dir: Path) -> None:
        """Copy BYOG parquet files to GraphRAG output directory.
        
        Args:
            byog_output_dir: Directory containing BYOG parquet files
        """
        output_dir = self.project_root / "output"
        output_dir.mkdir(exist_ok=True)
        
        # Copy required parquet files
        required_files = ["text_units.parquet", "entities.parquet", "relationships.parquet"]
        
        for filename in required_files:
            src_path = byog_output_dir / filename
            dst_path = output_dir / filename
            
            if src_path.exists():
                shutil.copy2(src_path, dst_path)
                logger.info(f"Copied {filename} to GraphRAG output directory")
            else:
                logger.warning(f"BYOG file not found: {src_path}")
    
    def get_config_dict(self) -> dict[str, Any]:
        """Get GraphRAG configuration as dictionary.
        
        Returns:
            Configuration dictionary
        """
        if not self.settings_path.exists():
            raise FileNotFoundError(f"Settings file not found: {self.settings_path}")
        
        with open(self.settings_path, 'r') as f:
            return yaml.safe_load(f)


def create_graphrag_project(
    project_name: str = "ailex_rwanda",
    base_dir: Path | None = None,
    enable_vector_store: bool = False
) -> GraphRAGConfig:
    """Create a new GraphRAG project.
    
    Args:
        project_name: Name of the GraphRAG project
        base_dir: Base directory for the project (default: current directory)
        enable_vector_store: Whether to enable LanceDB vector store
        
    Returns:
        GraphRAG configuration instance
    """
    if base_dir is None:
        base_dir = Path.cwd()
    
    project_root = base_dir / project_name
    config = GraphRAGConfig(project_root)
    
    # Initialize project
    config.initialize_project()
    
    # Enable vector store if requested
    if enable_vector_store:
        config.enable_vector_store("lancedb")
    
    return config
