"""BYOG mapper to convert gazette JSON to GraphRAG parquet format."""

from __future__ import annotations

import json
import logging
import re
from pathlib import Path
from typing import Any

import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from tiktoken import get_encoding

from ..extractor.schema import ExtractedDocument
from .models import (
    ENTITY_TYPES,
    RELATIONSHIP_WEIGHTS,
    BYOGData,
    Entity,
    Relationship,
    TextUnit,
    generate_id,
)

logger = logging.getLogger(__name__)


class BYOGMapper:
    """Maps gazette JSON data to GraphRAG BYOG parquet format."""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 100):
        """Initialize the mapper.
        
        Args:
            chunk_size: Target chunk size in tokens
            chunk_overlap: Overlap between chunks in tokens
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.encoding = get_encoding("cl100k_base")  # GPT-4 tokenizer
        
    def process_gazette_files(
        self, 
        input_dir: Path, 
        output_dir: Path,
        tenant_id: str = "rwanda_gov"
    ) -> BYOGData:
        """Process all gazette JSON files in a directory.
        
        Args:
            input_dir: Directory containing gazette.json files
            output_dir: Directory to write parquet files
            tenant_id: Tenant identifier for multi-tenancy
            
        Returns:
            Complete BYOG dataset
        """
        logger.info(f"Processing gazette files from {input_dir}")
        
        byog_data = BYOGData()
        
        # Find all gazette.json files
        gazette_files = list(input_dir.rglob("gazette.json"))
        logger.info(f"Found {len(gazette_files)} gazette files")
        
        for gazette_file in gazette_files:
            try:
                with open(gazette_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Parse as ExtractedDocument
                doc = ExtractedDocument.model_validate(data)
                
                # Process this document
                doc_data = self._process_document(doc, tenant_id)
                
                # Merge into main dataset
                byog_data.text_units.extend(doc_data.text_units)
                byog_data.entities.extend(doc_data.entities)
                byog_data.relationships.extend(doc_data.relationships)
                
                logger.debug(f"Processed {gazette_file.name}")
                
            except Exception as e:
                logger.error(f"Error processing {gazette_file}: {e}")
                continue
        
        # Write parquet files
        self._write_parquet_files(byog_data, output_dir)
        
        logger.info(
            f"Generated BYOG data: {len(byog_data.text_units)} text units, "
            f"{len(byog_data.entities)} entities, {len(byog_data.relationships)} relationships"
        )
        
        return byog_data
    
    def _process_document(self, doc: ExtractedDocument, tenant_id: str) -> BYOGData:
        """Process a single gazette document."""
        byog_data = BYOGData()
        
        # Document metadata
        doc_id = f"{tenant_id}_{doc.document.source_filename}"
        gazette_date = doc.document.date_iso
        
        # Process each article
        for article in doc.articles:
            article_data = self._process_article(
                article, doc_id, gazette_date, tenant_id
            )
            
            byog_data.text_units.extend(article_data.text_units)
            byog_data.entities.extend(article_data.entities)
            byog_data.relationships.extend(article_data.relationships)
        
        # Add cross-language relationships
        self._add_translation_relationships(doc.articles, byog_data)
        
        return byog_data
    
    def _process_article(
        self, 
        article: Any, 
        doc_id: str, 
        gazette_date: str,
        tenant_id: str
    ) -> BYOGData:
        """Process a single article across all languages."""
        byog_data = BYOGData()
        
        article_id = f"article_{article.article_no}"
        
        # Process each language version
        for lang_code in ["rw", "en", "fr"]:
            lang_text = getattr(article, lang_code, None)
            if not lang_text or not lang_text.text:
                continue
                
            # Create chunks for this language version
            chunks = self._create_chunks(lang_text.text)
            
            for i, chunk_text in enumerate(chunks):
                # Create text unit
                text_unit = TextUnit(
                    text=chunk_text,
                    n_tokens=len(self.encoding.encode(chunk_text)),
                    document_ids=[doc_id],
                    metadata={
                        "tenant_id": tenant_id,
                        "language": lang_code,
                        "gazette_date": gazette_date,
                        "article_id": article_id,
                        "article_no": article.article_no,
                        "chunk_index": i,
                        "pages": lang_text.pages,
                    }
                )
                byog_data.text_units.append(text_unit)
                
                # Extract entities from this chunk
                entities = self._extract_entities(chunk_text, lang_code, text_unit.id)
                byog_data.entities.extend(entities)
        
        return byog_data
    
    def _create_chunks(self, text: str) -> list[str]:
        """Create overlapping chunks from text."""
        tokens = self.encoding.encode(text)
        
        if len(tokens) <= self.chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(tokens):
            end = min(start + self.chunk_size, len(tokens))
            chunk_tokens = tokens[start:end]
            chunk_text = self.encoding.decode(chunk_tokens)
            chunks.append(chunk_text)
            
            if end >= len(tokens):
                break
                
            start = end - self.chunk_overlap
        
        return chunks
    
    def _extract_entities(self, text: str, language: str, text_unit_id: str) -> list[Entity]:
        """Extract entities from text using pattern matching."""
        entities = []
        
        # Article references (e.g., "Article 5", "Ingingo ya 5")
        article_patterns = {
            "en": r"Article\s+(\d+)",
            "fr": r"Article\s+(\d+)",
            "rw": r"Ingingo\s+ya\s+(\w+)",
        }
        
        if language in article_patterns:
            for match in re.finditer(article_patterns[language], text, re.IGNORECASE):
                entities.append(Entity(
                    title=match.group(0),
                    description=f"Legal article reference in {language}",
                    type="Article",
                    text_unit_ids=[text_unit_id],
                    metadata={"language": language, "article_number": match.group(1)}
                ))
        
        # Institution patterns
        institution_patterns = [
            r"Ministry\s+of\s+\w+",
            r"Minisiteri\s+y'\w+",
            r"Ministère\s+de\s+\w+",
            r"Rwanda\s+\w+\s+Authority",
            r"National\s+\w+\s+Commission",
        ]
        
        for pattern in institution_patterns:
            for match in re.finditer(pattern, text, re.IGNORECASE):
                entities.append(Entity(
                    title=match.group(0),
                    description=f"Government institution mentioned in {language}",
                    type="Institution",
                    text_unit_ids=[text_unit_id],
                    metadata={"language": language}
                ))
        
        return entities
    
    def _add_translation_relationships(self, articles: list[Any], byog_data: BYOGData) -> None:
        """Add same_as_translation relationships between language versions."""
        # Group entities by article number and type
        entity_groups: dict[tuple[int, str], list[Entity]] = {}
        
        for entity in byog_data.entities:
            if "article_number" in entity.metadata:
                key = (entity.metadata["article_number"], entity.type)
                if key not in entity_groups:
                    entity_groups[key] = []
                entity_groups[key].append(entity)
        
        # Create translation relationships
        for entities in entity_groups.values():
            if len(entities) > 1:
                # Create relationships between all pairs
                for i, entity1 in enumerate(entities):
                    for entity2 in entities[i+1:]:
                        relationship = Relationship(
                            source=entity1.id,
                            target=entity2.id,
                            description=f"Translation relationship between {entity1.title} and {entity2.title}",
                            weight=RELATIONSHIP_WEIGHTS["same_as_translation"],
                            text_unit_ids=list(set(entity1.text_unit_ids + entity2.text_unit_ids)),
                            metadata={
                                "relationship_type": "same_as_translation",
                                "source_language": entity1.metadata.get("language"),
                                "target_language": entity2.metadata.get("language"),
                            }
                        )
                        byog_data.relationships.append(relationship)
    
    def _write_parquet_files(self, byog_data: BYOGData, output_dir: Path) -> None:
        """Write BYOG data to parquet files."""
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Text units
        text_units_df = pd.DataFrame([
            {
                "id": tu.id,
                "text": tu.text,
                "n_tokens": tu.n_tokens,
                "document_ids": tu.document_ids,
                **tu.metadata
            }
            for tu in byog_data.text_units
        ])
        
        text_units_path = output_dir / "text_units.parquet"
        text_units_df.to_parquet(text_units_path, index=False)
        logger.info(f"Wrote {len(text_units_df)} text units to {text_units_path}")
        
        # Entities
        entities_df = pd.DataFrame([
            {
                "id": e.id,
                "title": e.title,
                "description": e.description,
                "type": e.type,
                "text_unit_ids": e.text_unit_ids,
                **e.metadata
            }
            for e in byog_data.entities
        ])
        
        entities_path = output_dir / "entities.parquet"
        entities_df.to_parquet(entities_path, index=False)
        logger.info(f"Wrote {len(entities_df)} entities to {entities_path}")
        
        # Relationships
        relationships_df = pd.DataFrame([
            {
                "id": r.id,
                "source": r.source,
                "target": r.target,
                "description": r.description,
                "weight": r.weight,
                "text_unit_ids": r.text_unit_ids,
                **r.metadata
            }
            for r in byog_data.relationships
        ])
        
        relationships_path = output_dir / "relationships.parquet"
        relationships_df.to_parquet(relationships_path, index=False)
        logger.info(f"Wrote {len(relationships_df)} relationships to {relationships_path}")
