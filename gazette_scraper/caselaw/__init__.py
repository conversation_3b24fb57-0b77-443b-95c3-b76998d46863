"""Case-law scraper module for Amategeko portal."""

from .models import CaseLawFile, CourtNode, NavigationState
from .state import CaseLawState

# Import components that require external dependencies conditionally
__all__ = [
    "CaseLawFile",
    "CourtNode",
    "NavigationState",
    "CaseLawState",
]

try:
    from .navigator import CaseLawNavigator
    __all__.append("CaseLawNavigator")
except ImportError:
    pass

try:
    from .pipeline import CaseLawPipeline
    __all__.append("CaseLawPipeline")
except ImportError:
    pass