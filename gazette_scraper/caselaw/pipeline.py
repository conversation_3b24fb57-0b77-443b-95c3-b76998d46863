"""Main case-law scraping pipeline extending the existing scraper framework."""

from __future__ import annotations

import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from tqdm.asyncio import tqdm

from ..models import ScrapeResult
from ..storage import GCSStorage, LocalStorage
from ..supabase_client import log_error, upsert_file
from .downloader import CaseLawDownloader
from .models import (
    CaseLawConfig,
    CaseLawFile,
    CaseLawManifest,
    CourtNode,
    NavigationState,
    NavigationStatus,
    NodeType,
)
from .navigator import CaseLawNavigator
from .state import CaseLawState

logger = logging.getLogger(__name__)


class CaseLawPipeline:
    """Main pipeline for case-law scraping with tree navigation and PDF download."""

    def __init__(self, config: CaseLawConfig):
        self.config = config
        self.state = CaseLawState(self.config.output_dir / "caselaw_state.db")
        self.local_storage = LocalStorage(self.config.output_dir)

        # Initialize GCS if configured
        self.gcs_storage = None
        if hasattr(config, 'gcs_bucket') and config.gcs_bucket:
            self.gcs_storage = GCSStorage(config.gcs_bucket)
            logger.info(f"GCS storage initialized: bucket={config.gcs_bucket}")
        else:
            logger.info("GCS storage not configured - files will be stored locally only")

        self.navigator: Optional[CaseLawNavigator] = None
        self.downloader: Optional[CaseLawDownloader] = None

    async def __aenter__(self) -> CaseLawPipeline:
        """Async context manager entry."""
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Async context manager exit."""
        await self.close()

    async def start(self) -> None:
        """Initialize pipeline components."""
        logger.info("Starting case-law scraping pipeline")

        # Initialize navigator and downloader
        self.navigator = CaseLawNavigator(self.config)
        self.downloader = CaseLawDownloader(self.config, self.state)

        await self.navigator.start()
        await self.downloader.start()

        # Clean up old sessions
        self.state.cleanup_old_sessions()

    async def close(self) -> None:
        """Clean up pipeline resources."""
        logger.info("Closing case-law scraping pipeline")

        if self.downloader:
            await self.downloader.close()

        if self.navigator:
            await self.navigator.close()

    async def run_full_scrape(self, resume: bool = True) -> ScrapeResult:
        """Run the complete case-law scraping process."""
        start_time = datetime.now()
        result = ScrapeResult(start_time=start_time)

        try:
            logger.info("Starting full case-law scraping process")

            # Phase 1: Tree Discovery
            logger.info("Phase 1: Discovering court tree structure")
            court_nodes = await self.discover_tree_structure(resume=resume)
            logger.info(f"Discovered {len(court_nodes)} total nodes")

            # Phase 2: Case Discovery
            logger.info("Phase 2: Discovering cases from day nodes")
            case_files = await self.discover_all_cases(court_nodes)
            logger.info(f"Discovered {len(case_files)} total cases")

            result.total_discovered = len(case_files)

            # Phase 3: PDF Download
            logger.info("Phase 3: Downloading PDF files")
            downloaded, errors = await self.download_all_cases(case_files)

            result.downloaded = downloaded
            result.errors = errors
            result.skipped = len(case_files) - downloaded - errors

            # Phase 4: Generate manifest and report
            await self.generate_manifest()

        except Exception as e:
            logger.error(f"Pipeline error: {e}")
            result.errors += 1
            result.error_messages.append(f"Pipeline error: {e}")

        finally:
            result.end_time = datetime.now()
            self._log_pipeline_summary(result)

        return result

    async def discover_tree_structure(self, resume: bool = True) -> List[CourtNode]:
        """Discover the complete court tree structure."""
        logger.info("Starting tree structure discovery")

        # Check for resume
        if resume:
            existing_nodes = self.state.get_nodes_by_status(NavigationStatus.COMPLETED)
            if existing_nodes:
                logger.info(f"Found {len(existing_nodes)} existing nodes, checking for completion")

                # Check if discovery is complete
                pending_nodes = self.state.get_nodes_by_status(NavigationStatus.PENDING)
                if not pending_nodes:
                    logger.info("Tree discovery appears complete, using existing data")
                    return self._load_all_nodes()

        # Discover tree structure
        try:
            court_nodes = await self.navigator.discover_court_tree()

            # Save discovered nodes
            for node in court_nodes:
                self.state.save_court_node(node)

            # Update navigation session
            session = NavigationState(
                session_id=self.navigator.navigation_state.session_id,
                total_nodes_discovered=len(court_nodes),
                total_nodes_expanded=len([n for n in court_nodes if n.status == NavigationStatus.EXPANDED]),
            )
            self.state.save_navigation_session(session)

            logger.info(f"Successfully discovered {len(court_nodes)} nodes")
            return court_nodes

        except Exception as e:
            logger.error(f"Error discovering tree structure: {e}")
            raise

    async def discover_all_cases(self, court_nodes: List[CourtNode]) -> List[CaseLawFile]:
        """Discover all cases from day nodes."""
        # Get day nodes that need case discovery
        day_nodes = [node for node in court_nodes if node.node_type == NodeType.DAY]

        if not day_nodes:
            # If no day nodes provided, get from state
            day_nodes = self.state.get_day_nodes_for_discovery()

        logger.info(f"Discovering cases from {len(day_nodes)} day nodes")

        all_cases = []

        # Process day nodes sequentially for robustness (site UI is fragile with concurrency)
        for idx, node in enumerate(day_nodes, start=1):
            logger.info(f"[Day {idx}/{len(day_nodes)}] {node.full_path}")
            cases = await self._discover_cases_for_day(node)
            if cases:
                all_cases.extend(cases)
                for case in cases:
                    self.state.save_case_file(case)
            # small polite delay between day navigations
            await asyncio.sleep(self.config.request_delay)

        logger.info(f"Discovered {len(all_cases)} total cases")
        return all_cases

    async def download_all_cases(self, case_files: List[CaseLawFile]) -> Tuple[int, int]:
        """Download all discovered case files."""
        # Get cases that need downloading (not already downloaded)
        cases_to_download = [
            case for case in case_files
            if not self.state.is_case_already_downloaded(case)
        ]

        logger.info(f"Will download {len(cases_to_download)} new cases")

        if not cases_to_download:
            return 0, 0

        downloaded = 0
        errors = 0

        # Process downloads with concurrency control
        semaphore = asyncio.Semaphore(self.config.max_concurrent_downloads)

        async def download_with_semaphore(case_file: CaseLawFile) -> bool:
            async with semaphore:
                context = self.navigator.contexts[0]  # Use first browser context
                return await self.downloader.process_case_file(context, case_file)

        # Execute downloads with progress tracking
        tasks = [download_with_semaphore(case) for case in cases_to_download]

        for coro in tqdm.as_completed(tasks, desc="Downloading PDFs"):
            result = await coro
            if result:
                downloaded += 1
            else:
                errors += 1

            # Rate limiting
            await asyncio.sleep(self.config.request_delay)

        return downloaded, errors

    async def scrape_node(self, target_node: CourtNode) -> ScrapeResult:
        """Scrape a specific node (court, year, month, or day) and all its descendants."""
        start_time = datetime.now()
        result = ScrapeResult(start_time=start_time)

        try:
            logger.info(f"Starting targeted scraping for node: {target_node.full_path}")

            # Determine scraping strategy based on node type
            if target_node.node_type == NodeType.DAY:
                # For day nodes, directly discover and download cases
                logger.info(f"Scraping day node: {target_node.full_path}")
                case_files = await self._discover_cases_for_day(target_node)
                result.total_discovered = len(case_files)

                if case_files:
                    downloaded, errors = await self.download_all_cases(case_files)
                    result.downloaded = downloaded
                    result.errors = errors
                    result.skipped = len(case_files) - downloaded - errors

            elif target_node.node_type in [NodeType.COURT, NodeType.YEAR, NodeType.MONTH]:
                # For higher-level nodes, discover the tree structure first
                logger.info(f"Discovering tree structure for {target_node.node_type.value}: {target_node.full_path}")

                # Use navigator to discover the subtree for this specific node
                court_nodes = await self._discover_subtree_for_node(target_node)
                logger.info(f"Discovered {len(court_nodes)} nodes in subtree")

                # Prefer robust month-level discovery to avoid missed day clicks
                month_nodes = [node for node in court_nodes if node.node_type == NodeType.MONTH]
                day_nodes = [node for node in court_nodes if node.node_type == NodeType.DAY]
                logger.info(f"Found {len(month_nodes)} months and {len(day_nodes)} days in subtree")

                case_files: List[CaseLawFile] = []

                if month_nodes:
                    # Aggregate cases per month (robust path)
                    for month in month_nodes:
                        month_cases = await self.navigator.discover_cases_for_month(month)
                        if month_cases:
                            case_files.extend(month_cases)
                            for case in month_cases:
                                self.state.save_case_file(case)
                    # If month-level yielded nothing, fallback to day-based discovery
                    if not case_files and day_nodes:
                        logger.info("Month-level discovery yielded 0 cases, falling back to day-level discovery")
                        case_files = await self.discover_all_cases(day_nodes)
                elif day_nodes:
                    # Fallback to day-based discovery
                    case_files = await self.discover_all_cases(day_nodes)

                result.total_discovered = len(case_files)

                if case_files:
                    # Download all discovered cases
                    downloaded, errors = await self.download_all_cases(case_files)
                    result.downloaded = downloaded
                    result.errors = errors
                    result.skipped = len(case_files) - downloaded - errors

            result.end_time = datetime.now()
            logger.info(f"Completed targeted scraping for {target_node.full_path}: "
                       f"{result.downloaded} downloaded, {result.errors} errors, {result.skipped} skipped")

            return result

        except Exception as e:
            logger.error(f"Error during targeted scraping of {target_node.full_path}: {e}")
            result.end_time = datetime.now()
            result.errors = 1
            raise

    async def resume_from_state(self) -> ScrapeResult:
        """Resume scraping from saved state."""
        logger.info("Resuming case-law scraping from saved state")

        # Get resume position
        resume_pos = self.state.get_resume_position()
        if not resume_pos:
            logger.info("No resume position found, starting full scrape")
            return await self.run_full_scrape(resume=False)

        path, node_type = resume_pos
        logger.info(f"Resuming from: {path} (type: {node_type})")

        # Load existing progress
        progress = self.state.get_navigation_progress()
        logger.info(f"Existing progress: {progress}")

        # Continue from where we left off
        if node_type == "day":
            # Resume case discovery
            pending_day_nodes = self.state.get_day_nodes_for_discovery()
            if pending_day_nodes:
                case_files = await self.discover_all_cases(pending_day_nodes)
                return await self._complete_download_phase(case_files)

        # For other node types, resume tree discovery
        return await self.run_full_scrape(resume=True)

    async def generate_manifest(self) -> CaseLawManifest:
        """Generate a complete manifest of all discovered and downloaded cases."""
        logger.info("Generating case-law manifest")

        # Get all cases from state
        all_cases = self.state.get_case_files_for_download()
        downloaded_cases = [
            case for case in all_cases
            if self.state.is_case_already_downloaded(case)
        ]

        # Create navigation state
        progress = self.state.get_navigation_progress()
        nav_state = NavigationState(
            session_id="manifest_generation",
            total_nodes_discovered=progress["nodes"].get("discovered", 0),
            total_nodes_expanded=progress["nodes"].get("expanded", 0),
            total_documents_discovered=progress["discovered_documents"],
            total_documents_downloaded=progress["downloaded_documents"],
        )

        # Create manifest
        manifest = CaseLawManifest(
            files=downloaded_cases,
            navigation_state=nav_state,
            total_discovered=len(all_cases),
        )

        # Calculate statistics
        for case in downloaded_cases:
            manifest.by_court[case.court_name] = manifest.by_court.get(case.court_name, 0) + 1
            manifest.by_year[case.year] = manifest.by_year.get(case.year, 0) + 1
            month_key = f"{case.year:04d}-{case.month:02d}"
            manifest.by_month[month_key] = manifest.by_month.get(month_key, 0) + 1

        # Save manifest
        manifest_path = self.config.output_dir / "caselaw_manifest.json"
        with open(manifest_path, "w", encoding="utf-8") as f:
            f.write(manifest.model_dump_json(indent=2))

        # Export state for backup
        state_export_path = self.config.output_dir / "caselaw_state_export.json"
        self.state.export_navigation_state(state_export_path)

        logger.info(f"Manifest saved to: {manifest_path}")
        return manifest

    async def validate_downloads(self) -> Dict[str, int]:
        """Validate all downloaded files and return statistics."""
        logger.info("Validating downloaded files")

        downloaded_cases = [
            case for case in self.state.get_case_files_for_download()
            if self.state.is_case_already_downloaded(case)
        ]

        validation_stats = {
            "total_files": len(downloaded_cases),
            "valid_files": 0,
            "invalid_files": 0,
            "missing_files": 0,
            "hash_mismatches": 0,
        }

        for case in downloaded_cases:
            if not case.local_path or not case.local_path.exists():
                validation_stats["missing_files"] += 1
                continue

            # Verify file hash
            if case.sha256:
                current_hash = self.downloader._calculate_file_hash(case.local_path)
                if current_hash != case.sha256:
                    validation_stats["hash_mismatches"] += 1
                    logger.warning(f"Hash mismatch for {case.filename}")
                    continue

            # Verify PDF content
            with open(case.local_path, "rb") as f:
                content = f.read()

            if self.downloader._is_valid_pdf(content):
                validation_stats["valid_files"] += 1
            else:
                validation_stats["invalid_files"] += 1
                logger.warning(f"Invalid PDF content: {case.filename}")

        logger.info(f"Validation complete: {validation_stats}")
        return validation_stats

    async def _discover_cases_for_day(self, day_node: CourtNode) -> List[CaseLawFile]:
        """Discover cases for a single day node."""
        try:
            cases = await self.navigator.discover_cases_for_day(day_node)

            # Update node with actual document count
            day_node.actual_documents = len(cases)
            day_node.status = NavigationStatus.COMPLETED
            self.state.save_court_node(day_node)

            return cases

        except Exception as e:
            logger.error(f"Error discovering cases for {day_node.full_path}: {e}")
            day_node.status = NavigationStatus.FAILED
            day_node.error_message = str(e)
            self.state.save_court_node(day_node)
            return []

    async def _discover_subtree_for_node(self, target_node: CourtNode) -> List[CourtNode]:
        """Discover the subtree structure for a specific target node using targeted navigation."""
        logger.info(f"Discovering subtree for {target_node.node_type.value}: {target_node.full_path}")

        try:
            # Use targeted navigation instead of full exploration
            context = self.navigator.contexts[0]
            page = await context.new_page()

            try:
                # Navigate to case-law page
                url = f"{self.navigator.config.base_url}{self.navigator.config.caselaw_path}"
                logger.info(f"Navigating to: {url}")
                await page.goto(url, wait_until="networkidle", timeout=self.navigator.config.page_load_timeout)
                await asyncio.sleep(self.navigator.config.navigation_delay)

                court_nodes = []

                if target_node.node_type == NodeType.COURT:
                    # For court nodes, discover only this specific court's structure
                    logger.info(f"Discovering structure for court: {target_node.court_name}")

                    # Find and expand the specific court
                    court_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
                    court_found = False

                    for element in court_elements:
                        text = await element.text_content()
                        if text and target_node.court_name in text:
                            logger.debug(f"Found target court: {text}")
                            await element.click()
                            await asyncio.sleep(self.navigator.config.navigation_delay)
                            court_found = True
                            break

                    if not court_found:
                        logger.error(f"Court '{target_node.court_name}' not found")
                        return []

                    # Add the court node itself
                    court_nodes.append(target_node)

                    # Discover all years for this court
                    years = await self.navigator._discover_years(page, target_node)
                    court_nodes.extend(years)

                    # For each year, discover months and days
                    for year in years:
                        year_expanded = await self.navigator._expand_node(page, year)
                        if year_expanded:
                            months = await self.navigator._discover_months(page, year)
                            court_nodes.extend(months)

                            for month in months:
                                month_expanded = await self.navigator._expand_node(page, month)
                                if month_expanded:
                                    days = await self.navigator._discover_days(page, month)
                                    court_nodes.extend(days)

                elif target_node.node_type == NodeType.YEAR:
                    # For year nodes, navigate to specific court/year and discover months/days
                    logger.info(f"Discovering structure for year: {target_node.full_path}")

                    # Navigate to the specific court and expand it
                    court_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
                    court_found = False

                    for element in court_elements:
                        text = await element.text_content()
                        if text and target_node.court_name in text:
                            await element.click()
                            await asyncio.sleep(self.navigator.config.navigation_delay)
                            court_found = True
                            break

                    if not court_found:
                        logger.error(f"Court '{target_node.court_name}' not found")
                        return []

                    # Find and expand the specific year
                    year_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
                    year_found = False

                    for element in year_elements:
                        text = await element.text_content()
                        if text and str(target_node.year) in text and "(" in text:
                            await element.click()
                            await asyncio.sleep(self.navigator.config.navigation_delay)
                            year_found = True
                            break

                    if not year_found:
                        logger.error(f"Year '{target_node.year}' not found")
                        return []

                    # Add the year node itself
                    court_nodes.append(target_node)

                    # Discover months for this year
                    months = await self.navigator._discover_months(page, target_node)
                    court_nodes.extend(months)

                    # For each month, discover days
                    for month in months:
                        month_expanded = await self.navigator._expand_node(page, month)
                        if month_expanded:
                            days = await self.navigator._discover_days(page, month)
                            court_nodes.extend(days)

                elif target_node.node_type == NodeType.MONTH:
                    # For month nodes, navigate to specific court/year/month and discover days
                    logger.info(f"Discovering structure for month: {target_node.full_path}")

                    # Navigate to the specific court and expand it
                    court_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
                    court_found = False

                    for element in court_elements:
                        text = await element.text_content()
                        if text and target_node.court_name in text:
                            await element.click()
                            await asyncio.sleep(self.navigator.config.navigation_delay)
                            court_found = True
                            break

                    if not court_found:
                        logger.error(f"Court '{target_node.court_name}' not found")
                        return []

                    # Find and expand the specific year
                    year_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
                    year_found = False

                    for element in year_elements:
                        text = await element.text_content()
                        if text and str(target_node.year) in text and "(" in text:
                            await element.click()
                            await asyncio.sleep(self.navigator.config.navigation_delay)
                            year_found = True
                            break

                    if not year_found:
                        logger.error(f"Year '{target_node.year}' not found")
                        return []

                    # Find and expand the specific month
                    month_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
                    month_found = False

                    # Convert month number to month name
                    month_names = ["", "January", "February", "March", "April", "May", "June",
                                 "July", "August", "September", "October", "November", "December"]
                    month_name = month_names[target_node.month] if target_node.month <= 12 else str(target_node.month)

                    for element in month_elements:
                        text = await element.text_content()
                        if text and month_name in text and "(" in text:
                            await element.click()
                            await asyncio.sleep(self.navigator.config.navigation_delay)
                            month_found = True
                            break

                    if not month_found:
                        logger.error(f"Month '{month_name}' not found")
                        return []

                    # Add the month node itself
                    court_nodes.append(target_node)

                    # Discover days for this month
                    days = await self.navigator._discover_days(page, target_node)
                    court_nodes.extend(days)

                else:
                    # For day nodes, return just the single node
                    court_nodes = [target_node]

                # Save discovered nodes to state
                for node in court_nodes:
                    self.state.save_court_node(node)

                logger.info(f"Discovered {len(court_nodes)} nodes for {target_node.full_path}")
                return court_nodes

            finally:
                await page.close()

        except Exception as e:
            logger.error(f"Error discovering subtree for {target_node.full_path}: {e}")
            raise

    async def _complete_download_phase(self, case_files: List[CaseLawFile]) -> ScrapeResult:
        """Complete the download phase for discovered cases."""
        start_time = datetime.now()
        result = ScrapeResult(start_time=start_time)

        result.total_discovered = len(case_files)
        downloaded, errors = await self.download_all_cases(case_files)

        result.downloaded = downloaded
        result.errors = errors
        result.skipped = len(case_files) - downloaded - errors
        result.end_time = datetime.now()

        return result

    def _load_all_nodes(self) -> List[CourtNode]:
        """Load all nodes from state."""
        all_statuses = [
            NavigationStatus.PENDING,
            NavigationStatus.DISCOVERED,
            NavigationStatus.EXPANDING,
            NavigationStatus.EXPANDED,
            NavigationStatus.COMPLETED,
            NavigationStatus.FAILED,
        ]

        all_nodes = []
        for status in all_statuses:
            nodes = self.state.get_nodes_by_status(status)
            all_nodes.extend(nodes)

        return all_nodes

    def _log_pipeline_summary(self, result: ScrapeResult) -> None:
        """Log pipeline execution summary."""
        duration = (result.end_time - result.start_time) if result.end_time else None

        logger.info("=== CASE-LAW SCRAPING SUMMARY ===")
        logger.info(f"Total discovered: {result.total_discovered}")
        logger.info(f"Downloaded: {result.downloaded}")
        logger.info(f"Skipped: {result.skipped}")
        logger.info(f"Errors: {result.errors}")
        if duration:
            logger.info(f"Duration: {duration}")

        # Get current progress
        progress = self.state.get_navigation_progress()
        logger.info(f"Navigation progress: {progress}")

        if result.error_messages:
            logger.error("Error messages:")
            for msg in result.error_messages[:10]:
                logger.error(f"  - {msg}")

        # Log validation stats
        try:
            asyncio.create_task(self._log_validation_stats())
        except Exception as e:
            logger.debug(f"Could not log validation stats: {e}")

    async def _log_validation_stats(self) -> None:
        """Log validation statistics."""
        try:
            validation_stats = await self.validate_downloads()
            logger.info(f"File validation: {validation_stats}")
        except Exception as e:
            logger.error(f"Error during validation: {e}")

    # Utility methods for external access

    def get_progress_stats(self) -> Dict[str, any]:
        """Get current progress statistics."""
        return self.state.get_navigation_progress()

    def get_resume_info(self) -> Optional[Tuple[str, str]]:
        """Get information about where to resume."""
        return self.state.get_resume_position()

    def export_state(self, output_path: Path) -> None:
        """Export current state for backup."""
        self.state.export_navigation_state(output_path)

    def import_state(self, input_path: Path) -> None:
        """Import state from backup."""
        self.state.import_navigation_state(input_path)