"""Language detection utilities for case law documents."""

import re
from typing import Op<PERSON>, Tuple
from .models import CaseLanguage


class CaseLanguageDetector:
    """Detects the language of case law documents based on various indicators."""
    
    # Common Kinyarwanda legal terms and patterns
    KINYARWANDA_INDICATORS = {
        # Legal terms
        'urubanza', 'urukiko', 'icyemezo', 'itegeko', 'ubwiyunge', 'ubucamanza',
        'ubunyangamugayo', 'ubushobozi', 'ubwoba', 'ubwenge', 'ubwiyunge',
        'koperative', 'duhuzumurimo', 'amategeko', 'urugaga', 'abavoka',
        'ikigo', 'cyimisoro', 'namahoro', 'mukankurikiyimana', 'niyonagize',
        'rutayisire', 'nsengiyumva', 'mhayimana', 'nayituriki', 'mugwaneza',
        
        # Common Kinyarwanda words
        'mu', 'na', 'wa', 'rw', 'ku', 'kw', 'by', 'cy', 'ry', 'ny', 'tw', 'bw',
        'gw', 'sw', 'zw', 'nk', 'mp', 'nt', 'nd', 'ng', 'mb', 'nz',
        
        # Kinyarwanda-specific patterns
        'rwanda', 'kigali', 'butare', 'gitarama', 'ruhengeri', 'gisenyi',
        'byumba', 'kibungo', 'cyangugu', 'kibuye', 'umutara', 'gikongoro'
    }
    
    # Common English legal terms
    ENGLISH_INDICATORS = {
        'court', 'judgment', 'case', 'plaintiff', 'defendant', 'appeal',
        'supreme', 'high', 'commercial', 'tribunal', 'justice', 'law',
        'legal', 'ruling', 'decision', 'order', 'motion', 'petition',
        'versus', 'against', 'limited', 'ltd', 'company', 'corporation',
        'association', 'authority', 'ministry', 'government', 'republic',
        'revenue', 'bank', 'insurance', 'general', 'security', 'interior',
        'hardware', 'truck', 'fast', 'excel', 'sonarwa', 'hayton'
    }
    
    # French legal terms (less common but possible)
    FRENCH_INDICATORS = {
        'tribunal', 'cour', 'jugement', 'arrêt', 'demandeur', 'défendeur',
        'appel', 'suprême', 'commercial', 'justice', 'droit', 'légal',
        'décision', 'ordonnance', 'requête', 'contre', 'société', 'limitée',
        'compagnie', 'corporation', 'association', 'autorité', 'ministère',
        'gouvernement', 'république', 'revenus', 'banque', 'assurance'
    }
    
    @classmethod
    def detect_language_from_title(cls, case_title: str) -> CaseLanguage:
        """
        Detect language from case title using keyword analysis.
        
        Args:
            case_title: The case title to analyze
            
        Returns:
            Detected language
        """
        if not case_title or not case_title.strip():
            return CaseLanguage.UNKNOWN
            
        # Normalize title for analysis
        title_lower = case_title.lower().strip()
        
        # Remove common punctuation and split into words
        words = re.findall(r'\b\w+\b', title_lower)
        
        if not words:
            return CaseLanguage.UNKNOWN
            
        # Count indicators for each language
        kinyarwanda_score = cls._calculate_language_score(words, cls.KINYARWANDA_INDICATORS)
        english_score = cls._calculate_language_score(words, cls.ENGLISH_INDICATORS)
        french_score = cls._calculate_language_score(words, cls.FRENCH_INDICATORS)
        
        # Determine language based on highest score
        max_score = max(kinyarwanda_score, english_score, french_score)
        
        if max_score == 0:
            return CaseLanguage.UNKNOWN
        elif kinyarwanda_score == max_score:
            return CaseLanguage.KINYARWANDA
        elif english_score == max_score:
            return CaseLanguage.ENGLISH
        else:
            return CaseLanguage.FRENCH
    
    @classmethod
    def detect_language_from_url(cls, url: str) -> Optional[CaseLanguage]:
        """
        Detect language from URL patterns if available.
        
        Args:
            url: The URL to analyze
            
        Returns:
            Detected language or None if not determinable
        """
        if not url:
            return None
            
        url_lower = url.lower()
        
        # Check for language indicators in URL
        if '/en/' in url_lower or 'lang=en' in url_lower:
            return CaseLanguage.ENGLISH
        elif '/rw/' in url_lower or 'lang=rw' in url_lower:
            return CaseLanguage.KINYARWANDA
        elif '/fr/' in url_lower or 'lang=fr' in url_lower:
            return CaseLanguage.FRENCH
            
        return None
    
    @classmethod
    def detect_language_comprehensive(
        cls, 
        case_title: str, 
        case_page_url: Optional[str] = None,
        download_url: Optional[str] = None
    ) -> Tuple[CaseLanguage, float]:
        """
        Comprehensive language detection using multiple indicators.
        
        Args:
            case_title: The case title
            case_page_url: Optional case page URL
            download_url: Optional download URL
            
        Returns:
            Tuple of (detected_language, confidence_score)
        """
        # Start with title-based detection
        title_language = cls.detect_language_from_title(case_title)
        confidence = 0.7  # Base confidence for title detection
        
        # Check URL-based indicators
        url_language = None
        if case_page_url:
            url_language = cls.detect_language_from_url(case_page_url)
        if not url_language and download_url:
            url_language = cls.detect_language_from_url(download_url)
            
        # Adjust confidence based on URL confirmation
        if url_language and url_language == title_language:
            confidence = 0.9  # High confidence when URL confirms title
        elif url_language and url_language != title_language:
            confidence = 0.5  # Lower confidence when URL contradicts title
            # In case of conflict, prefer URL-based detection for now
            title_language = url_language
            
        return title_language, confidence
    
    @classmethod
    def _calculate_language_score(cls, words: list, indicators: set) -> int:
        """Calculate language score based on word matches."""
        score = 0
        for word in words:
            if word in indicators:
                score += 1
            # Check for partial matches (for compound words)
            elif any(indicator in word for indicator in indicators if len(indicator) > 3):
                score += 0.5
                
        return score
    
    @classmethod
    def generate_language_suffix(cls, language: CaseLanguage) -> str:
        """Generate a suffix for filenames based on language."""
        suffix_map = {
            CaseLanguage.ENGLISH: "_EN",
            CaseLanguage.KINYARWANDA: "_RW", 
            CaseLanguage.FRENCH: "_FR",
            CaseLanguage.UNKNOWN: "_UNK"
        }
        return suffix_map.get(language, "_UNK")
    
    @classmethod
    def should_treat_as_separate_documents(
        cls, 
        case1_title: str, 
        case1_language: CaseLanguage,
        case2_title: str, 
        case2_language: CaseLanguage
    ) -> bool:
        """
        Determine if two cases should be treated as separate documents.
        
        Args:
            case1_title: First case title
            case1_language: First case language
            case2_title: Second case title  
            case2_language: Second case language
            
        Returns:
            True if they should be treated as separate documents
        """
        # If languages are different, treat as separate
        if case1_language != case2_language:
            return True
            
        # If titles are identical and languages are the same, treat as duplicate
        if case1_title.strip() == case2_title.strip():
            return False
            
        # Otherwise, treat as separate
        return True
