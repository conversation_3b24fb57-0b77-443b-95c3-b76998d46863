"""Validation and monitoring for case-law download completeness."""

from __future__ import annotations

import hashlib
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import pymupdf  # PyMuPDF for PDF validation

from .models import CaseLawConfig, CaseLawFile, CourtNode, NodeType
from .state import CaseLawState

logger = logging.getLogger(__name__)


class CaseLawValidator:
    """Validates downloaded case-law files and completeness."""

    def __init__(self, config: CaseLawConfig, state: CaseLawState):
        self.config = config
        self.state = state

    def validate_file_integrity(self, case_file: CaseLawFile) -> Dict[str, any]:
        """Validate integrity of a downloaded PDF file."""
        validation_result = {
            "file_path": str(case_file.local_path) if case_file.local_path else None,
            "exists": False,
            "size_match": False,
            "hash_match": False,
            "pdf_valid": False,
            "readable": False,
            "page_count": 0,
            "errors": [],
        }

        if not case_file.local_path:
            validation_result["errors"].append("No local path specified")
            return validation_result

        # Check if file exists
        if not case_file.local_path.exists():
            validation_result["errors"].append("File does not exist")
            return validation_result

        validation_result["exists"] = True

        try:
            # Check file size
            actual_size = case_file.local_path.stat().st_size
            if case_file.size_bytes:
                validation_result["size_match"] = actual_size == case_file.size_bytes
                if not validation_result["size_match"]:
                    validation_result["errors"].append(
                        f"Size mismatch: expected {case_file.size_bytes}, got {actual_size}"
                    )
            else:
                validation_result["size_match"] = True  # No expected size to compare

            # Check minimum size
            if actual_size < self.config.min_pdf_size:
                validation_result["errors"].append(
                    f"File too small: {actual_size} bytes (minimum: {self.config.min_pdf_size})"
                )

            # Check file hash
            if case_file.sha256:
                actual_hash = self._calculate_file_hash(case_file.local_path)
                validation_result["hash_match"] = actual_hash == case_file.sha256
                if not validation_result["hash_match"]:
                    validation_result["errors"].append(
                        f"Hash mismatch: expected {case_file.sha256}, got {actual_hash}"
                    )
            else:
                validation_result["hash_match"] = True  # No expected hash to compare

            # Validate PDF structure
            pdf_validation = self._validate_pdf_structure(case_file.local_path)
            validation_result.update(pdf_validation)

        except Exception as e:
            validation_result["errors"].append(f"Validation error: {e}")

        return validation_result

    def validate_navigation_completeness(self) -> Dict[str, any]:
        """Validate completeness of tree navigation."""
        progress = self.state.get_navigation_progress()

        completeness_result = {
            "total_nodes": sum(progress["nodes"].values()),
            "completed_nodes": progress["nodes"].get("completed", 0),
            "failed_nodes": progress["nodes"].get("failed", 0),
            "pending_nodes": progress["nodes"].get("pending", 0),
            "completion_rate": 0.0,
            "expected_vs_discovered": {},
            "missing_documents": [],
            "inconsistencies": [],
        }

        total_nodes = completeness_result["total_nodes"]
        if total_nodes > 0:
            completed = completeness_result["completed_nodes"]
            completeness_result["completion_rate"] = (completed / total_nodes) * 100

        # Check expected vs discovered document counts
        day_nodes = self.state.get_nodes_by_status(None)  # Get all nodes
        day_nodes = [n for n in day_nodes if n.node_type == NodeType.DAY]

        for node in day_nodes:
            if node.document_count > 0:  # Only check nodes that claim to have documents
                path = node.full_path
                expected = node.document_count
                discovered = node.actual_documents

                completeness_result["expected_vs_discovered"][path] = {
                    "expected": expected,
                    "discovered": discovered,
                    "match": expected == discovered,
                }

                if discovered < expected:
                    completeness_result["missing_documents"].append({
                        "path": path,
                        "missing_count": expected - discovered,
                        "expected": expected,
                        "discovered": discovered,
                    })

                if abs(discovered - expected) > expected * 0.1:  # >10% difference
                    completeness_result["inconsistencies"].append({
                        "path": path,
                        "expected": expected,
                        "discovered": discovered,
                        "difference": discovered - expected,
                        "percentage": ((discovered - expected) / expected) * 100 if expected > 0 else 0,
                    })

        return completeness_result

    def validate_download_completeness(self) -> Dict[str, any]:
        """Validate completeness of PDF downloads."""
        all_cases = self.state.get_case_files_for_download()
        downloaded_cases = [
            case for case in all_cases
            if self.state.is_case_already_downloaded(case)
        ]

        download_result = {
            "total_discovered": len(all_cases),
            "total_downloaded": len(downloaded_cases),
            "download_rate": 0.0,
            "failed_downloads": 0,
            "pending_downloads": 0,
            "corrupted_files": 0,
            "missing_files": 0,
            "size_mismatches": 0,
            "hash_mismatches": 0,
            "unreadable_pdfs": 0,
            "validation_details": [],
        }

        if len(all_cases) > 0:
            download_result["download_rate"] = (len(downloaded_cases) / len(all_cases)) * 100

        # Count different types of issues
        for case in all_cases:
            if self.state.is_case_already_downloaded(case):
                # Validate downloaded file
                validation = self.validate_file_integrity(case)

                if not validation["exists"]:
                    download_result["missing_files"] += 1
                elif validation["errors"]:
                    download_result["corrupted_files"] += 1

                if not validation["size_match"]:
                    download_result["size_mismatches"] += 1

                if not validation["hash_match"]:
                    download_result["hash_mismatches"] += 1

                if not validation["readable"]:
                    download_result["unreadable_pdfs"] += 1

                download_result["validation_details"].append({
                    "case_title": case.case_title,
                    "file_path": str(case.local_path) if case.local_path else None,
                    "validation": validation,
                })
            else:
                # Check if it's a failed download
                # This would need to be implemented in the state to track failed downloads
                download_result["pending_downloads"] += 1

        return download_result

    def generate_completeness_report(self) -> Dict[str, any]:
        """Generate comprehensive completeness report."""
        navigation_completeness = self.validate_navigation_completeness()
        download_completeness = self.validate_download_completeness()

        report = {
            "generated_at": datetime.now().isoformat(),
            "config": {
                "base_url": self.config.base_url,
                "output_dir": str(self.config.output_dir),
                "min_pdf_size": self.config.min_pdf_size,
            },
            "navigation": navigation_completeness,
            "downloads": download_completeness,
            "overall_health": self._calculate_overall_health(
                navigation_completeness, download_completeness
            ),
            "recommendations": self._generate_recommendations(
                navigation_completeness, download_completeness
            ),
        }

        return report

    def validate_court_coverage(self) -> Dict[str, any]:
        """Validate coverage across different courts."""
        all_cases = self.state.get_case_files_for_download()
        downloaded_cases = [
            case for case in all_cases
            if self.state.is_case_already_downloaded(case)
        ]

        court_coverage = {}

        # Group by court
        for case in all_cases:
            court = case.court_name
            if court not in court_coverage:
                court_coverage[court] = {
                    "discovered": 0,
                    "downloaded": 0,
                    "download_rate": 0.0,
                    "years_covered": set(),
                    "date_range": {"earliest": None, "latest": None},
                }

            court_coverage[court]["discovered"] += 1
            court_coverage[court]["years_covered"].add(case.year)

            # Track date range
            case_date = datetime(case.year, case.month, case.day)
            if court_coverage[court]["date_range"]["earliest"] is None:
                court_coverage[court]["date_range"]["earliest"] = case_date
                court_coverage[court]["date_range"]["latest"] = case_date
            else:
                if case_date < court_coverage[court]["date_range"]["earliest"]:
                    court_coverage[court]["date_range"]["earliest"] = case_date
                if case_date > court_coverage[court]["date_range"]["latest"]:
                    court_coverage[court]["date_range"]["latest"] = case_date

        for case in downloaded_cases:
            court = case.court_name
            if court in court_coverage:
                court_coverage[court]["downloaded"] += 1

        # Calculate download rates and convert sets to lists
        for court, data in court_coverage.items():
            if data["discovered"] > 0:
                data["download_rate"] = (data["downloaded"] / data["discovered"]) * 100
            data["years_covered"] = sorted(list(data["years_covered"]))

            # Convert dates to strings
            if data["date_range"]["earliest"]:
                data["date_range"]["earliest"] = data["date_range"]["earliest"].isoformat()
            if data["date_range"]["latest"]:
                data["date_range"]["latest"] = data["date_range"]["latest"].isoformat()

        return {
            "courts": court_coverage,
            "total_courts": len(court_coverage),
            "courts_with_complete_coverage": len([
                c for c in court_coverage.values()
                if c["download_rate"] >= 100.0
            ]),
        }

    def find_potential_duplicates(self) -> List[Dict[str, any]]:
        """Find potential duplicate files based on content hash."""
        all_cases = self.state.get_case_files_for_download()
        downloaded_cases = [
            case for case in all_cases
            if self.state.is_case_already_downloaded(case) and case.sha256
        ]

        # Group by hash
        hash_groups = {}
        for case in downloaded_cases:
            if case.sha256 in hash_groups:
                hash_groups[case.sha256].append(case)
            else:
                hash_groups[case.sha256] = [case]

        # Find duplicates
        duplicates = []
        for sha256, cases in hash_groups.items():
            if len(cases) > 1:
                duplicates.append({
                    "sha256": sha256,
                    "count": len(cases),
                    "cases": [
                        {
                            "case_title": case.case_title,
                            "court_name": case.court_name,
                            "date": f"{case.year}-{case.month:02d}-{case.day:02d}",
                            "file_path": str(case.local_path) if case.local_path else None,
                        }
                        for case in cases
                    ],
                })

        return duplicates

    def _validate_pdf_structure(self, file_path: Path) -> Dict[str, any]:
        """Validate PDF file structure and readability."""
        result = {
            "pdf_valid": False,
            "readable": False,
            "page_count": 0,
            "errors": [],
        }

        try:
            # Try to open with PyMuPDF
            doc = pymupdf.open(str(file_path))

            result["pdf_valid"] = True
            result["page_count"] = len(doc)

            if result["page_count"] > 0:
                # Try to read first page to ensure readability
                first_page = doc[0]
                text = first_page.get_text()
                result["readable"] = len(text.strip()) > 0

                if not result["readable"]:
                    # Check if it's an image-based PDF
                    image_list = first_page.get_images()
                    if image_list:
                        result["readable"] = True  # Has images, consider readable
                        result["errors"].append("Text-based content minimal, appears to be image-based PDF")

            doc.close()

        except Exception as e:
            result["errors"].append(f"PDF validation error: {e}")

            # Fallback: check if file starts with PDF header
            try:
                with open(file_path, "rb") as f:
                    header = f.read(10)
                    if header.startswith(b"%PDF-"):
                        result["pdf_valid"] = True
                        result["errors"].append("PDF header valid but document unreadable")
            except Exception as header_e:
                result["errors"].append(f"Header check failed: {header_e}")

        return result

    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA256 hash of a file."""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()

    def _calculate_overall_health(
        self, navigation: Dict[str, any], downloads: Dict[str, any]
    ) -> Dict[str, any]:
        """Calculate overall health score."""
        nav_score = navigation.get("completion_rate", 0)
        download_score = downloads.get("download_rate", 0)

        # Weight navigation and downloads equally
        overall_score = (nav_score + download_score) / 2

        # Determine health status
        if overall_score >= 95:
            status = "excellent"
        elif overall_score >= 85:
            status = "good"
        elif overall_score >= 70:
            status = "fair"
        elif overall_score >= 50:
            status = "poor"
        else:
            status = "critical"

        return {
            "overall_score": round(overall_score, 2),
            "status": status,
            "navigation_score": round(nav_score, 2),
            "download_score": round(download_score, 2),
        }

    def _generate_recommendations(
        self, navigation: Dict[str, any], downloads: Dict[str, any]
    ) -> List[str]:
        """Generate recommendations based on validation results."""
        recommendations = []

        # Navigation recommendations
        nav_rate = navigation.get("completion_rate", 0)
        if nav_rate < 90:
            recommendations.append(
                f"Navigation completion rate is {nav_rate:.1f}%. "
                "Consider rerunning navigation to discover missing nodes."
            )

        if navigation.get("failed_nodes", 0) > 0:
            recommendations.append(
                f"{navigation['failed_nodes']} nodes failed during navigation. "
                "Check error logs and consider manual intervention."
            )

        if navigation.get("missing_documents"):
            missing_count = len(navigation["missing_documents"])
            recommendations.append(
                f"{missing_count} day nodes have fewer documents than expected. "
                "Verify these nodes manually."
            )

        # Download recommendations
        download_rate = downloads.get("download_rate", 0)
        if download_rate < 95:
            recommendations.append(
                f"Download completion rate is {download_rate:.1f}%. "
                "Resume downloading to complete remaining files."
            )

        if downloads.get("corrupted_files", 0) > 0:
            recommendations.append(
                f"{downloads['corrupted_files']} files appear corrupted. "
                "Re-download these files."
            )

        if downloads.get("missing_files", 0) > 0:
            recommendations.append(
                f"{downloads['missing_files']} downloaded files are missing from disk. "
                "Check file system and re-download if necessary."
            )

        if downloads.get("hash_mismatches", 0) > 0:
            recommendations.append(
                f"{downloads['hash_mismatches']} files have hash mismatches. "
                "These files may be corrupted and should be re-downloaded."
            )

        if not recommendations:
            recommendations.append("System appears healthy. No immediate action required.")

        return recommendations


class CaseLawMonitor:
    """Real-time monitoring for case-law scraping operations."""

    def __init__(self, config: CaseLawConfig, state: CaseLawState):
        self.config = config
        self.state = state
        self.validator = CaseLawValidator(config, state)
        self.start_time = datetime.now()

    def get_real_time_stats(self) -> Dict[str, any]:
        """Get real-time scraping statistics."""
        progress = self.state.get_navigation_progress()

        # Calculate rates
        elapsed_time = (datetime.now() - self.start_time).total_seconds()

        stats = {
            "session": {
                "started_at": self.start_time.isoformat(),
                "elapsed_seconds": elapsed_time,
                "elapsed_formatted": self._format_duration(elapsed_time),
            },
            "progress": progress,
            "rates": {
                "nodes_per_hour": 0,
                "downloads_per_hour": 0,
                "estimated_completion_hours": None,
            },
            "current_status": self._get_current_status(),
        }

        if elapsed_time > 0:
            total_nodes = sum(progress["nodes"].values())
            downloaded = progress.get("downloaded_documents", 0)

            stats["rates"]["nodes_per_hour"] = (total_nodes / elapsed_time) * 3600
            stats["rates"]["downloads_per_hour"] = (downloaded / elapsed_time) * 3600

            # Estimate completion time
            remaining = progress.get("expected_documents", 0) - downloaded
            if remaining > 0 and stats["rates"]["downloads_per_hour"] > 0:
                remaining_hours = remaining / stats["rates"]["downloads_per_hour"]
                stats["rates"]["estimated_completion_hours"] = remaining_hours

        return stats

    def _get_current_status(self) -> str:
        """Get current scraping status."""
        progress = self.state.get_navigation_progress()

        pending_nodes = progress["nodes"].get("pending", 0)
        pending_cases = progress["cases"].get("discovered", 0) - progress["cases"].get("downloaded", 0)

        if pending_nodes > 0:
            return "discovering_nodes"
        elif pending_cases > 0:
            return "downloading_cases"
        else:
            return "completed"

    def _format_duration(self, seconds: float) -> str:
        """Format duration in human-readable format."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = int(seconds % 60)

        if hours > 0:
            return f"{hours}h {minutes}m {seconds}s"
        elif minutes > 0:
            return f"{minutes}m {seconds}s"
        else:
            return f"{seconds}s"