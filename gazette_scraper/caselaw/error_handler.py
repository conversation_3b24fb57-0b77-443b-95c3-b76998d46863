"""Error handling and retry mechanisms for case-law scraping."""

from __future__ import annotations

import asyncio
import logging
from datetime import datetime, timedelta
from functools import wraps
from typing import Any, Awaitable, Callable, Dict, List, Optional, Type, TypeVar

# Import Playwright errors conditionally
try:
    from playwright.async_api import <PERSON><PERSON><PERSON> as PlaywrightError, TimeoutError as PlaywrightTimeoutError
except ImportError:
    # Create dummy classes for testing without Playwright
    class PlaywrightError(Exception):
        pass

    class PlaywrightTimeoutError(Exception):
        pass

from .models import CaseLawConfig, CourtNode, NavigationStatus

logger = logging.getLogger(__name__)

T = TypeVar('T')


class CaseLawError(Exception):
    """Base exception for case-law scraping errors."""

    def __init__(self, message: str, node_path: Optional[str] = None, retryable: bool = True):
        super().__init__(message)
        self.node_path = node_path
        self.retryable = retryable


class NavigationError(CaseLawError):
    """Error during tree navigation."""
    pass


class ElementNotFoundError(NavigationError):
    """Required element not found on page."""

    def __init__(self, selector: str, node_path: Optional[str] = None):
        super().__init__(f"Element not found: {selector}", node_path, retryable=True)
        self.selector = selector


class ExpansionError(NavigationError):
    """Error expanding a tree node."""

    def __init__(self, node_path: str, reason: str):
        super().__init__(f"Failed to expand node {node_path}: {reason}", node_path, retryable=True)


class PDFExtractionError(CaseLawError):
    """Error extracting PDF from case page."""
    pass


class DownloadError(CaseLawError):
    """Error downloading PDF file."""

    def __init__(self, message: str, http_status: Optional[int] = None, url: Optional[str] = None):
        super().__init__(message, retryable=http_status not in [404, 403, 410] if http_status else True)
        self.http_status = http_status
        self.url = url


class ValidationError(CaseLawError):
    """Error validating downloaded content."""

    def __init__(self, message: str, file_path: Optional[str] = None):
        super().__init__(message, retryable=False)
        self.file_path = file_path


class RateLimitError(CaseLawError):
    """Rate limit exceeded."""

    def __init__(self, retry_after: int = 60):
        super().__init__(f"Rate limit exceeded, retry after {retry_after}s", retryable=True)
        self.retry_after = retry_after


class BrowserCrashError(CaseLawError):
    """Browser or context crashed."""

    def __init__(self, message: str):
        super().__init__(f"Browser crashed: {message}", retryable=True)


class ErrorHandler:
    """Centralized error handling and retry logic."""

    def __init__(self, config: CaseLawConfig):
        self.config = config
        self.error_counts: Dict[str, int] = {}
        self.last_errors: Dict[str, datetime] = {}

    def with_retry(
        self,
        max_attempts: Optional[int] = None,
        backoff_factor: float = 1.5,
        max_backoff: float = 60.0,
        exceptions: tuple[Type[Exception], ...] = (Exception,),
    ):
        """Decorator for automatic retry with exponential backoff."""

        def decorator(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
            @wraps(func)
            async def wrapper(*args, **kwargs) -> T:
                attempts = max_attempts or self.config.max_retry_attempts
                last_exception = None

                for attempt in range(attempts):
                    try:
                        return await func(*args, **kwargs)

                    except exceptions as e:
                        last_exception = e

                        # Don't retry non-retryable errors
                        if isinstance(e, CaseLawError) and not e.retryable:
                            raise

                        if attempt < attempts - 1:
                            # Calculate backoff delay
                            delay = min(
                                self.config.retry_delay * (backoff_factor ** attempt),
                                max_backoff
                            )

                            logger.warning(
                                f"Attempt {attempt + 1}/{attempts} failed: {e}. "
                                f"Retrying in {delay:.1f}s..."
                            )

                            await asyncio.sleep(delay)
                        else:
                            logger.error(f"All {attempts} attempts failed. Last error: {e}")

                raise last_exception

            return wrapper
        return decorator

    def with_browser_recovery(self):
        """Decorator for browser crash recovery."""

        def decorator(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
            @wraps(func)
            async def wrapper(*args, **kwargs) -> T:
                try:
                    return await func(*args, **kwargs)

                except (PlaywrightError, PlaywrightTimeoutError) as e:
                    # Check if this looks like a browser crash
                    error_msg = str(e).lower()
                    browser_crash_indicators = [
                        "target closed", "browser closed", "context closed",
                        "page closed", "connection closed", "protocol error"
                    ]

                    if any(indicator in error_msg for indicator in browser_crash_indicators):
                        raise BrowserCrashError(str(e))

                    # Re-raise as navigation error for other Playwright errors
                    raise NavigationError(f"Playwright error: {e}")

            return wrapper
        return decorator

    def record_error(self, error_key: str, error: Exception) -> None:
        """Record error occurrence for tracking."""
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        self.last_errors[error_key] = datetime.now()

        logger.error(f"Error recorded for {error_key}: {error} (count: {self.error_counts[error_key]})")

    def should_abort(self, error_key: str, max_errors: int = 10) -> bool:
        """Check if we should abort due to too many errors."""
        count = self.error_counts.get(error_key, 0)
        return count >= max_errors

    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of errors encountered."""
        return {
            "error_counts": dict(self.error_counts),
            "last_errors": {k: v.isoformat() for k, v in self.last_errors.items()},
            "total_errors": sum(self.error_counts.values()),
        }

    async def handle_rate_limit(self, retry_after: int = 60) -> None:
        """Handle rate limiting with appropriate delay."""
        logger.warning(f"Rate limited, waiting {retry_after} seconds...")
        await asyncio.sleep(retry_after)

    async def validate_node_state(self, node: CourtNode) -> bool:
        """Validate node state and attempt recovery if needed."""
        try:
            # Check if node has required fields
            if not node.full_path:
                raise ValidationError(f"Node missing full_path: {node}")

            if node.status == NavigationStatus.FAILED:
                # Check if we should retry failed nodes
                error_key = f"node_failure_{node.full_path}"
                if self.should_abort(error_key, max_errors=3):
                    logger.error(f"Too many failures for node {node.full_path}, marking as permanently failed")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error validating node {node.full_path}: {e}")
            return False


class CircuitBreaker:
    """Circuit breaker pattern for failing operations."""

    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time: Optional[datetime] = None
        self.state = "closed"  # closed, open, half_open

    def call(self, func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
        """Decorator to apply circuit breaker pattern."""

        @wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            if self.state == "open":
                if self._should_attempt_reset():
                    self.state = "half_open"
                else:
                    raise CaseLawError("Circuit breaker is open", retryable=False)

            try:
                result = await func(*args, **kwargs)
                self._on_success()
                return result

            except Exception as e:
                self._on_failure()
                raise

        return wrapper

    def _should_attempt_reset(self) -> bool:
        """Check if we should attempt to reset the circuit breaker."""
        if self.last_failure_time is None:
            return True

        return datetime.now() - self.last_failure_time > timedelta(seconds=self.recovery_timeout)

    def _on_success(self) -> None:
        """Handle successful operation."""
        self.failure_count = 0
        self.state = "closed"

    def _on_failure(self) -> None:
        """Handle failed operation."""
        self.failure_count += 1
        self.last_failure_time = datetime.now()

        if self.failure_count >= self.failure_threshold:
            self.state = "open"
            logger.warning(f"Circuit breaker opened after {self.failure_count} failures")


class RecoveryManager:
    """Manages recovery strategies for different types of failures."""

    def __init__(self, config: CaseLawConfig):
        self.config = config
        self.recovery_strategies: Dict[Type[Exception], Callable] = {
            BrowserCrashError: self._recover_browser_crash,
            RateLimitError: self._recover_rate_limit,
            ElementNotFoundError: self._recover_element_not_found,
            PlaywrightTimeoutError: self._recover_timeout,
        }

    async def recover_from_error(self, error: Exception, context: Dict[str, Any]) -> bool:
        """Attempt to recover from an error."""
        error_type = type(error)

        if error_type in self.recovery_strategies:
            try:
                await self.recovery_strategies[error_type](error, context)
                return True
            except Exception as recovery_error:
                logger.error(f"Recovery failed: {recovery_error}")
                return False

        return False

    async def _recover_browser_crash(self, error: BrowserCrashError, context: Dict[str, Any]) -> None:
        """Recover from browser crash by restarting browser."""
        logger.info("Attempting browser crash recovery...")

        navigator = context.get("navigator")
        if navigator:
            # Close existing browser
            try:
                await navigator.close()
            except Exception:
                pass  # Browser already closed

            # Restart browser
            await navigator.start()
            logger.info("Browser restarted successfully")
        else:
            raise CaseLawError("No navigator in context for browser recovery")

    async def _recover_rate_limit(self, error: RateLimitError, context: Dict[str, Any]) -> None:
        """Recover from rate limiting."""
        await asyncio.sleep(error.retry_after)

    async def _recover_element_not_found(self, error: ElementNotFoundError, context: Dict[str, Any]) -> None:
        """Recover from element not found by waiting and retrying."""
        page = context.get("page")
        if page:
            # Wait for page to stabilize
            await asyncio.sleep(self.config.navigation_delay * 2)

            # Try alternative selectors if available
            alternative_selectors = context.get("alternative_selectors", [])
            for selector in alternative_selectors:
                try:
                    element = await page.wait_for_selector(selector, timeout=5000)
                    if element:
                        logger.info(f"Found element with alternative selector: {selector}")
                        return
                except Exception:
                    continue

            raise ElementNotFoundError(f"No alternative selectors worked for {error.selector}")

    async def _recover_timeout(self, error: PlaywrightTimeoutError, context: Dict[str, Any]) -> None:
        """Recover from timeout by increasing wait time."""
        page = context.get("page")
        if page:
            # Wait longer and reload page
            await asyncio.sleep(self.config.navigation_delay * 3)
            await page.reload(wait_until="networkidle")
            logger.info("Page reloaded after timeout")


def resilient_operation(
    error_handler: ErrorHandler,
    recovery_manager: Optional[RecoveryManager] = None,
    max_attempts: int = 3,
):
    """Decorator combining retry, error handling, and recovery."""

    def decorator(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            context = kwargs.get("context", {})
            last_exception = None

            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)

                except Exception as e:
                    last_exception = e
                    error_key = f"{func.__name__}_{type(e).__name__}"
                    error_handler.record_error(error_key, e)

                    # Don't retry non-retryable errors
                    if isinstance(e, CaseLawError) and not e.retryable:
                        raise

                    # Attempt recovery if available
                    if recovery_manager and attempt < max_attempts - 1:
                        recovery_success = await recovery_manager.recover_from_error(e, context)
                        if recovery_success:
                            logger.info(f"Recovery successful for {type(e).__name__}")
                            # Add extra delay after recovery
                            await asyncio.sleep(error_handler.config.retry_delay)
                            continue

                    if attempt < max_attempts - 1:
                        delay = error_handler.config.retry_delay * (1.5 ** attempt)
                        logger.warning(f"Attempt {attempt + 1} failed, retrying in {delay:.1f}s: {e}")
                        await asyncio.sleep(delay)

            # All attempts failed
            if error_handler.should_abort(error_key):
                raise CaseLawError(f"Operation permanently failed: {last_exception}", retryable=False)

            raise last_exception

        return wrapper
    return decorator