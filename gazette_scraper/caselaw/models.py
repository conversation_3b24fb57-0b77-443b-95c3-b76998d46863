"""Data models for case-law scraping."""

from __future__ import annotations

import re
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional

from pydantic import BaseModel, Field, HttpUrl, field_validator


class NodeType(str, Enum):
    """Types of nodes in the court hierarchy tree."""

    COURT = "court"
    YEAR = "year"
    MONTH = "month"
    DAY = "day"


class CourtLevel(str, Enum):
    """Levels of courts in the Rwandan judicial system."""

    SUPREME = "supreme"
    APPEAL = "appeal"
    HIGH = "high"
    INTERMEDIATE = "intermediate"
    PRIMARY = "primary"
    COMMERCIAL = "commercial"
    MILITARY = "military"
    GACACA = "gacaca"
    OTHER = "other"


class NavigationStatus(str, Enum):
    """Status of navigation through tree nodes."""

    PENDING = "pending"
    DISCOVERED = "discovered"
    EXPANDING = "expanding"
    EXPANDED = "expanded"
    COMPLETED = "completed"
    FAILED = "failed"


class CaseLanguage(str, Enum):
    """Case document language."""

    ENGLISH = "en"
    KINYARWANDA = "rw"
    FRENCH = "fr"
    UNKNOWN = "unknown"


class CourtNode(BaseModel):
    """Represents a node in the hierarchical court tree."""

    # Navigation path
    court_name: str
    year: Optional[int] = None
    month: Optional[int] = None
    day: Optional[int] = None

    # Tree structure
    node_type: NodeType
    parent_path: Optional[str] = None
    full_path: str  # "Supreme Court/2023/January/Day_15"

    # Element identification
    click_selector: Optional[str] = None  # CSS selector for click element
    expand_icon_selector: Optional[str] = None  # Selector for "+" icon

    # Document discovery
    document_count: int = 0  # Count shown next to node
    actual_documents: int = 0  # Actually discovered documents

    # Navigation state
    status: NavigationStatus = NavigationStatus.PENDING
    last_attempt: Optional[datetime] = None
    attempt_count: int = 0
    error_message: Optional[str] = None

    # Browser state
    url: Optional[HttpUrl] = None
    page_signature: Optional[str] = None  # Hash of page content

    @field_validator("year")
    @classmethod
    def validate_year(cls, v: Optional[int]) -> Optional[int]:
        if v is not None and (v < 1990 or v > 2030):
            raise ValueError("Year must be between 1990 and 2030")
        return v

    @field_validator("month")
    @classmethod
    def validate_month(cls, v: Optional[int]) -> Optional[int]:
        if v is not None and (v < 1 or v > 12):
            raise ValueError("Month must be between 1 and 12")
        return v

    @field_validator("day")
    @classmethod
    def validate_day(cls, v: Optional[int]) -> Optional[int]:
        if v is not None and (v < 1 or v > 31):
            raise ValueError("Day must be between 1 and 31")
        return v


class CaseMetadata(BaseModel):
    """Metadata extracted from case titles and content."""

    case_number: Optional[str] = None
    case_type: Optional[str] = None  # Civil, Criminal, Administrative, etc.
    parties: List[str] = Field(default_factory=list)
    judge_names: List[str] = Field(default_factory=list)
    keywords: List[str] = Field(default_factory=list)
    summary: Optional[str] = None


class CaseLawFile(BaseModel):
    """Represents a single case-law PDF file."""

    # Schema versioning
    schema_version: str = Field(default="1.0.0", description="Schema version")

    # Basic file info
    title: str
    filename: str
    case_title: str  # Normalized case title
    size_bytes: Optional[int] = None

    # Court hierarchy
    court_name: str
    court_level: CourtLevel = Field(default=CourtLevel.OTHER)
    year: int
    month: int
    day: int

    # Case information
    case_date: Optional[datetime] = None
    case_metadata: CaseMetadata = Field(default_factory=CaseMetadata)
    language: CaseLanguage = Field(default=CaseLanguage.UNKNOWN, description="Document language")

    # URLs and paths
    download_url: HttpUrl  # Direct PDF download URL
    case_page_url: HttpUrl  # Page where case details are shown
    listing_url: HttpUrl  # Tree page where case was discovered

    # File management
    sha256: Optional[str] = None
    local_path: Optional[Path] = None
    gcs_path: Optional[str] = None

    # Timestamps
    discovered_at: datetime = Field(default_factory=datetime.utcnow)
    downloaded_at: Optional[datetime] = None

    # Navigation context
    navigation_path: str  # Full tree path where this was found
    tree_node_id: Optional[str] = None  # Reference to CourtNode

    @field_validator("case_title")
    @classmethod
    def validate_case_title(cls, v: str) -> str:
        if not v or not v.strip():
            raise ValueError("case_title cannot be empty")
        return " ".join(v.split())

    @field_validator("year")
    @classmethod
    def validate_year(cls, v: int) -> int:
        if v < 1990 or v > 2030:
            raise ValueError("Year must be between 1990 and 2030")
        return v

    @field_validator("month")
    @classmethod
    def validate_month(cls, v: int) -> int:
        if v < 1 or v > 12:
            raise ValueError("Month must be between 1 and 12")
        return v

    @field_validator("day")
    @classmethod
    def validate_day(cls, v: int) -> int:
        if v < 1 or v > 31:
            raise ValueError("Day must be between 1 and 31")
        return v

    @field_validator("sha256")
    @classmethod
    def validate_sha256(cls, v: Optional[str]) -> Optional[str]:
        if v is not None and not re.match(r"^[a-f0-9]{64}$", v):
            raise ValueError("sha256 must be a 64-character hexadecimal string")
        return v


class NavigationState(BaseModel):
    """Tracks the overall state of tree navigation."""

    # Progress tracking
    total_courts: int = 0
    discovered_courts: List[str] = Field(default_factory=list)
    completed_courts: List[str] = Field(default_factory=list)

    # Current position
    current_court: Optional[str] = None
    current_year: Optional[int] = None
    current_month: Optional[int] = None
    current_day: Optional[int] = None

    # Statistics
    total_nodes_discovered: int = 0
    total_nodes_expanded: int = 0
    total_documents_expected: int = 0
    total_documents_discovered: int = 0
    total_documents_downloaded: int = 0

    # Session info
    session_id: str
    started_at: datetime = Field(default_factory=datetime.utcnow)
    last_activity: datetime = Field(default_factory=datetime.utcnow)

    # Error tracking
    failed_nodes: List[str] = Field(default_factory=list)
    error_counts: Dict[str, int] = Field(default_factory=dict)

    # Browser state
    browser_contexts: int = 0
    active_pages: int = 0


class CaseLawConfig(BaseModel):
    """Configuration for case-law scraping."""

    # Base URLs
    base_url: str = "https://amategeko.gov.rw"
    caselaw_path: str = "/laws/judgement/2"

    # Browser settings
    browser_timeout: int = 30000  # milliseconds
    navigation_delay: float = 2.0  # seconds between clicks
    page_load_timeout: int = 15000  # milliseconds
    element_timeout: int = 10000  # milliseconds

    # Concurrency
    max_browser_contexts: int = 3
    max_concurrent_downloads: int = 2

    # Navigation behavior
    max_retry_attempts: int = 3
    retry_delay: float = 5.0  # seconds
    click_retry_attempts: int = 3

    # Error handling
    screenshot_on_error: bool = True
    debug_mode: bool = False
    headless: bool = True

    # File management
    output_dir: Path = Path("./caselaw_data")
    screenshots_dir: Path = Path("./screenshots")

    # Resume behavior
    resume_from_state: bool = True
    state_save_interval: int = 10  # Save state every N operations

    # Rate limiting
    request_delay: float = 1.0  # seconds between requests
    burst_protection: bool = True

    # Validation
    verify_pdf_content: bool = True
    min_pdf_size: int = 1024  # bytes

    # Dry run mode
    dry_run: bool = False

    # Google Cloud Storage configuration (optional)
    gcs_bucket: Optional[str] = None
    gcs_project_id: Optional[str] = None
    gcs_prefix: str = "case_law_pdfs/"

    # Supabase configuration (optional)
    supabase_url: Optional[str] = None
    supabase_key: Optional[str] = None


class CaseLawManifest(BaseModel):
    """Complete manifest of discovered case-law files."""

    files: List[CaseLawFile] = Field(default_factory=list)
    navigation_state: NavigationState

    # Statistics
    total_discovered: int = 0
    by_court: Dict[str, int] = Field(default_factory=dict)
    by_year: Dict[int, int] = Field(default_factory=dict)
    by_month: Dict[str, int] = Field(default_factory=dict)  # "YYYY-MM" -> count

    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    version: str = "1.0.0"