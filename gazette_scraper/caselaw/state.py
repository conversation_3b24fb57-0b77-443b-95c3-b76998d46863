"""State management for case-law tree traversal and resume capability."""

from __future__ import annotations

import json
import logging
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

from ..state import ScrapingState
from .models import CaseLawFile, CourtNode, NavigationState, NavigationStatus

logger = logging.getLogger(__name__)


class CaseLawState(ScrapingState):
    """Extended state management for case-law scraping with tree navigation tracking."""

    def __init__(self, db_path: Path):
        super().__init__(db_path)
        self._ensure_caselaw_tables()

    def _ensure_caselaw_tables(self) -> None:
        """Create case-law specific database tables."""
        with sqlite3.connect(self.db_path) as conn:
            # Court nodes table for tree state
            conn.execute("""
                CREATE TABLE IF NOT EXISTS court_nodes (
                    id TEXT PRIMARY KEY,
                    court_name TEXT NOT NULL,
                    year INTEGER,
                    month INTEGER,
                    day INTEGER,
                    node_type TEXT NOT NULL,
                    full_path TEXT NOT NULL UNIQUE,
                    parent_path TEXT,
                    click_selector TEXT,
                    document_count INTEGER DEFAULT 0,
                    actual_documents INTEGER DEFAULT 0,
                    status TEXT DEFAULT 'pending',
                    last_attempt TIMESTAMP,
                    attempt_count INTEGER DEFAULT 0,
                    error_message TEXT,
                    url TEXT,
                    page_signature TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Case files table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS case_files (
                    id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    filename TEXT NOT NULL,
                    case_title TEXT NOT NULL,
                    court_name TEXT NOT NULL,
                    court_level TEXT,
                    year INTEGER NOT NULL,
                    month INTEGER NOT NULL,
                    day INTEGER NOT NULL,
                    case_date TIMESTAMP,
                    language TEXT DEFAULT 'unknown',
                    download_url TEXT NOT NULL,
                    case_page_url TEXT NOT NULL,
                    listing_url TEXT NOT NULL,
                    navigation_path TEXT NOT NULL,
                    tree_node_id TEXT,
                    sha256 TEXT,
                    local_path TEXT,
                    gcs_path TEXT,
                    size_bytes INTEGER,
                    discovered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    downloaded_at TIMESTAMP,
                    status TEXT DEFAULT 'discovered',
                    error_message TEXT,
                    attempt_count INTEGER DEFAULT 0,
                    FOREIGN KEY (tree_node_id) REFERENCES court_nodes (id)
                )
            """)

            # Add language column to existing tables if it doesn't exist
            try:
                conn.execute("ALTER TABLE case_files ADD COLUMN language TEXT DEFAULT 'unknown'")
            except sqlite3.OperationalError:
                # Column already exists
                pass

            # Navigation sessions table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS navigation_sessions (
                    session_id TEXT PRIMARY KEY,
                    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    current_court TEXT,
                    current_year INTEGER,
                    current_month INTEGER,
                    current_day INTEGER,
                    total_nodes_discovered INTEGER DEFAULT 0,
                    total_nodes_expanded INTEGER DEFAULT 0,
                    total_documents_expected INTEGER DEFAULT 0,
                    total_documents_discovered INTEGER DEFAULT 0,
                    total_documents_downloaded INTEGER DEFAULT 0,
                    browser_contexts INTEGER DEFAULT 0,
                    active_pages INTEGER DEFAULT 0,
                    status TEXT DEFAULT 'active'
                )
            """)

            # Create indexes for performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_court_nodes_path ON court_nodes (full_path)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_court_nodes_status ON court_nodes (status)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_case_files_path ON case_files (navigation_path)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_case_files_status ON case_files (status)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_case_files_sha256 ON case_files (sha256)")

            conn.commit()

    def save_navigation_session(self, session: NavigationState) -> None:
        """Save navigation session state."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO navigation_sessions (
                    session_id, started_at, last_activity, current_court, current_year,
                    current_month, current_day, total_nodes_discovered, total_nodes_expanded,
                    total_documents_expected, total_documents_discovered, total_documents_downloaded,
                    browser_contexts, active_pages, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                session.session_id,
                session.started_at,
                session.last_activity,
                session.current_court,
                session.current_year,
                session.current_month,
                session.current_day,
                session.total_nodes_discovered,
                session.total_nodes_expanded,
                session.total_documents_expected,
                session.total_documents_discovered,
                session.total_documents_downloaded,
                session.browser_contexts,
                session.active_pages,
                "active"
            ))
            conn.commit()

    def load_navigation_session(self, session_id: str) -> Optional[NavigationState]:
        """Load navigation session state."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("""
                SELECT * FROM navigation_sessions WHERE session_id = ?
            """, (session_id,))
            row = cursor.fetchone()

            if row:
                return NavigationState(
                    session_id=row["session_id"],
                    started_at=datetime.fromisoformat(row["started_at"]),
                    last_activity=datetime.fromisoformat(row["last_activity"]),
                    current_court=row["current_court"],
                    current_year=row["current_year"],
                    current_month=row["current_month"],
                    current_day=row["current_day"],
                    total_nodes_discovered=row["total_nodes_discovered"],
                    total_nodes_expanded=row["total_nodes_expanded"],
                    total_documents_expected=row["total_documents_expected"],
                    total_documents_discovered=row["total_documents_discovered"],
                    total_documents_downloaded=row["total_documents_downloaded"],
                    browser_contexts=row["browser_contexts"],
                    active_pages=row["active_pages"],
                )
            return None

    def save_court_node(self, node: CourtNode) -> None:
        """Save or update a court node."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO court_nodes (
                    id, court_name, year, month, day, node_type, full_path, parent_path,
                    click_selector, document_count, actual_documents, status, last_attempt,
                    attempt_count, error_message, url, page_signature, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (
                node.full_path,  # Use full_path as ID
                node.court_name,
                node.year,
                node.month,
                node.day,
                node.node_type.value,
                node.full_path,
                node.parent_path,
                node.click_selector,
                node.document_count,
                node.actual_documents,
                node.status.value,
                node.last_attempt,
                node.attempt_count,
                node.error_message,
                str(node.url) if node.url else None,
                node.page_signature,
            ))
            conn.commit()

    def load_court_node(self, full_path: str) -> Optional[CourtNode]:
        """Load a court node by its full path."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("""
                SELECT * FROM court_nodes WHERE full_path = ?
            """, (full_path,))
            row = cursor.fetchone()

            if row:
                return CourtNode(
                    court_name=row["court_name"],
                    year=row["year"],
                    month=row["month"],
                    day=row["day"],
                    node_type=row["node_type"],
                    full_path=row["full_path"],
                    parent_path=row["parent_path"],
                    click_selector=row["click_selector"],
                    document_count=row["document_count"] or 0,
                    actual_documents=row["actual_documents"] or 0,
                    status=NavigationStatus(row["status"]),
                    last_attempt=datetime.fromisoformat(row["last_attempt"]) if row["last_attempt"] else None,
                    attempt_count=row["attempt_count"] or 0,
                    error_message=row["error_message"],
                    page_signature=row["page_signature"],
                )
            return None

    def get_nodes_by_status(self, status: NavigationStatus) -> List[CourtNode]:
        """Get all nodes with a specific status."""
        nodes = []
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("""
                SELECT * FROM court_nodes WHERE status = ? ORDER BY full_path
            """, (status.value,))

            for row in cursor.fetchall():
                node = CourtNode(
                    court_name=row["court_name"],
                    year=row["year"],
                    month=row["month"],
                    day=row["day"],
                    node_type=row["node_type"],
                    full_path=row["full_path"],
                    parent_path=row["parent_path"],
                    click_selector=row["click_selector"],
                    document_count=row["document_count"] or 0,
                    actual_documents=row["actual_documents"] or 0,
                    status=NavigationStatus(row["status"]),
                    last_attempt=datetime.fromisoformat(row["last_attempt"]) if row["last_attempt"] else None,
                    attempt_count=row["attempt_count"] or 0,
                    error_message=row["error_message"],
                    page_signature=row["page_signature"],
                )
                nodes.append(node)

        return nodes

    def get_completed_paths(self) -> Set[str]:
        """Get all completed navigation paths."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT full_path FROM court_nodes WHERE status = 'completed'
            """)
            return {row[0] for row in cursor.fetchall()}

    def get_day_nodes_for_discovery(self) -> List[CourtNode]:
        """Get day nodes that need case discovery."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("""
                SELECT * FROM court_nodes
                WHERE node_type = 'day'
                AND status IN ('expanded', 'discovered')
                AND (actual_documents = 0 OR actual_documents < document_count)
                ORDER BY full_path
            """)

            nodes = []
            for row in cursor.fetchall():
                node = CourtNode(
                    court_name=row["court_name"],
                    year=row["year"],
                    month=row["month"],
                    day=row["day"],
                    node_type=row["node_type"],
                    full_path=row["full_path"],
                    parent_path=row["parent_path"],
                    click_selector=row["click_selector"],
                    document_count=row["document_count"] or 0,
                    actual_documents=row["actual_documents"] or 0,
                    status=NavigationStatus(row["status"]),
                    last_attempt=datetime.fromisoformat(row["last_attempt"]) if row["last_attempt"] else None,
                    attempt_count=row["attempt_count"] or 0,
                    error_message=row["error_message"],
                    page_signature=row["page_signature"],
                )
                nodes.append(node)

            return nodes

    def save_case_file(self, case_file: CaseLawFile) -> None:
        """Save or update a case file (language-aware)."""
        with sqlite3.connect(self.db_path) as conn:
            # Include language in the unique identifier to distinguish language versions
            language_suffix = getattr(case_file, 'language', 'unknown')
            if hasattr(language_suffix, 'value'):
                language_suffix = language_suffix.value
            file_id = f"{case_file.navigation_path}_{case_file.case_title}_{language_suffix}"
            conn.execute("""
                INSERT OR REPLACE INTO case_files (
                    id, title, filename, case_title, court_name, court_level, year, month, day,
                    case_date, language, download_url, case_page_url, listing_url, navigation_path, tree_node_id,
                    sha256, local_path, gcs_path, size_bytes, discovered_at, downloaded_at, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                file_id,
                case_file.title,
                case_file.filename,
                case_file.case_title,
                case_file.court_name,
                case_file.court_level.value if case_file.court_level else None,
                case_file.year,
                case_file.month,
                case_file.day,
                case_file.case_date,
                language_suffix,
                str(case_file.download_url),
                str(case_file.case_page_url),
                str(case_file.listing_url),
                case_file.navigation_path,
                case_file.tree_node_id,
                case_file.sha256,
                str(case_file.local_path) if case_file.local_path else None,
                case_file.gcs_path,
                case_file.size_bytes,
                case_file.discovered_at,
                case_file.downloaded_at,
                "discovered",
            ))
            conn.commit()

    def get_case_files_for_download(self) -> List[CaseLawFile]:
        """Get case files that need to be downloaded."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("""
                SELECT * FROM case_files
                WHERE status IN ('discovered', 'failed')
                AND (downloaded_at IS NULL OR sha256 IS NULL)
                AND attempt_count < 3
                ORDER BY navigation_path, case_title
            """)

            files = []
            for row in cursor.fetchall():
                case_file = CaseLawFile(
                    title=row["title"],
                    filename=row["filename"],
                    case_title=row["case_title"],
                    court_name=row["court_name"],
                    year=row["year"],
                    month=row["month"],
                    day=row["day"],
                    case_date=datetime.fromisoformat(row["case_date"]) if row["case_date"] else None,
                    download_url=row["download_url"],
                    case_page_url=row["case_page_url"],
                    listing_url=row["listing_url"],
                    navigation_path=row["navigation_path"],
                    tree_node_id=row["tree_node_id"],
                    sha256=row["sha256"],
                    local_path=Path(row["local_path"]) if row["local_path"] else None,
                    gcs_path=row["gcs_path"],
                    size_bytes=row["size_bytes"],
                    discovered_at=datetime.fromisoformat(row["discovered_at"]),
                    downloaded_at=datetime.fromisoformat(row["downloaded_at"]) if row["downloaded_at"] else None,
                )
                files.append(case_file)

            return files

    def mark_case_downloaded(self, case_file: CaseLawFile, local_path: Path, sha256: str) -> None:
        """Mark a case file as successfully downloaded."""
        with sqlite3.connect(self.db_path) as conn:
            file_id = f"{case_file.navigation_path}_{case_file.case_title}"
            conn.execute("""
                UPDATE case_files
                SET sha256 = ?, local_path = ?, downloaded_at = CURRENT_TIMESTAMP,
                    status = 'downloaded', size_bytes = ?
                WHERE id = ?
            """, (sha256, str(local_path), case_file.size_bytes, file_id))
            conn.commit()

    def mark_case_failed(self, case_file: CaseLawFile, error_message: str) -> None:
        """Mark a case file download as failed."""
        with sqlite3.connect(self.db_path) as conn:
            file_id = f"{case_file.navigation_path}_{case_file.case_title}"
            conn.execute("""
                UPDATE case_files
                SET status = 'failed', error_message = ?, attempt_count = attempt_count + 1
                WHERE id = ?
            """, (error_message, file_id))
            conn.commit()

    def get_navigation_progress(self) -> Dict[str, int]:
        """Get overall navigation progress statistics."""
        with sqlite3.connect(self.db_path) as conn:
            # Node statistics
            cursor = conn.execute("""
                SELECT status, COUNT(*) as count FROM court_nodes GROUP BY status
            """)
            node_stats = {row[0]: row[1] for row in cursor.fetchall()}

            # Case statistics
            cursor = conn.execute("""
                SELECT status, COUNT(*) as count FROM case_files GROUP BY status
            """)
            case_stats = {row[0]: row[1] for row in cursor.fetchall()}

            # Document counts
            cursor = conn.execute("""
                SELECT
                    SUM(document_count) as expected_docs,
                    SUM(actual_documents) as discovered_docs
                FROM court_nodes WHERE node_type = 'day'
            """)
            doc_row = cursor.fetchone()

            return {
                "nodes": node_stats,
                "cases": case_stats,
                "expected_documents": doc_row[0] or 0,
                "discovered_documents": doc_row[1] or 0,
                "downloaded_documents": case_stats.get("downloaded", 0),
            }

    def get_resume_position(self) -> Optional[Tuple[str, str]]:
        """Get the position to resume navigation from."""
        with sqlite3.connect(self.db_path) as conn:
            # Find the first incomplete path
            cursor = conn.execute("""
                SELECT full_path, node_type FROM court_nodes
                WHERE status IN ('pending', 'discovered', 'expanding')
                ORDER BY full_path
                LIMIT 1
            """)
            row = cursor.fetchone()

            if row:
                return row[0], row[1]
            return None

    def cleanup_old_sessions(self, max_age_hours: int = 24) -> None:
        """Clean up old navigation sessions."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                UPDATE navigation_sessions
                SET status = 'expired'
                WHERE last_activity < datetime('now', '-{} hours')
                AND status = 'active'
            """.format(max_age_hours))
            conn.commit()

    def export_navigation_state(self, output_path: Path) -> None:
        """Export complete navigation state to JSON for backup/analysis."""
        state_data = {
            "exported_at": datetime.now().isoformat(),
            "progress": self.get_navigation_progress(),
            "nodes": [],
            "cases": [],
        }

        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row

            # Export nodes
            cursor = conn.execute("SELECT * FROM court_nodes ORDER BY full_path")
            for row in cursor.fetchall():
                state_data["nodes"].append(dict(row))

            # Export cases
            cursor = conn.execute("SELECT * FROM case_files ORDER BY navigation_path, case_title")
            for row in cursor.fetchall():
                state_data["cases"].append(dict(row))

        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(state_data, f, indent=2, default=str)

        logger.info(f"Navigation state exported to: {output_path}")

    def import_navigation_state(self, input_path: Path) -> None:
        """Import navigation state from JSON backup."""
        with open(input_path, "r", encoding="utf-8") as f:
            state_data = json.load(f)

        with sqlite3.connect(self.db_path) as conn:
            # Import nodes
            for node_data in state_data.get("nodes", []):
                conn.execute("""
                    INSERT OR REPLACE INTO court_nodes (
                        id, court_name, year, month, day, node_type, full_path, parent_path,
                        click_selector, document_count, actual_documents, status, last_attempt,
                        attempt_count, error_message, url, page_signature, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, tuple(node_data[k] for k in [
                    "id", "court_name", "year", "month", "day", "node_type", "full_path", "parent_path",
                    "click_selector", "document_count", "actual_documents", "status", "last_attempt",
                    "attempt_count", "error_message", "url", "page_signature", "created_at", "updated_at"
                ]))

            # Import cases
            for case_data in state_data.get("cases", []):
                conn.execute("""
                    INSERT OR REPLACE INTO case_files (
                        id, title, filename, case_title, court_name, court_level, year, month, day,
                        case_date, download_url, case_page_url, listing_url, navigation_path, tree_node_id,
                        sha256, local_path, gcs_path, size_bytes, discovered_at, downloaded_at, status,
                        error_message, attempt_count
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, tuple(case_data[k] for k in [
                    "id", "title", "filename", "case_title", "court_name", "court_level", "year", "month", "day",
                    "case_date", "download_url", "case_page_url", "listing_url", "navigation_path", "tree_node_id",
                    "sha256", "local_path", "gcs_path", "size_bytes", "discovered_at", "downloaded_at", "status",
                    "error_message", "attempt_count"
                ]))

            conn.commit()

        logger.info(f"Navigation state imported from: {input_path}")

    def is_case_already_downloaded(self, case_file: CaseLawFile) -> bool:
        """Check if a case file has already been downloaded (language-aware)."""
        with sqlite3.connect(self.db_path) as conn:
            # Include language in the unique identifier to distinguish language versions
            language_suffix = getattr(case_file, 'language', 'unknown')
            if hasattr(language_suffix, 'value'):
                language_suffix = language_suffix.value
            file_id = f"{case_file.navigation_path}_{case_file.case_title}_{language_suffix}"

            cursor = conn.execute("""
                SELECT sha256 FROM case_files
                WHERE id = ? AND status = 'downloaded' AND sha256 IS NOT NULL
            """, (file_id,))
            return cursor.fetchone() is not None