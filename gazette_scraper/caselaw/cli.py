"""Command-line interface for the case-law scraper with hybrid deployment support."""

import asyncio
import logging
import os
import sys
from pathlib import Path
from typing import Optional

import click
from fastapi import FastAP<PERSON>
from fastapi.responses import JSONResponse
import uvicorn

from ..config import load_caselaw_config
from .pipeline import CaseLawPipeline
from .models import CourtNode, NodeType

logger = logging.getLogger(__name__)

# FastAPI app for Cloud Run health checks
app = FastAPI(title="Rwanda Case-Law Scraper", version="1.0.0")

@app.get("/health")
async def health_check():
    """Health check endpoint for Cloud Run."""
    return JSONResponse({"status": "healthy", "service": "rwanda-caselaw-scraper"})

@app.get("/ready")
async def readiness_check():
    """Readiness check endpoint for Cloud Run."""
    return JSONResponse({"status": "ready", "service": "rwanda-caselaw-scraper"})

@app.get("/")
async def root():
    """Root endpoint with service information."""
    return JSONResponse({
        "service": "Rwanda Case-Law Scraper",
        "version": "1.0.0",
        "deployment": "hybrid",
        "storage": {
            "metadata": "Supabase",
            "pdfs": "Google Cloud Storage"
        },
        "endpoints": {
            "health": "/health",
            "ready": "/ready",
            "scrape": "/scrape"
        }
    })

@app.post("/scrape")
async def trigger_scrape(
    court_name: Optional[str] = None,
    year: Optional[int] = None,
    month: Optional[int] = None,
    day: Optional[int] = None
):
    """Trigger a scraping job via HTTP endpoint."""
    try:
        # Load configuration
        config = load_caselaw_config()
        
        # Create target node if parameters provided
        target_node = None
        if court_name:
            target_node = CourtNode(
                court_name=court_name,
                year=year,
                month=month,
                day=day,
                node_type=NodeType.DAY if day else (NodeType.MONTH if month else (NodeType.YEAR if year else NodeType.COURT)),
                full_path=f"{court_name}" + (f"/{year}" if year else "") + (f"/{month}" if month else "") + (f"/{day}" if day else ""),
                document_count=0
            )
        
        # Run scraping pipeline
        async with CaseLawPipeline(config) as pipeline:
            if target_node:
                result = await pipeline.scrape_node(target_node)
            else:
                result = await pipeline.run_full_scrape()
        
        return JSONResponse({
            "status": "success",
            "result": {
                "total_discovered": result.total_discovered,
                "downloaded": result.downloaded,
                "skipped": result.skipped,
                "errors": result.errors
            }
        })
    
    except Exception as e:
        logger.error(f"Scraping failed: {e}")
        return JSONResponse(
            {"status": "error", "message": str(e)},
            status_code=500
        )


@click.group()
@click.option("--config", "-c", type=click.Path(exists=True), help="Configuration file path")
@click.option("--verbose", "-v", is_flag=True, help="Enable verbose logging")
def cli(config: Optional[str], verbose: bool):
    """Rwanda Case-Law Scraper CLI - Hybrid Deployment."""
    # Set up logging
    log_level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # Set config path in environment if provided
    if config:
        os.environ["CASELAW_CONFIG_PATH"] = config


@cli.command()
@click.option("--host", default="0.0.0.0", help="Host to bind to")
@click.option("--port", default=8080, help="Port to bind to")
@click.option("--reload", is_flag=True, help="Enable auto-reload for development")
def serve(host: str, port: int, reload: bool):
    """Start the web server for Cloud Run deployment."""
    click.echo(f"🚀 Starting Rwanda Case-Law Scraper server on {host}:{port}")
    click.echo("📋 Deployment: Hybrid (Supabase + GCS)")
    
    uvicorn.run(
        "gazette_scraper.caselaw.cli:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )


@cli.command()
@click.option("--court", help="Specific court to scrape")
@click.option("--year", type=int, help="Specific year to scrape")
@click.option("--month", type=int, help="Specific month to scrape")
@click.option("--day", type=int, help="Specific day to scrape")
@click.option("--dry-run", is_flag=True, help="Run without downloading files")
def scrape(court: Optional[str], year: Optional[int], month: Optional[int], day: Optional[int], dry_run: bool):
    """Run the case-law scraping pipeline."""
    async def run_scrape():
        try:
            # Load configuration
            config = load_caselaw_config()
            if dry_run:
                config.dry_run = True
            
            click.echo("🚀 Starting Rwanda Case-Law Scraper")
            click.echo(f"📋 Configuration: {config}")
            
            # Create target node if parameters provided
            target_node = None
            if court:
                # Helper function to convert month number to name
                def month_num_to_name(month_num: int) -> str:
                    month_names = {
                        1: "January", 2: "February", 3: "March", 4: "April",
                        5: "May", 6: "June", 7: "July", 8: "August",
                        9: "September", 10: "October", 11: "November", 12: "December"
                    }
                    return month_names.get(month_num, str(month_num))

                # Build the full path in the format expected by the navigator
                if day and month and year:
                    # For day nodes, use the format: "Court/Year/MonthName/Day_N"
                    month_name = month_num_to_name(month)
                    full_path = f"{court}/{year}/{month_name}/Day_{day}"
                elif month and year:
                    # For month nodes, use the format: "Court/Year/MonthName"
                    month_name = month_num_to_name(month)
                    full_path = f"{court}/{year}/{month_name}"
                elif year:
                    # For year nodes, use the format: "Court/Year"
                    full_path = f"{court}/{year}"
                else:
                    # For court nodes, use the format: "Court"
                    full_path = court

                target_node = CourtNode(
                    court_name=court,
                    year=year,
                    month=month,
                    day=day,
                    node_type=NodeType.DAY if day else (NodeType.MONTH if month else (NodeType.YEAR if year else NodeType.COURT)),
                    full_path=full_path,
                    document_count=0
                )
                click.echo(f"🎯 Target: {target_node.full_path}")
            
            # Run scraping pipeline
            async with CaseLawPipeline(config) as pipeline:
                if target_node:
                    result = await pipeline.scrape_node(target_node)
                else:
                    result = await pipeline.run_full_scrape()
            
            # Display results
            click.echo("\n📊 Scraping Results:")
            click.echo(f"   Total Discovered: {result.total_discovered}")
            click.echo(f"   Downloaded: {result.downloaded}")
            click.echo(f"   Skipped: {result.skipped}")
            click.echo(f"   Errors: {result.errors}")
            
            if result.errors > 0:
                click.echo("\n❌ Errors encountered:")
                for error in result.error_messages:
                    click.echo(f"   - {error}")
            
            click.echo("\n✅ Scraping completed!")
            
        except Exception as e:
            click.echo(f"❌ Scraping failed: {e}")
            sys.exit(1)
    
    asyncio.run(run_scrape())


@cli.command()
def test():
    """Test the hybrid deployment configuration."""
    click.echo("🧪 Testing hybrid deployment configuration...")
    
    try:
        # Test configuration loading
        config = load_caselaw_config()
        click.echo("✅ Configuration loaded successfully")
        
        # Test GCS connection
        if hasattr(config, 'gcs_bucket') and config.gcs_bucket:
            click.echo(f"✅ GCS bucket configured: {config.gcs_bucket}")
        else:
            click.echo("❌ GCS bucket not configured")
        
        # Test Supabase connection
        supabase_url = os.getenv("SUPABASE_URL")
        if supabase_url:
            click.echo(f"✅ Supabase URL configured: {supabase_url}")
        else:
            click.echo("❌ Supabase URL not configured")
        
        click.echo("✅ All tests passed!")
        
    except Exception as e:
        click.echo(f"❌ Test failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    cli()
