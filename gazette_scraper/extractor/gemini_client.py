"""Gemini AI client for PDF extraction."""

from __future__ import annotations

import io
import json
import logging
import os
import re
import time
from pathlib import Path
from typing import Any

import google.generativeai as genai
from json_repair import repair_json
from PIL import Image
from pydantic import ValidationError

from .schema import GeminiResponse

logger = logging.getLogger(__name__)


class GeminiClient:
    """Client for interacting with Gemini AI for PDF extraction."""
    
    def __init__(self, api_key: str | None = None, model_name: str = "gemini-2.0-flash-exp") -> None:
        """Initialize the Gemini client.
        
        Args:
            api_key: Google API key. If None, will try to get from GOOGLE_API_KEY env var.
            model_name: Name of the Gemini model to use.
        """
        self.api_key = api_key or os.getenv("GOOGLE_API_KEY")
        if not self.api_key:
            raise ValueError("GOOGLE_API_KEY environment variable or api_key parameter required")
        
        genai.configure(api_key=self.api_key)
        self.model_name = model_name
        self.model = genai.GenerativeModel(model_name)
        
        # Load prompt template
        prompt_path = Path(__file__).parent / "prompts" / "model_prompt.txt"
        with open(prompt_path) as f:
            self.prompt_template = f.read().strip()
    
    def _create_response_schema(self) -> dict[str, Any]:
        """Create the JSON schema for structured output."""
        return {
            "type": "object",
            "properties": {
                "doc": {
                    "type": "object",
                    "properties": {
                        "title": {"type": "string"},
                        "date_iso": {"type": "string"},
                        "source_filename": {"type": "string"}
                    },
                    "required": ["source_filename"]
                },
                "pages": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "page_index": {"type": "integer"},
                            "blocks": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "section": {
                                            "type": "string",
                                            "enum": ["title", "article", "table", "footer", "other"]
                                        },
                                        "lang": {
                                            "type": ["string", "null"],
                                            "enum": ["rw", "en", "fr", None]
                                        },
                                        "article_no": {"type": ["integer", "null"]},
                                        "article_title": {"type": ["string", "null"]},
                                        "text": {"type": ["string", "null"]},
                                        "table_html": {"type": ["string", "null"]},
                                        "uncertain": {"type": "boolean", "default": False}
                                    },
                                    "required": ["section"]
                                }
                            },
                            "stats": {
                                "type": "object",
                                "properties": {
                                    "block_counts_by_section": {"type": "object"},
                                    "block_counts_by_lang": {"type": "object"},
                                    "article_numbers_found": {
                                        "type": "array",
                                        "items": {"type": "integer"}
                                    }
                                }
                            },
                            "errors": {
                                "type": "array",
                                "items": {"type": "string"}
                            }
                        },
                        "required": ["page_index", "blocks"]
                    }
                }
            },
            "required": ["pages"]
        }
    
    def extract_pages(
        self,
        pdf_path: Path | None = None,
        images: list[Image.Image] | None = None,
        source_filename: str = "",
        max_retries: int = 2,
        batch_size: int = 4
    ) -> GeminiResponse:
        """Extract content from PDF pages using Gemini.

        Args:
            pdf_path: Path to PDF file (will be converted to images)
            images: List of PIL Images (alternative to pdf_path)
            source_filename: Name of the source file for metadata
            max_retries: Maximum number of retry attempts
            batch_size: Number of pages to process in each batch

        Returns:
            GeminiResponse with extracted content

        Raises:
            ValueError: If neither pdf_path nor images provided
            RuntimeError: If extraction fails after retries
        """
        if not pdf_path and not images:
            raise ValueError("Either pdf_path or images must be provided")
        
        if pdf_path and not images:
            images = self._pdf_to_images(pdf_path)
        
        if not images:
            raise ValueError("No images to process")

        # Try batch processing first, then fallback to individual pages
        try:
            return self._extract_with_batching(images, source_filename, max_retries, batch_size)
        except RuntimeError as e:
            logger.warning(f"Batch processing failed: {e}")
            if len(images) > 1:
                logger.info("Falling back to individual page processing")
                return self._extract_individual_pages(images, source_filename, max_retries)
            else:
                raise

    def _extract_with_batching(
        self,
        images: list[Image.Image],
        source_filename: str,
        max_retries: int,
        batch_size: int
    ) -> GeminiResponse:
        """Extract content using batch processing.

        Args:
            images: List of PIL Images
            source_filename: Name of the source file
            max_retries: Maximum retry attempts
            batch_size: Number of pages per batch

        Returns:
            GeminiResponse with extracted content
        """
        all_pages = []

        # Process images in batches
        for i in range(0, len(images), batch_size):
            batch_images = images[i:i + batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}/{(len(images) + batch_size - 1)//batch_size} ({len(batch_images)} pages)")

            batch_response = self._extract_batch(batch_images, source_filename, max_retries)
            all_pages.extend(batch_response.pages)

        # Create combined response
        doc_metadata = None
        if all_pages and hasattr(all_pages[0], 'doc'):
            doc_metadata = all_pages[0].doc

        if not doc_metadata:
            from .schema import DocumentMetadata
            doc_metadata = DocumentMetadata(source_filename=source_filename)

        return GeminiResponse(doc=doc_metadata, pages=all_pages)

    def _extract_batch(self, images: list[Image.Image], source_filename: str, max_retries: int) -> GeminiResponse:
        """Extract content from a batch of images.

        Args:
            images: Batch of PIL Images
            source_filename: Name of the source file
            max_retries: Maximum retry attempts

        Returns:
            GeminiResponse for this batch
        """
        # Prepare content for Gemini
        content = [self.prompt_template] + images

        # Add schema information
        schema = self._create_response_schema()

        for attempt in range(max_retries + 1):
            try:
                logger.info(f"Calling Gemini API (attempt {attempt + 1}/{max_retries + 1})")
                
                # Configure generation with JSON schema
                generation_config = genai.GenerationConfig(
                    response_mime_type="application/json",
                    temperature=0.1,  # Low temperature for consistent extraction
                )
                
                response = self.model.generate_content(
                    content,
                    generation_config=generation_config
                )
                
                # Parse and validate response
                response_text = response.text.strip()
                logger.debug(f"Raw Gemini response: {response_text[:500]}...")
                
                try:
                    response_data = self._parse_json_with_repair(response_text)
                    logger.info("Successfully parsed JSON response")
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse JSON response after all repair attempts: {e}")
                    if attempt < max_retries:
                        time.sleep(2 ** attempt)  # Exponential backoff
                        continue
                    raise RuntimeError(f"Invalid JSON response after {max_retries + 1} attempts: {e}")
                
                # Validate with Pydantic
                try:
                    gemini_response = GeminiResponse(**response_data)
                    
                    # Set source filename if not provided
                    if gemini_response.doc and not gemini_response.doc.source_filename:
                        gemini_response.doc.source_filename = source_filename
                    elif not gemini_response.doc:
                        from .schema import DocumentMetadata
                        gemini_response.doc = DocumentMetadata(source_filename=source_filename)
                    
                    logger.info(f"Successfully extracted {len(gemini_response.pages)} pages from batch")
                    return gemini_response
                    
                except ValidationError as e:
                    logger.warning(f"Response validation failed: {e}")
                    if attempt < max_retries:
                        time.sleep(2 ** attempt)
                        continue
                    raise RuntimeError(f"Response validation failed after {max_retries + 1} attempts: {e}")
                
            except Exception as e:
                logger.error(f"Gemini API call failed (attempt {attempt + 1}): {e}")
                if attempt < max_retries:
                    time.sleep(2 ** attempt)
                    continue
                raise RuntimeError(f"Gemini API failed after {max_retries + 1} attempts: {e}")
        
        raise RuntimeError("Unexpected error in batch extraction")

    def _extract_individual_pages(
        self,
        images: list[Image.Image],
        source_filename: str,
        max_retries: int
    ) -> GeminiResponse:
        """Extract content from individual pages as fallback.

        Args:
            images: List of PIL Images
            source_filename: Name of the source file
            max_retries: Maximum retry attempts

        Returns:
            GeminiResponse with extracted content
        """
        all_pages = []

        for i, image in enumerate(images):
            logger.info(f"Processing individual page {i + 1}/{len(images)}")

            try:
                # Process single page
                batch_response = self._extract_batch([image], source_filename, max_retries)
                if batch_response.pages:
                    # Update page index to match position in document
                    page = batch_response.pages[0]
                    page.page_index = i
                    all_pages.append(page)
                else:
                    # Create minimal page structure for failed extraction
                    from .schema import PageData
                    all_pages.append(PageData(
                        page_index=i,
                        blocks=[],
                        errors=[f"Failed to extract content from page {i + 1}"]
                    ))
            except Exception as e:
                logger.error(f"Failed to process page {i + 1}: {e}")
                # Create error page
                from .schema import PageData
                all_pages.append(PageData(
                    page_index=i,
                    blocks=[],
                    errors=[f"Extraction error: {str(e)}"]
                ))

        # Create combined response
        from .schema import DocumentMetadata
        doc_metadata = DocumentMetadata(source_filename=source_filename)

        logger.info(f"Individual page processing complete: {len(all_pages)} pages")
        return GeminiResponse(doc=doc_metadata, pages=all_pages)
    
    def _pdf_to_images(self, pdf_path: Path) -> list[Image.Image]:
        """Convert PDF to list of PIL Images.
        
        Args:
            pdf_path: Path to PDF file
            
        Returns:
            List of PIL Images, one per page
        """
        try:
            import fitz  # PyMuPDF
        except ImportError:
            raise ImportError("PyMuPDF (fitz) is required for PDF processing. Install with: pip install PyMuPDF")
        
        images = []
        doc = fitz.open(pdf_path)
        
        try:
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                
                # Render page to image at 300 DPI
                mat = fitz.Matrix(300/72, 300/72)  # 300 DPI scaling
                pix = page.get_pixmap(matrix=mat)
                
                # Convert to PIL Image
                img_data = pix.tobytes("png")
                img = Image.open(io.BytesIO(img_data))
                images.append(img)
                
        finally:
            doc.close()
        
        logger.info(f"Converted PDF to {len(images)} images")
        return images

    def _clean_json_response(self, response_text: str) -> str:
        """Clean and repair malformed JSON response.

        Args:
            response_text: Raw response text from Gemini

        Returns:
            Cleaned JSON string
        """
        # Remove any text before the first {
        start_idx = response_text.find('{')
        if start_idx > 0:
            response_text = response_text[start_idx:]

        # Remove any text after the last }
        end_idx = response_text.rfind('}')
        if end_idx > 0:
            response_text = response_text[:end_idx + 1]

        # Remove control characters that cause JSON parsing issues
        response_text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', response_text)

        # Fix common JSON issues
        response_text = self._fix_common_json_issues(response_text)

        return response_text

    def _fix_common_json_issues(self, text: str) -> str:
        """Fix common JSON formatting issues.

        Args:
            text: JSON text to fix

        Returns:
            Fixed JSON text
        """
        # Fix unescaped quotes in strings
        text = re.sub(r'(?<!\\)"(?=.*")', '\\"', text)

        # Fix trailing commas
        text = re.sub(r',(\s*[}\]])', r'\1', text)

        # Fix missing commas between objects/arrays
        text = re.sub(r'}\s*{', '},{', text)
        text = re.sub(r']\s*\[', '],[', text)

        # Fix unquoted keys
        text = re.sub(r'(\w+):', r'"\1":', text)

        return text

    def _parse_json_with_repair(self, response_text: str) -> dict[str, Any]:
        """Parse JSON with multiple repair strategies.

        Args:
            response_text: Raw response text

        Returns:
            Parsed JSON data

        Raises:
            json.JSONDecodeError: If all repair strategies fail
        """
        # Strategy 1: Try parsing as-is
        try:
            result = json.loads(response_text)
            return self._fix_schema_issues(result)
        except json.JSONDecodeError:
            logger.debug("Initial JSON parse failed, trying repair strategies")

        # Strategy 2: Clean and try again
        try:
            cleaned_text = self._clean_json_response(response_text)
            result = json.loads(cleaned_text)
            return self._fix_schema_issues(result)
        except json.JSONDecodeError:
            logger.debug("Cleaned JSON parse failed, trying json-repair")

        # Strategy 3: Use json-repair library
        try:
            repaired_text = repair_json(response_text)
            result = json.loads(repaired_text)
            # Ensure we have a dictionary, not a list
            if isinstance(result, list):
                logger.debug("json-repair returned a list, wrapping in pages structure")
                result = {"pages": result}
            return self._fix_schema_issues(result)
        except (json.JSONDecodeError, Exception) as e:
            logger.debug(f"json-repair failed: {e}, trying manual repair")

        # Strategy 4: Manual repair on cleaned text
        try:
            cleaned_text = self._clean_json_response(response_text)
            repaired_text = repair_json(cleaned_text)
            result = json.loads(repaired_text)
            # Ensure we have a dictionary, not a list
            if isinstance(result, list):
                logger.debug("Manual repair returned a list, wrapping in pages structure")
                result = {"pages": result}
            return self._fix_schema_issues(result)
        except (json.JSONDecodeError, Exception) as e:
            logger.debug(f"Manual repair failed: {e}, trying truncation")

        # Strategy 5: Try to extract valid JSON from truncated response
        try:
            result = self._extract_partial_json(response_text)
            return self._fix_schema_issues(result)
        except json.JSONDecodeError as e:
            logger.error(f"All JSON repair strategies failed: {e}")
            raise

    def _fix_schema_issues(self, data: dict[str, Any]) -> dict[str, Any]:
        """Fix common schema validation issues in the parsed data.

        Args:
            data: Parsed JSON data

        Returns:
            Fixed JSON data
        """
        if not isinstance(data, dict):
            return data

        # If this looks like a single page object, wrap it in the expected structure
        if "page" in data or "page_index" in data:
            logger.debug("Converting single page object to full response structure")
            page_data = data.copy()
            data = {
                "doc": {"source_filename": ""},
                "pages": [page_data]
            }

        # Fix pages array
        if "pages" in data and isinstance(data["pages"], list):
            for page in data["pages"]:
                if isinstance(page, dict):
                    # Fix page_index field (sometimes comes as 'page')
                    if "page" in page and "page_index" not in page:
                        page["page_index"] = page.pop("page")

                    # Ensure page_index is an integer
                    if "page_index" in page:
                        try:
                            page["page_index"] = int(page["page_index"]) - 1  # Convert to 0-based
                        except (ValueError, TypeError):
                            page["page_index"] = 0
                    else:
                        page["page_index"] = 0

                    # Fix blocks array
                    if "blocks" in page and isinstance(page["blocks"], list):
                        for block in page["blocks"]:
                            if isinstance(block, dict):
                                # Map old field names to new schema
                                if "content" in block and "text" not in block:
                                    block["text"] = block.pop("content")
                                if "block_type" in block and "section" not in block:
                                    block["section"] = block.pop("block_type")

                                # Fix language field - map invalid values to 'rw'
                                if "lang" in block:
                                    lang = block["lang"]
                                    if lang not in ["rw", "en", "fr"]:
                                        # Map common variations
                                        lang_map = {
                                            "other": "rw",
                                            "kinyarwanda": "rw",
                                            "english": "en",
                                            "french": "fr",
                                            "kn": "rw",
                                            "kin": "rw"
                                        }
                                        block["lang"] = lang_map.get(lang.lower(), "rw")

                                # Ensure required fields exist
                                if "text" not in block:
                                    block["text"] = ""
                                if "section" not in block:
                                    block["section"] = "other"
                                if "lang" not in block:
                                    block["lang"] = "rw"
                    else:
                        page["blocks"] = []

        # Ensure doc exists
        if "doc" not in data:
            data["doc"] = {"source_filename": ""}

        return data

    def _extract_partial_json(self, response_text: str) -> dict[str, Any]:
        """Extract partial JSON from truncated response.

        Args:
            response_text: Raw response text

        Returns:
            Partial JSON data with minimal structure
        """
        # Try to find the main structure and create a minimal valid JSON
        cleaned_text = self._clean_json_response(response_text)

        # Look for pages array start
        pages_match = re.search(r'"pages"\s*:\s*\[', cleaned_text)
        if pages_match:
            # Try to extract at least the beginning of the pages array
            start_pos = pages_match.start()
            before_pages = cleaned_text[:start_pos]

            # Create minimal structure
            minimal_json = {
                "pages": []
            }

            # Try to extract any complete page objects
            page_pattern = r'\{\s*"page_index"\s*:\s*(\d+).*?\}'
            page_matches = re.finditer(page_pattern, cleaned_text, re.DOTALL)

            for match in page_matches:
                try:
                    page_text = match.group(0)
                    # Try to parse this page
                    page_data = json.loads(page_text)
                    minimal_json["pages"].append(page_data)
                except json.JSONDecodeError:
                    # Create minimal page structure
                    page_index = int(match.group(1))
                    minimal_json["pages"].append({
                        "page_index": page_index,
                        "blocks": [],
                        "errors": ["Partial extraction due to malformed JSON"]
                    })

            return minimal_json

        # Fallback: return minimal structure
        return {
            "pages": [{
                "page_index": 0,
                "blocks": [],
                "errors": ["Failed to parse JSON response"]
            }]
        }
