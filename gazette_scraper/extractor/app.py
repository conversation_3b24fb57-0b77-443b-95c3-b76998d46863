"""Main CLI application for gazette extraction."""

from __future__ import annotations

import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Any

import click
from dotenv import load_dotenv
from rich.console import Console
from rich.logging import RichHandler
from rich.progress import Progress, SpinnerColumn, TextColumn

from .gemini_client import GeminiClient
from .postprocess import PostProcessor
from .preview import PreviewGenerator
from .schema import RunReport

# Load environment variables from .env file
load_dotenv()

console = Console()
logger = logging.getLogger(__name__)


def setup_logging(verbose: bool = False) -> None:
    """Set up logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    
    # Console handler with Rich
    console_handler = RichHandler(
        console=console,
        show_time=True,
        show_path=False,
        markup=True
    )
    console_handler.setLevel(level)
    
    # File handler
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    file_handler = logging.FileHandler(log_dir / "gazette_extractor.log")
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(
        logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
    )
    
    # Root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    # Suppress noisy third-party loggers
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("google").setLevel(logging.WARNING)


@click.command()
@click.option(
    "--pdf",
    type=click.Path(exists=True, path_type=Path),
    required=True,
    help="Input PDF file to extract"
)
@click.option(
    "--out",
    type=click.Path(path_type=Path),
    default="./out",
    help="Output directory for extracted content"
)
@click.option(
    "--batch-pages",
    type=int,
    default=4,
    help="Number of pages to send per Gemini API call"
)
@click.option(
    "--api-key",
    type=str,
    help="Google API key (overrides GOOGLE_API_KEY env var)"
)
@click.option(
    "--model",
    type=str,
    default="gemini-2.0-flash-exp",
    help="Gemini model to use"
)
@click.option(
    "--verbose", "-v",
    is_flag=True,
    help="Enable verbose logging"
)
def extract_gazette(
    pdf: Path,
    out: Path,
    batch_pages: int,
    api_key: str | None,
    model: str,
    verbose: bool
) -> None:
    """Extract structured content from a tri-lingual Rwandan Gazette PDF.
    
    This tool uses Gemini AI to extract articles, tables, and metadata from
    Official Gazette PDFs, producing JSON, CSV, and HTML outputs suitable
    for GraphRAG ingestion.
    """
    setup_logging(verbose)
    
    console.print(f"[bold blue]Gazette Extractor[/bold blue]")
    console.print(f"Input PDF: {pdf}")
    console.print(f"Output directory: {out}")
    console.print(f"Batch size: {batch_pages} pages")
    console.print(f"Model: {model}")
    
    start_time = time.time()
    
    try:
        # Create output directory
        out.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            # Initialize Gemini client
            init_task = progress.add_task("Initializing Gemini client...", total=None)
            try:
                client = GeminiClient(api_key=api_key, model_name=model)
                progress.update(init_task, description="✓ Gemini client initialized")
            except Exception as e:
                progress.update(init_task, description=f"✗ Failed to initialize Gemini client: {e}")
                raise
            
            # Extract content
            extract_task = progress.add_task("Extracting content from PDF...", total=None)
            try:
                response = client.extract_pages(
                    pdf_path=pdf,
                    source_filename=pdf.name,
                    batch_size=batch_pages
                )
                progress.update(extract_task, description=f"✓ Extracted {len(response.pages)} pages")
            except Exception as e:
                progress.update(extract_task, description=f"✗ Extraction failed: {e}")
                raise
            
            # Post-process content
            process_task = progress.add_task("Post-processing content...", total=None)
            try:
                processor = PostProcessor()
                document = processor.process_response(response, out)
                progress.update(process_task, description="✓ Post-processing complete")
            except Exception as e:
                progress.update(process_task, description=f"✗ Post-processing failed: {e}")
                raise
            
            # Generate outputs
            output_task = progress.add_task("Generating output files...", total=None)
            try:
                _write_outputs(document, response, out)
                progress.update(output_task, description="✓ Output files generated")
            except Exception as e:
                progress.update(output_task, description=f"✗ Output generation failed: {e}")
                raise
        
        # Calculate processing time
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Generate run report
        run_report = _generate_run_report(document, response, processing_time)
        
        # Write run report
        report_path = out / "run_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(run_report.model_dump(), f, indent=2, ensure_ascii=False)
        
        # Show summary
        _show_summary(document, run_report, out)
        
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        if verbose:
            console.print_exception()
        raise click.ClickException(str(e))


def _write_outputs(document: Any, response: Any, out_dir: Path) -> None:
    """Write all output files.
    
    Args:
        document: Processed document
        response: Raw Gemini response
        out_dir: Output directory
    """
    # Write main gazette.json
    gazette_path = out_dir / "gazette.json"
    with open(gazette_path, 'w', encoding='utf-8') as f:
        json.dump(document.model_dump(), f, indent=2, ensure_ascii=False)
    
    # Write tables index
    if document.tables_index:
        tables_index_path = out_dir / "tables" / "tables.json"
        tables_index_path.parent.mkdir(exist_ok=True)
        with open(tables_index_path, 'w', encoding='utf-8') as f:
            json.dump([table.model_dump() for table in document.tables_index], f, indent=2)
    
    # Generate preview HTML
    preview_path = out_dir / "preview.html"
    generator = PreviewGenerator()
    generator.generate_preview(document, response.pages, preview_path)
    
    logger.info(f"Outputs written to {out_dir}")


def _generate_run_report(document: Any, response: Any, processing_time: float) -> RunReport:
    """Generate detailed run report.
    
    Args:
        document: Processed document
        response: Raw Gemini response
        processing_time: Total processing time in seconds
        
    Returns:
        Run report
    """
    # Count articles by language
    articles_by_language = {"rw": 0, "en": 0, "fr": 0}
    for article in document.articles:
        if article.rw:
            articles_by_language["rw"] += 1
        if article.en:
            articles_by_language["en"] += 1
        if article.fr:
            articles_by_language["fr"] += 1
    
    # Find table-heavy pages (heuristic: >3 table blocks)
    table_heavy_pages = []
    for page in response.pages:
        table_count = len([b for b in page.blocks if b.section == "table"])
        if table_count > 3:
            table_heavy_pages.append(page.page_index)
    
    # Collect processing errors
    processing_errors = []
    for page in response.pages:
        if hasattr(page, 'errors') and page.errors:
            processing_errors.extend(page.errors)
    
    return RunReport(
        total_pages=len(response.pages),
        articles_by_language=articles_by_language,
        tables_extracted=len(document.tables_index),
        page_anomalies=[],  # Could be enhanced with more detailed analysis
        table_heavy_pages=table_heavy_pages,
        processing_errors=processing_errors,
        extraction_time_seconds=processing_time,
        gemini_api_calls=1  # Could be tracked more precisely
    )


def _show_summary(document: Any, report: RunReport, out_dir: Path) -> None:
    """Show extraction summary.
    
    Args:
        document: Processed document
        report: Run report
        out_dir: Output directory
    """
    console.print("\n[bold green]✓ Extraction Complete![/bold green]")
    
    # Statistics table
    from rich.table import Table
    
    stats_table = Table(title="Extraction Statistics")
    stats_table.add_column("Metric", style="cyan")
    stats_table.add_column("Value", justify="right", style="green")
    
    stats_table.add_row("Pages processed", str(report.total_pages))
    stats_table.add_row("Articles found", str(len(document.articles)))
    stats_table.add_row("Tables extracted", str(report.tables_extracted))
    stats_table.add_row("Processing time", f"{report.extraction_time_seconds:.1f}s")
    
    console.print(stats_table)
    
    # Language breakdown
    if document.articles:
        lang_table = Table(title="Articles by Language")
        lang_table.add_column("Language", style="cyan")
        lang_table.add_column("Count", justify="right", style="green")
        
        lang_table.add_row("Kinyarwanda (rw)", str(report.articles_by_language["rw"]))
        lang_table.add_row("English (en)", str(report.articles_by_language["en"]))
        lang_table.add_row("French (fr)", str(report.articles_by_language["fr"]))
        
        console.print(lang_table)
    
    # Output files
    console.print(f"\n[bold]Output files:[/bold]")
    console.print(f"  📄 Main document: {out_dir / 'gazette.json'}")
    console.print(f"  🌐 Preview: {out_dir / 'preview.html'}")
    console.print(f"  📊 Run report: {out_dir / 'run_report.json'}")
    
    if document.tables_index:
        console.print(f"  📋 Tables: {out_dir / 'tables'}/ ({len(document.tables_index)} files)")
    
    # Warnings
    if report.processing_errors:
        console.print(f"\n[yellow]⚠️  {len(report.processing_errors)} processing errors detected[/yellow]")
    
    if document.run_stats.anomalies:
        console.print(f"[yellow]⚠️  {len(document.run_stats.anomalies)} anomalies detected[/yellow]")


if __name__ == "__main__":
    extract_gazette()
