"""
Gemini client with structured output support for Rwanda legal document extraction.
This replaces the JSON repair gauntlet with schema-constrained generation.
"""

import io
import json
import logging
import os
import time
from pathlib import Path
from typing import Any, Dict, List, Optional

import google.generativeai as genai
from google.generativeai import types
from PIL import Image
from pydantic import ValidationError

from .schema import GeminiResponse
from ..preprocessing.headers_footers import HeaderFooterStripper

logger = logging.getLogger(__name__)

# Load prompts and schema
PROMPTS_DIR = Path(__file__).parent.parent.parent / "prompts" / "gemini"
SCHEMAS_DIR = Path(__file__).parent.parent.parent / "schemas" / "rwanda_legal"

SYSTEM_PROMPT = (PROMPTS_DIR / "system.md").read_text()
USER_TEMPLATE = (PROMPTS_DIR / "user_batch_template.md").read_text()
RWANDA_LEGAL_SCHEMA = json.loads((SCHEMAS_DIR / "1.0.json").read_text())


class GeminiStructuredClient:
    """Gemini client with structured output support."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize the Gemini client.
        
        Args:
            api_key: Google API key. If None, will use GOOGLE_API_KEY env var.
        """
        self.api_key = api_key or os.getenv("GOOGLE_API_KEY")
        if not self.api_key:
            raise ValueError("GOOGLE_API_KEY environment variable is required")
        
        # Configure Gemini
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel(
            'gemini-1.5-flash',
            system_instruction=SYSTEM_PROMPT
        )

        # Initialize header/footer stripper
        self.header_footer_stripper = HeaderFooterStripper(
            top_ratio=0.15,      # Top 15% of page for gazette documents
            bottom_ratio=0.10,   # Bottom 10% of page
            min_share=0.40,      # Must appear on 40% of pages
            jaccard_threshold=0.6 # 60% similarity threshold
        )

        logger.info("Initialized Gemini structured client with header/footer stripping")
    
    def _render_user_prompt(
        self,
        pages_content: str,
        meta: Dict[str, Any],
        existing_json: Optional[Dict[str, Any]] = None
    ) -> str:
        """Render the user prompt template with provided data.
        
        Args:
            pages_content: Cleaned text content from PDF pages
            meta: Metadata dictionary with source, doc_type_hint, etc.
            existing_json: Optional existing document JSON to merge with
            
        Returns:
            Rendered prompt string
        """
        # Handle existing document block
        existing_block = ""
        if existing_json:
            existing_block = f"[EXISTING_DOC] (merge with this existing document)\n{json.dumps(existing_json, ensure_ascii=False, indent=2)}\n"
        
        # Render template
        prompt = USER_TEMPLATE.replace("{{SOURCE_URI_OR_NAME}}", str(meta.get("source", "")))
        prompt = prompt.replace("{{LANGUAGES_PRESENT}}", str(meta.get("languages_present", ["rw", "en", "fr"])))
        prompt = prompt.replace("{{HINT_DOC_TYPE_OR_EMPTY}}", str(meta.get("doc_type_hint", "")))
        prompt = prompt.replace("{{START_PAGE}}", str(meta.get("start_page", "")))
        prompt = prompt.replace("{{END_PAGE}}", str(meta.get("end_page", "")))
        prompt = prompt.replace("{{EXISTING_DOC_BLOCK}}", existing_block)
        prompt = prompt.replace("{{PAGES_CONTENT}}", pages_content)
        
        return prompt
    
    def _call_gemini_structured(
        self,
        content: Any,
        max_retries: int = 2
    ) -> Dict[str, Any]:
        """Call Gemini with structured output constraints.

        Args:
            content: The content to send (text + images)
            max_retries: Maximum number of retry attempts

        Returns:
            Parsed JSON response

        Raises:
            RuntimeError: If all attempts fail
        """
        for attempt in range(max_retries + 1):
            try:
                logger.info(f"Calling Gemini API with structured output (attempt {attempt + 1}/{max_retries + 1})")
                
                response = self.model.generate_content(
                    content,
                    generation_config=types.GenerationConfig(
                        temperature=0,
                        max_output_tokens=200_000,
                        response_mime_type="application/json",
                        response_schema=RWANDA_LEGAL_SCHEMA,
                    ),
                    safety_settings=[
                        {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                        {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                        {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                        {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"},
                    ]
                )
                
                # With structured output, response should be valid JSON
                if hasattr(response, 'text'):
                    response_text = response.text
                    logger.info(f"Received structured response ({len(response_text)} chars)")
                    
                    try:
                        # Parse the JSON response
                        data = json.loads(response_text)
                        logger.info("Successfully parsed structured JSON response")
                        logger.debug(f"Response data: {json.dumps(data, indent=2)[:500]}...")
                        return data
                    except json.JSONDecodeError as e:
                        logger.warning(f"JSON parsing failed despite structured output: {e}")
                        # Fallback to JSON repair if needed
                        return self._fallback_json_repair(response_text)
                else:
                    raise RuntimeError("No text response from Gemini")
                    
            except Exception as e:
                logger.error(f"Gemini API call failed (attempt {attempt + 1}): {e}")
                if attempt < max_retries:
                    wait_time = 2 ** attempt
                    logger.info(f"Retrying in {wait_time} seconds...")
                    time.sleep(wait_time)
                    continue
                else:
                    raise RuntimeError(f"Gemini API failed after {max_retries + 1} attempts: {e}")
    
    def _fallback_json_repair(self, response_text: str) -> Dict[str, Any]:
        """Fallback JSON repair for edge cases where structured output fails.
        
        Args:
            response_text: Raw response text
            
        Returns:
            Repaired JSON data
        """
        logger.warning("Using fallback JSON repair")
        
        try:
            from json_repair import repair_json
            repaired_text = repair_json(response_text)
            data = json.loads(repaired_text)
            logger.info("JSON repair successful")
            return data
        except Exception as e:
            logger.error(f"JSON repair failed: {e}")
            # Return minimal valid structure
            return {
                "schema_version": "rwanda-legal-1.0",
                "doc_type": "other",
                "sections": [{
                    "type": "other",
                    "language": "rw",
                    "text": "Failed to parse response",
                    "uncertain": True
                }]
            }

    def _convert_structured_to_legacy_format(
        self,
        structured_data: Dict[str, Any],
        source_filename: str
    ) -> Dict[str, Any]:
        """Convert structured output format to our existing legacy format.

        Args:
            structured_data: Data from structured output
            source_filename: Source filename for metadata

        Returns:
            Data in legacy format compatible with GeminiResponse
        """
        # Extract metadata
        metadata = structured_data.get("metadata", {})

        # Create document metadata
        doc = {
            "title": metadata.get("title"),
            "date_iso": metadata.get("date_iso"),
            "source_filename": source_filename
        }

        # Convert sections to blocks grouped by page
        sections = structured_data.get("sections", [])
        pages_dict = {}

        for section in sections:
            page_num = section.get("page", 0)
            if page_num not in pages_dict:
                pages_dict[page_num] = {
                    "page_index": page_num,
                    "blocks": [],
                    "stats": None,
                    "errors": []
                }

            # Convert section to block format
            section_type = section.get("type", "other")
            # Map new schema types to legacy types
            type_mapping = {
                "heading": "title",
                "signature": "other",
                "preamble": "other",
                "definitions": "other",
                "chapter": "title",
                "annex": "other"
            }
            legacy_type = type_mapping.get(section_type, section_type)

            block = {
                "section": legacy_type,
                "lang": section.get("language", "rw"),
                "article_no": section.get("article_no"),
                "article_title": section.get("title"),
                "text": section.get("text", ""),
                "table_html": None,  # TODO: Convert rows to HTML if present
                "uncertain": section.get("uncertain", False)
            }

            # Handle table data
            if section.get("rows"):
                # Convert rows to simple HTML table
                rows = section["rows"]
                html_rows = []
                for row in rows:
                    html_rows.append("<tr>" + "".join(f"<td>{cell}</td>" for cell in row) + "</tr>")
                block["table_html"] = f"<table>{''.join(html_rows)}</table>"

            pages_dict[page_num]["blocks"].append(block)

        # Convert to list format
        pages = [pages_dict[i] for i in sorted(pages_dict.keys())]

        return {
            "doc": doc,
            "pages": pages
        }

    def extract_pages(
        self,
        pdf_path: Optional[Path] = None,
        images: Optional[List[Image.Image]] = None,
        source_filename: str = "",
        doc_type_hint: str = "",
        existing_json: Optional[Dict[str, Any]] = None,
        max_retries: int = 2
    ) -> GeminiResponse:
        """Extract content from PDF pages using structured output.
        
        Args:
            pdf_path: Path to PDF file (will be converted to images)
            images: List of PIL Images (alternative to pdf_path)
            source_filename: Name of the source file for metadata
            doc_type_hint: Hint about document type (law, decree, etc.)
            existing_json: Existing document JSON to merge with
            max_retries: Maximum number of retry attempts
            
        Returns:
            GeminiResponse with extracted content
            
        Raises:
            ValueError: If neither pdf_path nor images provided
            RuntimeError: If extraction fails after retries
        """
        # Convert PDF to images if needed
        if pdf_path and not images:
            images = self._pdf_to_images(pdf_path)
        elif not images:
            raise ValueError("Either pdf_path or images must be provided")
        
        logger.info(f"Processing {len(images)} pages with structured output and header/footer stripping")

        # Apply header/footer stripping if we have a PDF path
        if pdf_path:
            try:
                cleaned_text_pages = self.header_footer_stripper.strip_headers_footers(pdf_path)
                logger.info("Applied header/footer stripping to reduce token count")
            except Exception as e:
                logger.warning(f"Header/footer stripping failed, using original text: {e}")
                cleaned_text_pages = self.header_footer_stripper._extract_all_text(pdf_path)
        else:
            # If no PDF path, we can't do text-based stripping, so we'll rely on images only
            cleaned_text_pages = [f"Page {i+1} content (image-based)" for i in range(len(images))]

        # Create pages content with cleaned text
        pages_content = ""
        for i, (image, cleaned_text) in enumerate(zip(images, cleaned_text_pages)):
            pages_content += f"\n=== PAGE {i + 1} ===\n"
            if cleaned_text.strip():
                pages_content += f"Text content:\n{cleaned_text}\n\n"
            pages_content += f"[Image data for page {i + 1} also provided]\n"
        
        # Prepare metadata
        meta = {
            "source": source_filename,
            "doc_type_hint": doc_type_hint,
            "start_page": 1,
            "end_page": len(images),
            "languages_present": ["rw", "en", "fr"]
        }
        
        # Render prompt
        prompt = self._render_user_prompt(pages_content, meta, existing_json)
        
        # Add images to the prompt
        content = [prompt] + images

        # Call Gemini with structured output
        try:
            data = self._call_gemini_structured(content, max_retries)

            # Convert structured output to our existing format
            converted_data = self._convert_structured_to_legacy_format(data, source_filename)

            # Convert to our Pydantic model for validation
            response = GeminiResponse.model_validate(converted_data)
            logger.info(f"Successfully extracted {len(response.pages)} pages")
            return response
            
        except ValidationError as e:
            logger.error(f"Response validation failed: {e}")
            # Create minimal valid response
            from .schema import DocumentMetadata, PageData
            return GeminiResponse(
                doc=DocumentMetadata(source_filename=source_filename),
                pages=[PageData(page_index=0, blocks=[], errors=[f"Validation failed: {str(e)}"])]
            )
    
    def _pdf_to_images(self, pdf_path: Path) -> List[Image.Image]:
        """Convert PDF to list of PIL Images.
        
        Args:
            pdf_path: Path to PDF file
            
        Returns:
            List of PIL Images
        """
        import fitz  # PyMuPDF
        
        doc = fitz.open(pdf_path)
        images = []
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            # Convert to image at 150 DPI for good quality
            pix = page.get_pixmap(matrix=fitz.Matrix(150/72, 150/72))
            img_data = pix.tobytes("png")
            image = Image.open(io.BytesIO(img_data))
            images.append(image)
        
        doc.close()
        logger.info(f"Converted PDF to {len(images)} images")
        return images
