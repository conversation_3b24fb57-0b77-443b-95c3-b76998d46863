"""Pydantic schemas for gazette extraction."""

from __future__ import annotations

from datetime import datetime
from enum import Enum
from typing import Any

from pydantic import BaseModel, Field


class SectionType(str, Enum):
    """Types of document sections."""
    
    TITLE = "title"
    ARTICLE = "article"
    TABLE = "table"
    FOOTER = "footer"
    OTHER = "other"


class Language(str, Enum):
    """Supported languages."""
    
    KINYARWANDA = "rw"
    ENGLISH = "en"
    FRENCH = "fr"


class PageBlock(BaseModel):
    """A block of content on a page."""
    
    section: SectionType
    lang: Language | None = None
    article_no: int | None = None
    article_title: str | None = None
    text: str | None = None
    table_html: str | None = None
    uncertain: bool = False


class PageStats(BaseModel):
    """Statistics for a page."""
    
    block_counts_by_section: dict[str, int] = Field(default_factory=dict)
    block_counts_by_lang: dict[str, int] = Field(default_factory=dict)
    article_numbers_found: list[int] = Field(default_factory=list)


class PageData(BaseModel):
    """Data extracted from a single page."""
    
    page_index: int
    blocks: list[PageBlock] = Field(default_factory=list)
    stats: PageStats | None = None
    errors: list[str] = Field(default_factory=list)


class DocumentMetadata(BaseModel):
    """Metadata about the document."""
    
    title: str | None = None
    date_iso: str | None = None
    source_filename: str


class GeminiResponse(BaseModel):
    """Response from Gemini API."""
    
    doc: DocumentMetadata | None = None
    pages: list[PageData] = Field(default_factory=list)


class LanguageText(BaseModel):
    """Text content in a specific language."""
    
    text: str
    pages: list[int] = Field(default_factory=list)


class ExtractedArticle(BaseModel):
    """An article with aligned text across languages."""
    
    article_no: int
    rw: LanguageText | None = None
    en: LanguageText | None = None
    fr: LanguageText | None = None
    pages: list[int] = Field(default_factory=list)


class TableInfo(BaseModel):
    """Information about an extracted table."""
    
    table_id: str
    pages: list[int]
    csv: str
    html: str


class RunStats(BaseModel):
    """Statistics from the extraction run."""
    
    pages: int
    articles_detected: int
    tables_detected: int = 0
    anomalies: list[str] = Field(default_factory=list)


class ExtractedDocument(BaseModel):
    """Final extracted document structure."""
    
    document: DocumentMetadata
    articles: list[ExtractedArticle] = Field(default_factory=list)
    tables_index: list[TableInfo] = Field(default_factory=list)
    run_stats: RunStats


class ArticleBlock(BaseModel):
    """Intermediate representation for article processing."""
    
    article_no: int
    lang: Language
    title: str | None = None
    text: str
    page_index: int


class TableBlock(BaseModel):
    """Intermediate representation for table processing."""
    
    table_html: str
    page_index: int
    table_id: str | None = None


class RunReport(BaseModel):
    """Detailed run report for debugging."""
    
    total_pages: int
    articles_by_language: dict[str, int] = Field(default_factory=dict)
    tables_extracted: int = 0
    page_anomalies: list[dict[str, Any]] = Field(default_factory=list)
    table_heavy_pages: list[int] = Field(default_factory=list)
    processing_errors: list[str] = Field(default_factory=list)
    extraction_time_seconds: float = 0.0
    gemini_api_calls: int = 0
    created_at: datetime = Field(default_factory=datetime.utcnow)
