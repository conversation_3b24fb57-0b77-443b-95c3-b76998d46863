"""Post-processing utilities for gazette extraction."""

from __future__ import annotations

import csv
import logging
import re
from collections import defaultdict
from io import String<PERSON>
from pathlib import Path
from typing import Any

from bs4 import BeautifulSoup
from rapidfuzz import fuzz

from .schema import (
    ArticleBlock,
    ExtractedArticle,
    ExtractedDocument,
    GeminiResponse,
    Language,
    LanguageText,
    PageBlock,
    RunStats,
    SectionType,
    TableBlock,
    TableInfo,
)

logger = logging.getLogger(__name__)


class PostProcessor:
    """Post-processes extracted content from Gemini."""
    
    def __init__(self, similarity_threshold: float = 0.92) -> None:
        """Initialize post-processor.
        
        Args:
            similarity_threshold: Threshold for header/footer detection
        """
        self.similarity_threshold = similarity_threshold
    
    def process_response(
        self,
        response: GeminiResponse,
        output_dir: Path
    ) -> ExtractedDocument:
        """Process Gemini response into final document structure.
        
        Args:
            response: Raw response from Gemini
            output_dir: Directory to save tables and other outputs
            
        Returns:
            Processed document with aligned articles and extracted tables
        """
        logger.info("Starting post-processing of Gemini response")
        
        # Create output directories
        tables_dir = output_dir / "tables"
        tables_dir.mkdir(parents=True, exist_ok=True)
        
        # Strip headers and footers
        cleaned_pages = self._strip_headers_footers(response.pages)
        
        # Extract articles and tables
        article_blocks = self._extract_article_blocks(cleaned_pages)
        table_blocks = self._extract_table_blocks(cleaned_pages)
        
        # Align articles across languages
        articles = self._align_articles(article_blocks)
        
        # Process tables
        tables_index = self._process_tables(table_blocks, tables_dir)
        
        # Generate statistics
        run_stats = self._generate_stats(cleaned_pages, articles, tables_index)
        
        # Create final document
        document = ExtractedDocument(
            document=response.doc or {"source_filename": "unknown"},
            articles=articles,
            tables_index=tables_index,
            run_stats=run_stats
        )
        
        logger.info(f"Post-processing complete: {len(articles)} articles, {len(tables_index)} tables")
        return document
    
    def _strip_headers_footers(self, pages: list[Any]) -> list[Any]:
        """Remove repeating headers and footers from pages.
        
        Args:
            pages: List of page data
            
        Returns:
            Pages with headers/footers removed
        """
        if len(pages) < 3:
            return pages  # Not enough pages to detect patterns
        
        # Collect first/last lines from each page
        first_lines = []
        last_lines = []
        
        for page in pages:
            page_lines = []
            for block in page.blocks:
                if block.section in [SectionType.TITLE, SectionType.ARTICLE, SectionType.OTHER] and block.text:
                    page_lines.extend(block.text.strip().split('\n'))
            
            if page_lines:
                first_lines.append(page_lines[0].strip())
                last_lines.append(page_lines[-1].strip())
        
        # Find common headers/footers
        common_headers = self._find_common_strings(first_lines)
        common_footers = self._find_common_strings(last_lines)
        
        logger.info(f"Found {len(common_headers)} common headers, {len(common_footers)} common footers")
        
        # Remove from pages
        cleaned_pages = []
        for page in pages:
            cleaned_blocks = []
            for block in page.blocks:
                if block.text and block.section in [SectionType.TITLE, SectionType.ARTICLE, SectionType.OTHER]:
                    cleaned_text = self._remove_common_strings(block.text, common_headers + common_footers)
                    if cleaned_text.strip():  # Only keep non-empty blocks
                        block.text = cleaned_text
                        cleaned_blocks.append(block)
                else:
                    cleaned_blocks.append(block)
            
            page.blocks = cleaned_blocks
            cleaned_pages.append(page)
        
        return cleaned_pages
    
    def _find_common_strings(self, strings: list[str]) -> list[str]:
        """Find strings that appear frequently across pages.
        
        Args:
            strings: List of strings to analyze
            
        Returns:
            List of common strings
        """
        if len(strings) < 3:
            return []
        
        common = []
        for i, s1 in enumerate(strings):
            if not s1.strip():
                continue
                
            similar_count = 1
            for j, s2 in enumerate(strings):
                if i != j and fuzz.ratio(s1, s2) >= self.similarity_threshold * 100:
                    similar_count += 1
            
            # If string appears in >50% of pages, consider it common
            if similar_count > len(strings) * 0.5:
                common.append(s1)
        
        return list(set(common))
    
    def _remove_common_strings(self, text: str, common_strings: list[str]) -> str:
        """Remove common strings from text.
        
        Args:
            text: Text to clean
            common_strings: Strings to remove
            
        Returns:
            Cleaned text
        """
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            is_common = False
            for common in common_strings:
                if fuzz.ratio(line, common) >= self.similarity_threshold * 100:
                    is_common = True
                    break
            
            if not is_common:
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def _extract_article_blocks(self, pages: list[Any]) -> list[ArticleBlock]:
        """Extract article blocks from pages.
        
        Args:
            pages: List of page data
            
        Returns:
            List of article blocks
        """
        article_blocks = []
        
        for page in pages:
            for block in page.blocks:
                if (block.section == SectionType.ARTICLE and 
                    block.article_no is not None and 
                    block.lang and 
                    block.text):
                    
                    article_blocks.append(ArticleBlock(
                        article_no=block.article_no,
                        lang=Language(block.lang),
                        title=block.article_title,
                        text=block.text,
                        page_index=page.page_index
                    ))
        
        logger.info(f"Extracted {len(article_blocks)} article blocks")
        return article_blocks
    
    def _extract_table_blocks(self, pages: list[Any]) -> list[TableBlock]:
        """Extract table blocks from pages.
        
        Args:
            pages: List of page data
            
        Returns:
            List of table blocks
        """
        table_blocks = []
        table_counter = 0
        
        for page in pages:
            for block in page.blocks:
                if block.section == SectionType.TABLE and block.table_html:
                    table_blocks.append(TableBlock(
                        table_html=block.table_html,
                        page_index=page.page_index,
                        table_id=f"table_{page.page_index:04d}_{table_counter:03d}"
                    ))
                    table_counter += 1
        
        logger.info(f"Extracted {len(table_blocks)} table blocks")
        return table_blocks

    def _align_articles(self, article_blocks: list[ArticleBlock]) -> list[ExtractedArticle]:
        """Align articles across languages.

        Args:
            article_blocks: List of article blocks

        Returns:
            List of aligned articles
        """
        # Group by article number
        articles_by_number = defaultdict(list)
        for block in article_blocks:
            articles_by_number[block.article_no].append(block)

        aligned_articles = []
        for article_no, blocks in articles_by_number.items():
            # Group by language
            by_lang = defaultdict(list)
            all_pages = set()

            for block in blocks:
                by_lang[block.lang].append(block)
                all_pages.add(block.page_index)

            # Combine text for each language
            lang_texts = {}
            for lang, lang_blocks in by_lang.items():
                combined_text = '\n\n'.join(block.text for block in lang_blocks)
                lang_pages = [block.page_index for block in lang_blocks]
                lang_texts[lang.value] = LanguageText(text=combined_text, pages=lang_pages)

            # Create aligned article
            article = ExtractedArticle(
                article_no=article_no,
                pages=sorted(all_pages)
            )

            # Set language texts
            if Language.KINYARWANDA.value in lang_texts:
                article.rw = lang_texts[Language.KINYARWANDA.value]
            if Language.ENGLISH.value in lang_texts:
                article.en = lang_texts[Language.ENGLISH.value]
            if Language.FRENCH.value in lang_texts:
                article.fr = lang_texts[Language.FRENCH.value]

            aligned_articles.append(article)

        # Sort by article number
        aligned_articles.sort(key=lambda x: x.article_no)
        logger.info(f"Aligned {len(aligned_articles)} articles")
        return aligned_articles

    def _process_tables(self, table_blocks: list[TableBlock], tables_dir: Path) -> list[TableInfo]:
        """Process table blocks into CSV files.

        Args:
            table_blocks: List of table blocks
            tables_dir: Directory to save table files

        Returns:
            List of table information
        """
        tables_index = []

        for block in table_blocks:
            try:
                # Parse HTML table
                soup = BeautifulSoup(block.table_html, 'html.parser')
                table = soup.find('table')

                if not table:
                    logger.warning(f"No table found in HTML for {block.table_id}")
                    continue

                # Convert to CSV
                csv_data = self._table_to_csv(table)

                # Save files
                csv_path = tables_dir / f"{block.table_id}.csv"
                html_path = tables_dir / f"{block.table_id}.html"

                with open(csv_path, 'w', newline='', encoding='utf-8') as f:
                    f.write(csv_data)

                with open(html_path, 'w', encoding='utf-8') as f:
                    f.write(block.table_html)

                # Add to index
                tables_index.append(TableInfo(
                    table_id=block.table_id,
                    pages=[block.page_index],
                    csv=str(csv_path.relative_to(tables_dir.parent)),
                    html=str(html_path.relative_to(tables_dir.parent))
                ))

                logger.debug(f"Processed table {block.table_id}")

            except Exception as e:
                logger.error(f"Failed to process table {block.table_id}: {e}")

        logger.info(f"Processed {len(tables_index)} tables")
        return tables_index

    def _table_to_csv(self, table: Any) -> str:
        """Convert HTML table to CSV string.

        Args:
            table: BeautifulSoup table element

        Returns:
            CSV string
        """
        output = StringIO()
        writer = csv.writer(output)

        # Process rows
        rows = table.find_all('tr')
        for row in rows:
            cells = row.find_all(['td', 'th'])
            row_data = []

            for cell in cells:
                # Get text content and clean it
                text = cell.get_text(strip=True)
                text = re.sub(r'\s+', ' ', text)  # Normalize whitespace
                row_data.append(text)

                # Handle colspan by adding empty cells
                colspan = int(cell.get('colspan', 1))
                for _ in range(colspan - 1):
                    row_data.append('')

            if row_data:  # Only add non-empty rows
                writer.writerow(row_data)

        return output.getvalue()

    def _generate_stats(
        self,
        pages: list[Any],
        articles: list[ExtractedArticle],
        tables: list[TableInfo]
    ) -> RunStats:
        """Generate run statistics.

        Args:
            pages: List of processed pages
            articles: List of aligned articles
            tables: List of table information

        Returns:
            Run statistics
        """
        anomalies = []

        # Check for pages with no content
        for page in pages:
            if not page.blocks:
                anomalies.append(f"Page {page.page_index}: No content blocks found")

        # Check for articles with missing languages
        for article in articles:
            missing_langs = []
            if not article.rw:
                missing_langs.append("rw")
            if not article.en:
                missing_langs.append("en")
            if not article.fr:
                missing_langs.append("fr")

            if missing_langs:
                anomalies.append(f"Article {article.article_no}: Missing languages: {', '.join(missing_langs)}")

        return RunStats(
            pages=len(pages),
            articles_detected=len(articles),
            tables_detected=len(tables),
            anomalies=anomalies
        )
