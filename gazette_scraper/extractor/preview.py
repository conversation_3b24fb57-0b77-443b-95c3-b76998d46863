"""HTML preview generator for extracted gazette content."""

from __future__ import annotations

import html
import logging
from pathlib import Path
from typing import Any

from .schema import ExtractedDocument, SectionType

logger = logging.getLogger(__name__)


class PreviewGenerator:
    """Generates HTML preview of extracted content."""
    
    def __init__(self) -> None:
        """Initialize preview generator."""
        pass
    
    def generate_preview(
        self,
        document: ExtractedDocument,
        raw_pages: list[Any],
        output_path: Path
    ) -> None:
        """Generate HTML preview file.
        
        Args:
            document: Extracted document data
            raw_pages: Raw page data from Gemini
            output_path: Path to save HTML file
        """
        logger.info(f"Generating preview HTML: {output_path}")
        
        html_content = self._build_html(document, raw_pages)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info("Preview HTML generated successfully")
    
    def _build_html(self, document: ExtractedDocument, raw_pages: list[Any]) -> str:
        """Build complete HTML content.
        
        Args:
            document: Extracted document data
            raw_pages: Raw page data from Gemini
            
        Returns:
            Complete HTML string
        """
        # Build page navigation
        page_nav = self._build_page_navigation(raw_pages)
        
        # Build page content
        page_content = self._build_page_content(raw_pages)
        
        # Build articles summary
        articles_summary = self._build_articles_summary(document.articles)
        
        # Build tables summary
        tables_summary = self._build_tables_summary(document.tables_index)
        
        # Build statistics
        stats_section = self._build_statistics(document.run_stats)
        
        return f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gazette Extraction Preview - {html.escape(document.document.source_filename)}</title>
    <style>
        {self._get_css()}
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Gazette Extraction Preview</h1>
            <p class="filename">{html.escape(document.document.source_filename)}</p>
            {f'<p class="doc-title">{html.escape(document.document.title)}</p>' if document.document.title else ''}
            {f'<p class="doc-date">Date: {document.document.date_iso}</p>' if document.document.date_iso else ''}
        </header>
        
        <div class="main-content">
            <aside class="sidebar">
                <div class="nav-section">
                    <h3>Pages ({len(raw_pages)})</h3>
                    {page_nav}
                </div>
                
                <div class="nav-section">
                    <h3>Articles ({len(document.articles)})</h3>
                    {articles_summary}
                </div>
                
                <div class="nav-section">
                    <h3>Tables ({len(document.tables_index)})</h3>
                    {tables_summary}
                </div>
                
                <div class="nav-section">
                    <h3>Statistics</h3>
                    {stats_section}
                </div>
            </aside>
            
            <main class="content">
                {page_content}
            </main>
        </div>
    </div>
    
    <script>
        {self._get_javascript()}
    </script>
</body>
</html>"""
    
    def _build_page_navigation(self, pages: list[Any]) -> str:
        """Build page navigation HTML.
        
        Args:
            pages: List of page data
            
        Returns:
            HTML string for page navigation
        """
        nav_items = []
        for page in pages:
            block_count = len(page.blocks)
            article_count = len([b for b in page.blocks if b.section == SectionType.ARTICLE])
            table_count = len([b for b in page.blocks if b.section == SectionType.TABLE])
            
            nav_items.append(f"""
                <div class="page-nav-item" onclick="scrollToPage({page.page_index})">
                    <span class="page-number">Page {page.page_index + 1}</span>
                    <span class="page-stats">{block_count} blocks, {article_count} articles, {table_count} tables</span>
                </div>
            """)
        
        return '<div class="page-nav">' + ''.join(nav_items) + '</div>'
    
    def _build_page_content(self, pages: list[Any]) -> str:
        """Build main page content HTML.
        
        Args:
            pages: List of page data
            
        Returns:
            HTML string for page content
        """
        content_sections = []
        
        for page in pages:
            blocks_html = []
            
            for block in page.blocks:
                block_html = self._format_block(block)
                blocks_html.append(block_html)
            
            page_html = f"""
                <div class="page-section" id="page-{page.page_index}">
                    <h2>Page {page.page_index + 1}</h2>
                    <div class="page-blocks">
                        {''.join(blocks_html)}
                    </div>
                    {self._format_page_errors(page.errors) if hasattr(page, 'errors') and page.errors else ''}
                </div>
            """
            content_sections.append(page_html)
        
        return ''.join(content_sections)
    
    def _format_block(self, block: Any) -> str:
        """Format a single block as HTML.
        
        Args:
            block: Block data
            
        Returns:
            HTML string for the block
        """
        section_class = f"block-{block.section.value}" if hasattr(block.section, 'value') else f"block-{block.section}"
        lang_class = f"lang-{block.lang}" if block.lang else ""
        uncertain_class = "uncertain" if getattr(block, 'uncertain', False) else ""
        
        classes = f"block {section_class} {lang_class} {uncertain_class}".strip()
        
        # Build block header
        header_parts = [block.section.value if hasattr(block.section, 'value') else str(block.section)]
        if block.lang:
            header_parts.append(f"({block.lang})")
        if getattr(block, 'article_no', None):
            header_parts.append(f"Article {block.article_no}")
        if getattr(block, 'uncertain', False):
            header_parts.append("⚠️ Uncertain")
        
        header = " ".join(header_parts)
        
        # Build block content
        content = ""
        if block.text:
            content = f'<div class="block-text">{html.escape(block.text)}</div>'
        elif getattr(block, 'table_html', None):
            content = f'<div class="block-table">{block.table_html}</div>'
        
        # Add article title if present
        title_html = ""
        if getattr(block, 'article_title', None):
            title_html = f'<div class="article-title">{html.escape(block.article_title)}</div>'
        
        return f"""
            <div class="{classes}">
                <div class="block-header">{header}</div>
                {title_html}
                {content}
            </div>
        """
    
    def _format_page_errors(self, errors: list[str]) -> str:
        """Format page errors as HTML.
        
        Args:
            errors: List of error messages
            
        Returns:
            HTML string for errors
        """
        if not errors:
            return ""
        
        error_items = [f"<li>{html.escape(error)}</li>" for error in errors]
        return f"""
            <div class="page-errors">
                <h4>⚠️ Page Errors</h4>
                <ul>{''.join(error_items)}</ul>
            </div>
        """

    def _build_articles_summary(self, articles: list[Any]) -> str:
        """Build articles summary HTML.

        Args:
            articles: List of extracted articles

        Returns:
            HTML string for articles summary
        """
        if not articles:
            return '<p class="empty">No articles found</p>'

        items = []
        for article in articles:
            langs = []
            if article.rw:
                langs.append("rw")
            if article.en:
                langs.append("en")
            if article.fr:
                langs.append("fr")

            lang_str = ", ".join(langs) if langs else "none"
            pages_str = ", ".join(map(str, [p + 1 for p in article.pages])) if article.pages else "none"

            items.append(f"""
                <div class="article-item">
                    <strong>Article {article.article_no}</strong><br>
                    <small>Languages: {lang_str}</small><br>
                    <small>Pages: {pages_str}</small>
                </div>
            """)

        return '<div class="articles-list">' + ''.join(items) + '</div>'

    def _build_tables_summary(self, tables: list[Any]) -> str:
        """Build tables summary HTML.

        Args:
            tables: List of table information

        Returns:
            HTML string for tables summary
        """
        if not tables:
            return '<p class="empty">No tables found</p>'

        items = []
        for table in tables:
            pages_str = ", ".join(map(str, [p + 1 for p in table.pages])) if table.pages else "none"

            items.append(f"""
                <div class="table-item">
                    <strong>{table.table_id}</strong><br>
                    <small>Pages: {pages_str}</small><br>
                    <small><a href="{table.csv}" target="_blank">CSV</a> | <a href="{table.html}" target="_blank">HTML</a></small>
                </div>
            """)

        return '<div class="tables-list">' + ''.join(items) + '</div>'

    def _build_statistics(self, stats: Any) -> str:
        """Build statistics HTML.

        Args:
            stats: Run statistics

        Returns:
            HTML string for statistics
        """
        anomalies_html = ""
        if stats.anomalies:
            anomaly_items = [f"<li>{html.escape(anomaly)}</li>" for anomaly in stats.anomalies]
            anomalies_html = f"""
                <div class="anomalies">
                    <h4>⚠️ Anomalies ({len(stats.anomalies)})</h4>
                    <ul>{''.join(anomaly_items)}</ul>
                </div>
            """

        return f"""
            <div class="stats">
                <div class="stat-item">
                    <span class="stat-label">Pages:</span>
                    <span class="stat-value">{stats.pages}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Articles:</span>
                    <span class="stat-value">{stats.articles_detected}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Tables:</span>
                    <span class="stat-value">{getattr(stats, 'tables_detected', 0)}</span>
                </div>
                {anomalies_html}
            </div>
        """

    def _get_css(self) -> str:
        """Get CSS styles for the preview.

        Returns:
            CSS string
        """
        return """
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                line-height: 1.6;
                color: #333;
                background-color: #f5f5f5;
            }

            .container {
                max-width: 1400px;
                margin: 0 auto;
                background: white;
                min-height: 100vh;
            }

            header {
                background: #2c3e50;
                color: white;
                padding: 1rem 2rem;
                border-bottom: 3px solid #3498db;
            }

            header h1 {
                margin-bottom: 0.5rem;
            }

            .filename {
                font-family: monospace;
                background: rgba(255,255,255,0.1);
                padding: 0.25rem 0.5rem;
                border-radius: 3px;
                display: inline-block;
            }

            .doc-title {
                font-weight: bold;
                margin-top: 0.5rem;
            }

            .doc-date {
                color: #bdc3c7;
                font-size: 0.9rem;
            }

            .main-content {
                display: flex;
                min-height: calc(100vh - 120px);
            }

            .sidebar {
                width: 300px;
                background: #ecf0f1;
                border-right: 1px solid #bdc3c7;
                padding: 1rem;
                overflow-y: auto;
                max-height: calc(100vh - 120px);
            }

            .nav-section {
                margin-bottom: 2rem;
            }

            .nav-section h3 {
                color: #2c3e50;
                margin-bottom: 0.5rem;
                padding-bottom: 0.25rem;
                border-bottom: 2px solid #3498db;
            }

            .page-nav-item {
                padding: 0.5rem;
                margin: 0.25rem 0;
                background: white;
                border-radius: 3px;
                cursor: pointer;
                transition: background-color 0.2s;
            }

            .page-nav-item:hover {
                background: #3498db;
                color: white;
            }

            .page-number {
                font-weight: bold;
                display: block;
            }

            .page-stats {
                font-size: 0.8rem;
                color: #7f8c8d;
            }

            .page-nav-item:hover .page-stats {
                color: #ecf0f1;
            }

            .content {
                flex: 1;
                padding: 2rem;
                overflow-y: auto;
                max-height: calc(100vh - 120px);
            }

            .page-section {
                margin-bottom: 3rem;
                padding-bottom: 2rem;
                border-bottom: 2px solid #ecf0f1;
            }

            .page-section h2 {
                color: #2c3e50;
                margin-bottom: 1rem;
                padding: 0.5rem 0;
                border-bottom: 1px solid #bdc3c7;
            }

            .block {
                margin: 1rem 0;
                border: 1px solid #ddd;
                border-radius: 5px;
                overflow: hidden;
            }

            .block-header {
                background: #34495e;
                color: white;
                padding: 0.5rem 1rem;
                font-weight: bold;
                font-size: 0.9rem;
            }

            .block-title .block-header {
                background: #8e44ad;
            }

            .block-article .block-header {
                background: #27ae60;
            }

            .block-table .block-header {
                background: #e67e22;
            }

            .block-footer .block-header {
                background: #95a5a6;
            }

            .uncertain .block-header {
                background: #e74c3c;
            }

            .article-title {
                background: #f8f9fa;
                padding: 0.5rem 1rem;
                font-weight: bold;
                border-bottom: 1px solid #dee2e6;
            }

            .block-text {
                padding: 1rem;
                white-space: pre-wrap;
                font-family: Georgia, serif;
                line-height: 1.8;
            }

            .block-table {
                padding: 1rem;
                overflow-x: auto;
            }

            .block-table table {
                width: 100%;
                border-collapse: collapse;
            }

            .block-table th,
            .block-table td {
                border: 1px solid #ddd;
                padding: 0.5rem;
                text-align: left;
            }

            .block-table th {
                background: #f8f9fa;
                font-weight: bold;
            }

            .page-errors {
                background: #fff5f5;
                border: 1px solid #fed7d7;
                border-radius: 5px;
                padding: 1rem;
                margin-top: 1rem;
            }

            .page-errors h4 {
                color: #c53030;
                margin-bottom: 0.5rem;
            }

            .page-errors ul {
                margin-left: 1rem;
            }

            .page-errors li {
                color: #742a2a;
                margin: 0.25rem 0;
            }

            .empty {
                color: #7f8c8d;
                font-style: italic;
            }

            .article-item,
            .table-item {
                background: white;
                padding: 0.75rem;
                margin: 0.5rem 0;
                border-radius: 3px;
                border-left: 3px solid #3498db;
            }

            .stat-item {
                display: flex;
                justify-content: space-between;
                padding: 0.25rem 0;
            }

            .stat-label {
                font-weight: bold;
            }

            .stat-value {
                color: #3498db;
                font-weight: bold;
            }

            .anomalies {
                margin-top: 1rem;
                padding: 0.75rem;
                background: #fff5f5;
                border-radius: 3px;
            }

            .anomalies h4 {
                color: #c53030;
                margin-bottom: 0.5rem;
            }

            .anomalies ul {
                margin-left: 1rem;
            }

            .anomalies li {
                color: #742a2a;
                font-size: 0.8rem;
                margin: 0.25rem 0;
            }
        """

    def _get_javascript(self) -> str:
        """Get JavaScript for the preview.

        Returns:
            JavaScript string
        """
        return """
            function scrollToPage(pageIndex) {
                const element = document.getElementById('page-' + pageIndex);
                if (element) {
                    element.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }

            // Highlight current page in navigation
            function updateActiveNavItem() {
                const pageElements = document.querySelectorAll('.page-section');
                const navItems = document.querySelectorAll('.page-nav-item');

                let activeIndex = 0;
                const scrollTop = document.querySelector('.content').scrollTop;

                for (let i = 0; i < pageElements.length; i++) {
                    if (pageElements[i].offsetTop <= scrollTop + 100) {
                        activeIndex = i;
                    }
                }

                navItems.forEach((item, index) => {
                    if (index === activeIndex) {
                        item.style.background = '#3498db';
                        item.style.color = 'white';
                    } else {
                        item.style.background = 'white';
                        item.style.color = '#333';
                    }
                });
            }

            // Add scroll listener
            document.querySelector('.content').addEventListener('scroll', updateActiveNavItem);

            // Initial highlight
            updateActiveNavItem();
        """
