"""HTTP client with rate limiting and proxy support."""

from __future__ import annotations

import logging
import random
import time
from typing import Any

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

logger = logging.getLogger(__name__)


class GazetteHTTPClient:
    """HTTP client with rate limiting, retries, and proxy support."""

    def __init__(
        self,
        rate_limit: float = 1.0,
        max_retries: int = 3,
        user_agent: str = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
        accept_language: str = "rw,en,fr",
        proxies: list[str] | None = None,
        timeout: int = 30,
        jitter_range: float = 0.5,
    ):
        self.rate_limit = rate_limit
        self.max_retries = max_retries
        self.user_agent = user_agent
        self.accept_language = accept_language
        self.proxies = proxies or []
        self.timeout = timeout
        self.jitter_range = jitter_range
        self.last_request_time = 0.0
        self._proxy_index = 0

        # Set up session with retries
        self.session = requests.Session()
        retry_strategy = Retry(
            total=max_retries,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            raise_on_status=False,  # Let us handle status codes
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # Default headers
        self.session.headers.update(
            {
                "User-Agent": self.user_agent,
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "Accept-Language": self.accept_language,
                "Accept-Encoding": "gzip, deflate",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
                "DNT": "1",
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
            }
        )

    def _rate_limit(self) -> None:
        """Enforce rate limiting with jitter."""
        if self.rate_limit > 0:
            elapsed = time.time() - self.last_request_time
            min_interval = 1.0 / self.rate_limit

            # Add jitter to avoid thundering herd
            jitter = (
                random.uniform(-self.jitter_range, self.jitter_range) * min_interval
            )
            adjusted_interval = max(0.1, min_interval + jitter)  # Minimum 0.1s

            if elapsed < adjusted_interval:
                sleep_time = adjusted_interval - elapsed
                time.sleep(sleep_time)
        self.last_request_time = time.time()

    def _get_proxy(self) -> dict[str, str] | None:
        """Get the next proxy from the list (round-robin)."""
        if not self.proxies:
            return None

        proxy_url = self.proxies[self._proxy_index]
        self._proxy_index = (self._proxy_index + 1) % len(self.proxies)
        return {"http": proxy_url, "https": proxy_url}

    def get(  # type: ignore[no-any-unimported,explicit-any]
        self,
        url: str,
        headers: dict[str, str] | None = None,
        referer: str | None = None,
        **kwargs: Any,
    ) -> requests.Response:
        """Make a GET request with rate limiting and retries."""
        self._rate_limit()

        request_headers = self.session.headers.copy()
        if headers:
            request_headers.update(headers)

        # Add referer if provided
        if referer:
            request_headers["Referer"] = referer

        proxy = self._get_proxy()

        logger.debug(f"GET {url}")

        try:
            response = self.session.get(
                url,
                headers=request_headers,
                proxies=proxy,
                timeout=self.timeout,
                **kwargs,
            )

            # Handle specific status codes
            if response.status_code in [403, 410]:
                logger.warning(f"HTTP {response.status_code} for {url}")
                # Don't raise for these - let caller handle
                return response
            elif response.status_code in [429, 503]:
                # Rate limited or service unavailable - exponential backoff
                backoff_time = min(60, 2 ** getattr(self, "_backoff_count", 0))
                logger.warning(
                    f"HTTP {response.status_code} for {url}, backing off {backoff_time}s"
                )
                time.sleep(backoff_time)
                self._backoff_count = getattr(self, "_backoff_count", 0) + 1
                response.raise_for_status()
            else:
                response.raise_for_status()

            # Reset backoff counter on success
            self._backoff_count = 0
            return response

        except requests.exceptions.RequestException as e:
            logger.warning(f"Request failed for {url}: {e}")
            raise

    def download_file(
        self, url: str, output_path: str, referer: str | None = None
    ) -> bool:
        """Download a file with streaming and progress tracking."""
        try:
            response = self.get(url, referer=referer, stream=True)

            # Handle 403/410 errors
            if response.status_code in [403, 410]:
                logger.error(f"HTTP {response.status_code} for {url}")
                return False

            with open(output_path, "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            logger.info(f"Downloaded: {output_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to download {url}: {e}")
            return False
