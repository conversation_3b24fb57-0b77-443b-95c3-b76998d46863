# Google Cloud Build Configuration for Rwanda Gazette Scraper
# Builds and deploys the scraper service to Cloud Run with security best practices

# Substitution variables for flexible deployment
substitutions:
  _SERVICE_NAME: rwanda-gazette-scraper
  _REGION: us-central1
  _PLATFORM: managed
  _MIN_INSTANCES: "0"
  _MAX_INSTANCES: "10"
  _CPU: "2"
  _MEMORY: "2Gi"
  _TIMEOUT: 3600s
  _CONCURRENCY: "1"


steps:
  # Step 1: Build the container image using multi-stage Docker build
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-image'
    args:
      - 'build'
      - '--tag'
      - 'us-central1-docker.pkg.dev/${PROJECT_ID}/caselaw-scraper/${_SERVICE_NAME}:${BUILD_ID}'
      - '--tag'
      - 'us-central1-docker.pkg.dev/${PROJECT_ID}/caselaw-scraper/${_SERVICE_NAME}:latest'
      - '--cache-from'
      - 'us-central1-docker.pkg.dev/${PROJECT_ID}/caselaw-scraper/${_SERVICE_NAME}:latest'
      - '--build-arg'
      - 'BUILDKIT_INLINE_CACHE=1'
      - '.'
    waitFor: ['-']

  # Step 2: Push the container image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-image'
    args:
      - 'push'
      - '--all-tags'
      - 'us-central1-docker.pkg.dev/${PROJECT_ID}/caselaw-scraper/${_SERVICE_NAME}'
    waitFor: ['build-image']

  # Step 3: Deploy to Cloud Run with comprehensive configuration
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-service'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'deploy'
      - '${_SERVICE_NAME}'
      - '--image'
      - 'us-central1-docker.pkg.dev/${PROJECT_ID}/caselaw-scraper/${_SERVICE_NAME}:${BUILD_ID}'
      - '--region'
      - '${_REGION}'
      - '--platform'
      - '${_PLATFORM}'
      - '--allow-unauthenticated'
      - '--service-account'
      - '${_SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com'
      - '--set-env-vars'
      - 'GOOGLE_CLOUD_PROJECT=${PROJECT_ID}'
      - '--set-env-vars'
      - 'GCS_BUCKET=rwandan_laws'
      - '--set-env-vars'
      - 'VERTEX_AI_LOCATION=${_REGION}'
      - '--set-secrets'
      - 'SUPABASE_SERVICE_ROLE_KEY=supabase-service-key:latest'
      - '--set-secrets'
      - 'GEMINI_API_KEY=gemini-api-key:latest'
      - '--cpu'
      - '${_CPU}'
      - '--memory'
      - '${_MEMORY}'
      - '--timeout'
      - '${_TIMEOUT}'
      - '--concurrency'
      - '${_CONCURRENCY}'
      - '--min-instances'
      - '${_MIN_INSTANCES}'
      - '--max-instances'
      - '${_MAX_INSTANCES}'
      - '--port'
      - '8080'
      - '--execution-environment'
      - 'gen2'
      - '--cpu-boost'
      - '--session-affinity'
    waitFor: ['push-image']

  # Step 4: Configure IAM policies for the service
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'set-iam-policy'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'services'
      - 'add-iam-policy-binding'
      - '${_SERVICE_NAME}'
      - '--region'
      - '${_REGION}'
      - '--member'
      - 'serviceAccount:${_SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com'
      - '--role'
      - 'roles/run.invoker'
    waitFor: ['deploy-service']

  # Step 5: Update traffic allocation to new revision
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'update-traffic'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'services'
      - 'update-traffic'
      - '${_SERVICE_NAME}'
      - '--region'
      - '${_REGION}'
      - '--to-latest'
    waitFor: ['set-iam-policy']

# Build timeout (max 24 hours, we use 20 minutes)
timeout: '1200s'

# Options for build configuration
options:
  # Use higher-performance machine type for faster builds
  machineType: 'E2_HIGHCPU_8'

  # Specify disk size for build (helpful for large dependencies)
  diskSizeGb: 100

  # Enable build logging for debugging
  logging: CLOUD_LOGGING_ONLY

# Define build artifacts and images
images:
  - 'us-central1-docker.pkg.dev/${PROJECT_ID}/caselaw-scraper/${_SERVICE_NAME}:${BUILD_ID}'
  - 'us-central1-docker.pkg.dev/${PROJECT_ID}/caselaw-scraper/${_SERVICE_NAME}:latest'

# Build tags for organization and filtering
tags:
  - 'rwanda-gazette-scraper'
  - 'cloud-run'
  - 'production'