#!/usr/bin/env python3
"""Test script for header/footer stripping functionality."""

import sys
import time
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from gazette_scraper.preprocessing.headers_footers import HeaderFooterStripper


def test_header_footer_stripping():
    """Test the header/footer stripping functionality."""
    print("🧪 Testing Header/Footer Stripping")
    print("=" * 50)
    
    # Find a test PDF
    data_dir = Path("data")
    pdf_files = list(data_dir.rglob("*.pdf"))
    
    if not pdf_files:
        print("❌ No PDF files found in data directory")
        return
    
    # Use the same PDF we tested with structured output
    test_pdf = None
    for pdf in pdf_files:
        if "2025-03-01_Igazeti ya Leta n° 13 Bis" in pdf.name:
            test_pdf = pdf
            break

    if not test_pdf:
        # Try any gazette file
        for pdf in pdf_files:
            if "Igazeti ya Leta" in pdf.name:
                test_pdf = pdf
                break

    if not test_pdf:
        test_pdf = pdf_files[0]
    
    print(f"📄 Testing with: {test_pdf.name}")
    print(f"📊 File size: {test_pdf.stat().st_size / 1024:.1f} KB")
    
    # Initialize the stripper with more aggressive settings for gazette documents
    stripper = HeaderFooterStripper(
        top_ratio=0.15,      # Top 15% of page (gazette headers can be larger)
        bottom_ratio=0.10,   # Bottom 10% of page
        min_share=0.40,      # Must appear on 40% of pages (more strict)
        jaccard_threshold=0.6 # 60% similarity threshold (more lenient)
    )
    
    print("\n🔍 Step 1: Detecting repeated headers/footers...")
    start_time = time.time()
    
    try:
        repeated_headers, repeated_footers = stripper.detect_repeated_banners(test_pdf)
        detection_time = time.time() - start_time
        
        print(f"✅ Detection completed in {detection_time:.2f}s")
        print(f"   • Found {len(repeated_headers)} repeated headers")
        print(f"   • Found {len(repeated_footers)} repeated footers")
        
        if repeated_headers:
            print("\n📋 Repeated Headers:")
            for i, header in enumerate(repeated_headers, 1):
                print(f"   {i}. \"{header[:80]}{'...' if len(header) > 80 else ''}\"")
        
        if repeated_footers:
            print("\n📋 Repeated Footers:")
            for i, footer in enumerate(repeated_footers, 1):
                print(f"   {i}. \"{footer[:80]}{'...' if len(footer) > 80 else ''}\"")
        
    except Exception as e:
        print(f"❌ Detection failed: {e}")
        return
    
    print("\n🧹 Step 2: Stripping headers/footers and extracting clean text...")
    start_time = time.time()
    
    try:
        # Extract original text for comparison
        original_pages = stripper._extract_all_text(test_pdf)
        
        # Extract cleaned text
        cleaned_pages = stripper.strip_headers_footers(test_pdf)
        
        stripping_time = time.time() - start_time
        
        print(f"✅ Stripping completed in {stripping_time:.2f}s")
        print(f"   • Processed {len(cleaned_pages)} pages")
        
        # Calculate statistics
        original_chars = sum(len(page) for page in original_pages)
        cleaned_chars = sum(len(page) for page in cleaned_pages)
        
        original_tokens = sum(len(page.split()) for page in original_pages)
        cleaned_tokens = sum(len(page.split()) for page in cleaned_pages)
        
        char_reduction = (original_chars - cleaned_chars) / original_chars * 100 if original_chars > 0 else 0
        token_reduction = (original_tokens - cleaned_tokens) / original_tokens * 100 if original_tokens > 0 else 0
        
        print(f"\n📊 Content Reduction Statistics:")
        print(f"   • Characters: {original_chars:,} → {cleaned_chars:,} ({char_reduction:.1f}% reduction)")
        print(f"   • Tokens: {original_tokens:,} → {cleaned_tokens:,} ({token_reduction:.1f}% reduction)")
        
        # Save results for inspection
        output_dir = Path("test_header_footer_results")
        output_dir.mkdir(exist_ok=True)
        
        # Save original text
        with open(output_dir / "original_text.txt", "w", encoding="utf-8") as f:
            for i, page in enumerate(original_pages):
                f.write(f"=== PAGE {i + 1} ===\n")
                f.write(page)
                f.write("\n\n")
        
        # Save cleaned text
        with open(output_dir / "cleaned_text.txt", "w", encoding="utf-8") as f:
            for i, page in enumerate(cleaned_pages):
                f.write(f"=== PAGE {i + 1} ===\n")
                f.write(page)
                f.write("\n\n")
        
        # Save detected banners
        with open(output_dir / "detected_banners.txt", "w", encoding="utf-8") as f:
            f.write("REPEATED HEADERS:\n")
            f.write("=" * 50 + "\n")
            for header in repeated_headers:
                f.write(f"- {header}\n")
            
            f.write("\nREPEATED FOOTERS:\n")
            f.write("=" * 50 + "\n")
            for footer in repeated_footers:
                f.write(f"- {footer}\n")
        
        print(f"\n📁 Results saved to: {output_dir}/")
        
    except Exception as e:
        print(f"❌ Stripping failed: {e}")
        return
    
    print("\n🎯 SUMMARY")
    print("=" * 50)
    
    if token_reduction > 5:
        print(f"✅ Header/Footer Stripping: EFFECTIVE")
        print(f"🚀 Token Reduction: {token_reduction:.1f}% (significant savings)")
        print(f"💰 Cost Impact: ~{token_reduction:.1f}% reduction in API costs")
        print(f"🎯 Quality Impact: Cleaner content for better article segmentation")
        print(f"🔧 Recommendation: DEPLOY header/footer stripping")
    elif token_reduction > 0:
        print(f"⚠️  Header/Footer Stripping: MINIMAL IMPACT")
        print(f"📉 Token Reduction: {token_reduction:.1f}% (small savings)")
        print(f"🔧 Recommendation: Consider adjusting thresholds or skip for this document type")
    else:
        print(f"❌ Header/Footer Stripping: NO IMPACT")
        print(f"📊 Token Reduction: {token_reduction:.1f}%")
        print(f"🔧 Recommendation: This document may not have repeated headers/footers")
    
    print(f"\n⏱️  Total Processing Time: {detection_time + stripping_time:.2f}s")
    
    # Test with different thresholds if minimal impact
    if 0 <= token_reduction <= 5:
        print(f"\n🔧 Testing with more aggressive settings...")
        
        aggressive_stripper = HeaderFooterStripper(
            top_ratio=0.15,      # Top 15% of page
            bottom_ratio=0.15,   # Bottom 15% of page
            min_share=0.20,      # Must appear on 20% of pages
            jaccard_threshold=0.6 # 60% similarity threshold
        )
        
        try:
            aggressive_cleaned = aggressive_stripper.strip_headers_footers(test_pdf)
            aggressive_tokens = sum(len(page.split()) for page in aggressive_cleaned)
            aggressive_reduction = (original_tokens - aggressive_tokens) / original_tokens * 100 if original_tokens > 0 else 0
            
            print(f"   • Aggressive settings: {aggressive_reduction:.1f}% token reduction")
            
            if aggressive_reduction > token_reduction + 2:
                print(f"   • 💡 Suggestion: Use more aggressive settings for this document type")
            
        except Exception as e:
            print(f"   • ❌ Aggressive test failed: {e}")


if __name__ == "__main__":
    test_header_footer_stripping()
