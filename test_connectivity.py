#!/usr/bin/env python3
"""Test connectivity to minijust.gov.rw and basic site structure."""

import requests
from bs4 import BeautifulSoup


def test_site_connectivity():
    """Test if we can reach the minijust site."""
    base_url = "https://minijust.gov.rw"

    try:
        print(f"Testing connectivity to {base_url}...")
        response = requests.get(base_url, timeout=10)
        response.raise_for_status()
        print(f"✓ Site is accessible (HTTP {response.status_code})")
        return True
    except Exception as e:
        print(f"✗ Cannot reach site: {e}")
        return False


def test_gazette_page():
    """Test if we can access the gazette section."""
    gazette_urls = [
        "https://minijust.gov.rw/index.php?id=35",  # Typical gazette page ID
        "https://minijust.gov.rw/fileadmin/user_upload/Official%20Gazette/",
        "https://minijust.gov.rw/documents/official-gazette/",
    ]

    for url in gazette_urls:
        try:
            print(f"Testing {url}...")
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"✓ Found gazette page at {url}")

                # Check for file list indicators
                BeautifulSoup(response.text, "html.parser")

                # Look for common gazette indicators
                indicators = [
                    "official gazette",
                    "tx_filelist",
                    "dumpFile",
                    "gazette",
                    ".pdf",
                ]

                content_lower = response.text.lower()
                found_indicators = [ind for ind in indicators if ind in content_lower]

                if found_indicators:
                    print(f"  Found indicators: {found_indicators}")
                else:
                    print("  No gazette indicators found")

                return url, response.text[:1000]  # Return first 1KB for inspection
            else:
                print(f"  HTTP {response.status_code}")

        except Exception as e:
            print(f"  Error: {e}")

    print("✗ No accessible gazette pages found")
    return None, None


def test_robots_txt():
    """Check robots.txt."""
    try:
        url = "https://minijust.gov.rw/robots.txt"
        print(f"Checking {url}...")
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            print("✓ robots.txt found:")
            print(response.text[:500])
        else:
            print(f"  HTTP {response.status_code}")
    except Exception as e:
        print(f"✗ Cannot access robots.txt: {e}")


if __name__ == "__main__":
    print("=== Rwanda Minijust Site Connectivity Test ===\n")

    if test_site_connectivity():
        print()
        test_robots_txt()
        print()
        gazette_url, sample_html = test_gazette_page()

        if sample_html:
            print("\n=== Sample HTML from gazette page ===")
            print(sample_html)

        print("\n=== Test completed ===")
    else:
        print("Cannot proceed - site is not accessible")
