# GraphRAG Integration Guide

This guide covers the complete GraphRAG integration for Rwanda Gazette analysis, including setup, usage, and advanced features.

## Overview

The GraphRAG integration adds advanced knowledge graph and vector search capabilities to the Rwanda Gazette scraper, enabling:

- **Knowledge Graph Construction**: Automatic entity and relationship extraction
- **Contextual Embeddings**: Voyage AI contextualized embeddings for superior search
- **Multi-modal Search**: Global, local, and DRIFT search modes
- **Cross-language Support**: Search across Kinyarwanda, English, and French
- **Quality Evaluation**: RAGAS-based evaluation metrics

## Architecture

```
Gazette JSON → BYOG Mapper → GraphRAG Index + MongoDB Atlas
                ↓
Query Service → Global/Local/DRIFT Search → Results
```

### Components

1. **BYOG Mapper**: Converts gazette JSON to GraphRAG BYOG format
2. **Voyage Embedder**: Generates contextual embeddings
3. **GraphRAG Indexer**: Builds communities and reports
4. **Query Service**: Unified interface for all search types
5. **Evaluation**: RAGAS-based quality assessment

## Installation

### Dependencies

```bash
# Install with all GraphRAG features
poetry install --extras "graphrag evaluation all"

# Or install specific extras
poetry install --extras "graphrag"  # Core GraphRAG
poetry install --extras "evaluation"  # RAGAS evaluation
```

### Environment Variables

```bash
# Required
export OPENAI_API_KEY="your-openai-key"
export VOYAGE_API_KEY="your-voyage-key"
export MONGODB_URI="mongodb+srv://user:<EMAIL>/"

# Optional
export GRAPHRAG_OUTPUT_DIR="./graphrag_output"
export MONGODB_DATABASE="rwanda_laws"
export MONGODB_COLLECTION="gazette_chunks"
```

## Quick Start

### 1. Automated Setup

```bash
# Run the complete setup script
./scripts/setup_graphrag.sh
```

### 2. Manual Setup

```bash
# Step 1: Build BYOG data
gazette-scraper graphrag build-byog \
    --input-dir ./data \
    --output-dir ./graphrag_data \
    --tenant-id rwanda_gov

# Step 2: Initialize GraphRAG project
gazette-scraper graphrag init-project \
    --project-name ailex_rwanda \
    --byog-dir ./graphrag_data \
    --enable-vector-store

# Step 3: Generate embeddings
gazette-scraper graphrag embed-chunks \
    --text-units-path ./graphrag_data/text_units.parquet \
    --model voyage-context-3 \
    --dimension 1024

# Step 4: Build GraphRAG index
gazette-scraper graphrag build-index \
    --project-root ./ailex_rwanda

# Step 5: Test queries
gazette-scraper graphrag query \
    --query "cooperative registration requirements" \
    --search-type local
```

## Search Types

### Global Search

Best for high-level questions and trend analysis.

```bash
gazette-scraper graphrag query \
    --search-type global \
    --query "What are the main legal trends in Rwanda for 2024?"
```

**Use cases:**
- Legal trend analysis
- High-level policy questions
- Comparative analysis across time periods

### Local Search

Best for specific facts and detailed information.

```bash
gazette-scraper graphrag query \
    --search-type local \
    --query "land registration process requirements" \
    --language en \
    --max-results 10
```

**Use cases:**
- Specific legal requirements
- Procedural questions
- Citation finding

### DRIFT Search

Best for comprehensive analysis combining overview and details.

```bash
gazette-scraper graphrag query \
    --search-type drift \
    --query "cooperative law changes and their implementation"
```

**Use cases:**
- Comprehensive legal analysis
- Research questions requiring both overview and details
- Complex multi-faceted queries

## API Usage

### Basic Query Service

```python
import asyncio
from gazette_scraper.graphrag import GraphRAGQueryService, VoyageEmbedder

async def search_example():
    # Initialize services
    embedder = VoyageEmbedder()
    query_service = GraphRAGQueryService(
        graphrag_project_root="./ailex_rwanda",
        voyage_embedder=embedder,
        tenant_id="rwanda_gov"
    )
    
    # Perform search
    result = await query_service.local_search(
        query="cooperative registration requirements",
        language="en",
        max_results=5
    )
    
    print("Response:", result["response"])
    print("Sources:", len(result["context"]))
    
    # Cleanup
    embedder.close()

# Run the example
asyncio.run(search_example())
```

### Advanced Filtering

```python
# Date-filtered search
result = await query_service.local_search(
    query="ministerial appointments",
    gazette_date_filter={"$gte": "2023-01-01"},
    language="en"
)

# Law type filtering
result = await query_service.local_search(
    query="land use regulations",
    law_type="land_law",
    language="fr"
)
```

### Building Custom Applications

```python
from gazette_scraper.graphrag import BYOGMapper, VoyageEmbedder

class LegalSearchApp:
    def __init__(self):
        self.embedder = VoyageEmbedder()
        self.query_service = GraphRAGQueryService(
            graphrag_project_root="./ailex_rwanda",
            voyage_embedder=self.embedder
        )
    
    async def search(self, query: str, search_type: str = "local"):
        if search_type == "global":
            return await self.query_service.global_search(query)
        elif search_type == "local":
            return await self.query_service.local_search(query)
        elif search_type == "drift":
            return await self.query_service.drift_search(query)
    
    def close(self):
        self.embedder.close()
```

## Data Processing

### BYOG Data Format

The BYOG mapper creates three parquet files:

1. **text_units.parquet**: Chunked text with metadata
2. **entities.parquet**: Extracted legal entities
3. **relationships.parquet**: Relationships between entities

### Entity Types

- `Act`: Legal acts or laws
- `Article`: Articles within documents
- `Institution`: Government institutions
- `Person`: Officials and appointees
- `Court`: Judicial courts
- `Topic`: Legal topics
- `Date`: Important dates
- `Location`: Geographic locations
- `Procedure`: Legal procedures
- `Penalty`: Legal penalties

### Relationship Types

- `repeals` (weight: 3.0): One law repeals another
- `amends` (weight: 2.0): One law amends another
- `same_as_translation` (weight: 2.0): Cross-language equivalents
- `cites` (weight: 1.0): Citation relationships
- `references` (weight: 1.0): General references

## Evaluation

### RAGAS Metrics

```python
from gazette_scraper.graphrag.evaluation import GraphRAGEvaluator

# Create evaluator
evaluator = GraphRAGEvaluator(query_service)

# Define test queries
test_queries = [
    {
        "question": "What are cooperative registration requirements?",
        "expected_answer": "Cooperatives must submit articles of association...",
        "language": "en"
    }
]

# Run evaluation
results = await evaluator.evaluate_queries(
    test_queries=test_queries,
    search_types=["local", "global"],
    output_path="./evaluation_results.json"
)
```

### Metrics Explained

- **Context Precision**: Relevance of retrieved contexts
- **Context Recall**: Coverage of relevant information
- **Faithfulness**: Accuracy of generated answers
- **Answer Relevancy**: Relevance of answers to questions

## MongoDB Atlas Setup

### Vector Search Index

Create a vector search index in MongoDB Atlas:

```json
{
  "fields": [
    {
      "type": "vector",
      "path": "vector",
      "numDimensions": 1024,
      "similarity": "dotProduct"
    }
  ]
}
```

### Collection Schema

```javascript
{
  "chunk_id": "uuid",
  "tenant_id": "rwanda_gov",
  "doc_id": "document_identifier",
  "text": "chunk_content",
  "vector": [0.1, 0.2, ...],  // 1024 dimensions
  "language": "en|fr|rw",
  "gazette_date": "2024-01-01",
  "law_type": "cooperative_law",
  "article_id": "article_1",
  "metadata": {...}
}
```

## Performance Optimization

### Chunking Strategy

- **Chunk size**: 800-1000 tokens for optimal context
- **Overlap**: 100-200 tokens for continuity
- **No overlap needed**: Voyage contextual embeddings handle boundaries

### Embedding Optimization

- **Batch size**: 50-100 documents per batch
- **Dimensions**: 1024 for best quality, 512 for speed
- **Model**: `voyage-context-3` for multilingual support

### Query Optimization

- **Local search**: Use specific filters (language, date, type)
- **Global search**: Adjust community level (1-3)
- **DRIFT search**: Balance global and local limits

## Troubleshooting

### Common Issues

1. **Missing embeddings**: Check Voyage API key and MongoDB connection
2. **Empty search results**: Verify GraphRAG index was built
3. **Slow queries**: Reduce search limits or add filters
4. **Memory issues**: Reduce batch sizes or chunk dimensions

### Debug Commands

```bash
# Check BYOG data
python -c "import pandas as pd; print(pd.read_parquet('./graphrag_data/text_units.parquet').info())"

# Verify MongoDB connection
python -c "from gazette_scraper.graphrag import VoyageEmbedder; e = VoyageEmbedder(); print('Connected')"

# Test GraphRAG config
python -c "from gazette_scraper.graphrag.config import GraphRAGConfig; c = GraphRAGConfig('./ailex_rwanda'); print(c.get_config_dict())"
```

## Best Practices

1. **Data Quality**: Ensure clean gazette JSON before BYOG processing
2. **Incremental Updates**: Process new gazettes incrementally
3. **Multi-tenancy**: Use tenant_id for isolation
4. **Monitoring**: Track search quality with RAGAS evaluation
5. **Caching**: Cache frequent queries for better performance

## Integration Examples

See `examples/graphrag_api_example.py` for complete integration examples and `scripts/setup_graphrag.sh` for automated setup.
