# Rwanda Case-Law Scraper - Hybrid Deployment Dockerfile
# Optimized for Cloud Run with Supabase + GCS

FROM python:3.12-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies for Playwright
RUN apt-get update && apt-get install -y \
    # Basic utilities
    curl \
    wget \
    ca-certificates \
    # Build tools for Python packages with C extensions
    build-essential \
    g++ \
    gcc \
    # Playwright browser dependencies
    libnss3 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libgtk-3-0 \
    libgbm1 \
    libasound2 \
    libxrandr2 \
    libxss1 \
    libxtst6 \
    libxcomposite1 \
    libxdamage1 \
    libxi6 \
    libxfixes3 \
    libxcursor1 \
    # Fonts for better PDF rendering
    fonts-liberation \
    fonts-noto-color-emoji \
    fonts-noto-cjk \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for security
RUN groupadd --gid 1000 scraper \
    && useradd --uid 1000 --gid scraper --shell /bin/bash --create-home scraper

# Set working directory
WORKDIR /app

# Copy dependency files first for better caching
COPY pyproject.toml poetry.lock ./

# Install Poetry and configure it
RUN pip install poetry==1.8.0 \
    && poetry config virtualenvs.create false

# Install dependencies only (without the package itself)
RUN poetry install --only main --no-root --no-interaction --no-ansi

# Install Playwright system dependencies (must be done as root)
RUN python -m playwright install-deps chromium

# Create necessary directories
RUN mkdir -p /app/caselaw_data /app/screenshots /app/logs \
    && chown -R scraper:scraper /app/caselaw_data /app/screenshots /app/logs

# Copy application code
COPY --chown=scraper:scraper . .

# Install the application package in editable mode (as root to avoid permission issues)
USER root
RUN pip install -e .
USER scraper

# Copy hybrid configuration
COPY caselaw_hybrid_config.toml /app/caselaw_config.toml

# Switch to non-root user
USER scraper

# Install Playwright browsers as the scraper user (so they're accessible at runtime)
RUN python -m playwright install chromium

# Expose port for Cloud Run
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Set default environment variables
ENV PORT=8080 \
    CASELAW_CONFIG_PATH=/app/caselaw_config.toml \
    CASELAW_HEADLESS=true \
    CASELAW_DEBUG_MODE=false \
    GOOGLE_CLOUD_PROJECT=rwandan-law-bot-440710 \
    CASE_LAWS_GCS_BUCKET=rwandan-caselaws

# Labels for container registry
LABEL org.opencontainers.image.title="Rwanda Case-Law Scraper"
LABEL org.opencontainers.image.description="Hybrid deployment tool for scraping Rwandan case-law documents with Supabase + GCS"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.vendor="Internal Use Only"

# Default command - can be overridden
CMD ["python", "-m", "gazette_scraper.caselaw.cli", "serve", "--host", "0.0.0.0", "--port", "8080"]