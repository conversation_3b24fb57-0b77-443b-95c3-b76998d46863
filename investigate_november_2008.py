#!/usr/bin/env python3
"""
Investigation script for November 2008 Supreme Court documents.
This script will manually navigate through the website structure to understand
what documents and days are available.
"""

import asyncio
import logging
from gazette_scraper.config import load_caselaw_config
from gazette_scraper.caselaw.navigator import CaseLawNavigator
from gazette_scraper.caselaw.models import CourtNode, NodeType

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def investigate_november_2008():
    """Investigate the structure of Supreme Court documents in November 2008."""
    
    # Load configuration
    config = load_caselaw_config()
    config.dry_run = True  # Don't download anything
    
    print("🔍 Investigating Supreme Court/2008/November structure...")
    
    async with CaseLawNavigator(config) as navigator:
        # Discover the full tree structure
        print("\n📊 Discovering complete court tree structure...")

        # Discover the tree structure
        discovered_nodes = await navigator.discover_court_tree()
        
        print(f"\n📋 Total nodes discovered: {len(discovered_nodes)}")
        
        # Filter for 2008 nodes
        nodes_2008 = [node for node in discovered_nodes if "2008" in node.full_path]
        print(f"📅 Nodes containing '2008': {len(nodes_2008)}")
        
        # Filter for November 2008 nodes
        november_2008_nodes = [node for node in nodes_2008 if "November" in node.full_path]
        print(f"🍂 November 2008 nodes: {len(november_2008_nodes)}")
        
        # Print all November 2008 nodes
        for node in november_2008_nodes:
            print(f"   📄 {node.full_path} (Type: {node.node_type}, Count: {node.document_count})")
        
        # If we found November 2008 nodes, try to extract cases from day nodes
        day_nodes = [node for node in november_2008_nodes if node.node_type == NodeType.DAY]
        print(f"\n📆 Day nodes in November 2008: {len(day_nodes)}")
        
        for day_node in day_nodes:
            print(f"\n🔍 Investigating {day_node.full_path}...")
            try:
                # Use the high-level method to discover cases for the day
                cases = await navigator.discover_cases_for_day(day_node)
                print(f"   📋 Cases found: {len(cases)}")
                for i, case in enumerate(cases[:3]):  # Show first 3 cases
                    print(f"   📄 {i+1}. {case.title}")
                if len(cases) > 3:
                    print(f"   ... and {len(cases) - 3} more cases")
            except Exception as e:
                print(f"   ❌ Error extracting cases: {e}")

if __name__ == "__main__":
    asyncio.run(investigate_november_2008())
