#!/usr/bin/env python3
"""
Test script for Case Discovery phase of the case-law scraper.

This script tests the ability to discover individual case documents
from day nodes in the Amategeko website hierarchy.
"""

import asyncio
import logging
from pathlib import Path

from gazette_scraper.caselaw.pipeline import CaseLawPipeline
from gazette_scraper.caselaw.models import CourtN<PERSON>, CaseLawConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_case_discovery():
    """Test the case discovery functionality."""
    logger.info("Starting Case Discovery Test")
    
    # Initialize configuration
    config = CaseLawConfig(
        output_dir=Path("test_case_discovery_output"),
        headless=False,  # Use non-headless for debugging
        debug_mode=True,
        screenshot_on_error=True,
        max_concurrent_downloads=2,
        browser_timeout=30000,
        max_retry_attempts=3
    )

    # Initialize pipeline with configuration
    pipeline = CaseLawPipeline(config)
    
    try:
        await pipeline.start()
        
        # First, discover the complete court tree to get all nodes
        logger.info("Step 1: Discovering complete court tree...")
        all_nodes = await pipeline.navigator.discover_court_tree()
        logger.info(f"Found {len(all_nodes)} total nodes in court tree")

        if not all_nodes:
            logger.error("No nodes found - cannot proceed with test")
            return

        # Analyze the tree structure
        courts = [node for node in all_nodes if node.level == 0]
        years = [node for node in all_nodes if node.level == 1]
        months = [node for node in all_nodes if node.level == 2]
        days = [node for node in all_nodes if node.level == 3]

        logger.info(f"Tree structure:")
        logger.info(f"  - Courts: {len(courts)}")
        logger.info(f"  - Years: {len(years)}")
        logger.info(f"  - Months: {len(months)}")
        logger.info(f"  - Days: {len(days)}")

        # Find a day node with documents for testing
        test_day = None
        for day in days:
            if day.expected_documents > 0:
                test_day = day
                break

        if not test_day:
            logger.error("No day nodes with expected documents found - cannot proceed with test")
            return

        logger.info(f"Selected test day: {test_day.court_name} ({test_day.expected_documents} expected docs)")
        logger.info(f"Full path: {test_day.full_path}")
        
        # NOW TEST THE CASE DISCOVERY
        logger.info("=" * 60)
        logger.info("Step 5: TESTING CASE DISCOVERY")
        logger.info("=" * 60)
        
        cases = await pipeline.navigator.discover_cases_for_day(test_day)
        
        logger.info(f"Case Discovery Results:")
        logger.info(f"  Expected documents: {test_day.expected_documents}")
        logger.info(f"  Discovered cases: {len(cases)}")

        if cases:
            logger.info("  Sample cases discovered:")
            for i, case in enumerate(cases[:3]):  # Show first 3 cases
                logger.info(f"    {i+1}. {case.case_title}")
                logger.info(f"       URL: {case.download_url}")
                logger.info(f"       Path: {case.navigation_path}")
                logger.info(f"       Filename: {case.filename}")
        else:
            logger.warning("  No cases discovered!")

        # Validate the results
        if len(cases) == test_day.expected_documents:
            logger.info("✅ SUCCESS: Case count matches expected document count")
        elif len(cases) > 0:
            logger.warning(f"⚠️  PARTIAL: Found {len(cases)} cases but expected {test_day.expected_documents}")
        else:
            logger.error("❌ FAILURE: No cases discovered")
        
        # Test state management
        logger.info("=" * 60)
        logger.info("Step 6: TESTING STATE MANAGEMENT")
        logger.info("=" * 60)
        
        if cases:
            # Test saving case discovery to state
            for case in cases[:2]:  # Test with first 2 cases
                pipeline.state.save_case_file(case)
                is_downloaded = pipeline.state.is_case_already_downloaded(case)
                logger.info(f"Case '{case.case_title}' - Already downloaded: {is_downloaded}")
        
        logger.info("Case Discovery Test Complete!")
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await pipeline.close()


if __name__ == "__main__":
    asyncio.run(test_case_discovery())
