#!/usr/bin/env python3
"""
Test script to examine actual case page structure and test PDF extraction.
"""

import asyncio
import logging
from pathlib import Path
from gazette_scraper.caselaw.navigator import CaseLawNavigator
from gazette_scraper.caselaw.models import CaseLawConfig, CaseLawFile

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_pdf_extraction():
    """Test PDF extraction on a real case page."""
    
    # Create a basic config
    config = CaseLawConfig(
        base_url="https://amategeko.gov.rw",
        caselaw_path="/laws/judgement/2",
        navigation_delay=1.0,
        max_retries=3
    )
    
    # Create navigator
    navigator = CaseLawNavigator(config)
    
    try:
        # Initialize browser
        await navigator.start()
        
        # Create a test case file from our previous test results
        test_case = CaseLawFile(
            title="RWANDA REVENUE AUTHORITY (RRA) v SOCIÉTÉ RWANDAISE DE DISTRIBUTION ET SERVICES LTD (SRDS LTD)2025-02-12",
            case_title="RWANDA REVENUE AUTHORITY (RRA) v SOCIÉTÉ RWANDAISE DE DISTRIBUTION ET SERVICES LTD (SRDS LTD)2025-02-12",
            case_page_url="https://amategeko.gov.rw/view/doc/8893/9229",
            download_url="https://amategeko.gov.rw/view/doc/8893/9229",  # Placeholder
            listing_url="https://amategeko.gov.rw/laws/judgement/2",
            navigation_path="Supreme Court (911)/2025/February/Day_12",
            court_name="Supreme Court",
            year=2025,
            month=2,
            day=12,
            filename="test_case.pdf"
        )
        
        logger.info(f"Testing PDF extraction for: {test_case.case_title}")
        logger.info(f"Case page URL: {test_case.case_page_url}")
        
        # Test 1: Navigator's basic PDF extraction
        logger.info("\n=== Testing Navigator PDF Extraction ===")
        navigator_result = await navigator.extract_pdf_download_url(test_case)
        if navigator_result:
            logger.info(f"✅ Navigator found PDF URL: {navigator_result}")
        else:
            logger.warning("❌ Navigator failed to find PDF URL")

        # Test 2: Manual page inspection
        logger.info("\n=== Manual Page Inspection ===")
        context = navigator.contexts[0]
        page = await context.new_page()
        try:
            await page.goto(str(test_case.case_page_url), wait_until="networkidle")
            await asyncio.sleep(2)
            
            # Take a screenshot
            screenshot_path = f"screenshots/case_page_{test_case.case_title[:50].replace(' ', '_')}.png"
            await page.screenshot(path=screenshot_path)
            logger.info(f"Screenshot saved: {screenshot_path}")
            
            # Get page title and URL
            title = await page.title()
            url = page.url
            logger.info(f"Page title: {title}")
            logger.info(f"Current URL: {url}")
            
            # Look for all links on the page
            all_links = await page.query_selector_all("a")
            logger.info(f"Total links found: {len(all_links)}")
            
            pdf_related_links = []
            for i, link in enumerate(all_links[:20]):  # Check first 20 links
                try:
                    href = await link.get_attribute("href")
                    text = await link.text_content()
                    title_attr = await link.get_attribute("title")
                    onclick = await link.get_attribute("onclick")
                    
                    if href and (".pdf" in href.lower() or "download" in href.lower()):
                        pdf_related_links.append(f"PDF Link {len(pdf_related_links)+1}: href='{href}' text='{text}' title='{title_attr}'")
                    elif text and ("download" in text.lower() or "pdf" in text.lower()):
                        pdf_related_links.append(f"PDF Text {len(pdf_related_links)+1}: href='{href}' text='{text}' title='{title_attr}'")
                    elif onclick and ("download" in onclick.lower() or "pdf" in onclick.lower()):
                        pdf_related_links.append(f"PDF JS {len(pdf_related_links)+1}: href='{href}' onclick='{onclick}' text='{text}'")
                        
                except Exception as e:
                    continue
            
            if pdf_related_links:
                logger.info("Found PDF-related elements:")
                for link in pdf_related_links:
                    logger.info(f"  - {link}")
            else:
                logger.warning("No obvious PDF-related links found")
            
            # Look for buttons
            all_buttons = await page.query_selector_all("button")
            logger.info(f"Total buttons found: {len(all_buttons)}")
            
            for i, button in enumerate(all_buttons[:10]):  # Check first 10 buttons
                try:
                    text = await button.text_content()
                    onclick = await button.get_attribute("onclick")
                    class_attr = await button.get_attribute("class")
                    
                    if text and ("download" in text.lower() or "pdf" in text.lower()):
                        logger.info(f"  Button {i+1}: text='{text}' onclick='{onclick}' class='{class_attr}'")
                    elif onclick and ("download" in onclick.lower() or "pdf" in onclick.lower()):
                        logger.info(f"  Button {i+1}: text='{text}' onclick='{onclick}' class='{class_attr}'")
                        
                except Exception as e:
                    continue
            
            # Look for iframes and embeds
            iframes = await page.query_selector_all("iframe")
            embeds = await page.query_selector_all("embed")
            objects = await page.query_selector_all("object")
            
            logger.info(f"Found {len(iframes)} iframes, {len(embeds)} embeds, {len(objects)} objects")
            
            for iframe in iframes:
                try:
                    src = await iframe.get_attribute("src")
                    if src:
                        logger.info(f"  Iframe src: {src}")
                except Exception:
                    continue
                    
            for embed in embeds:
                try:
                    src = await embed.get_attribute("src")
                    if src:
                        logger.info(f"  Embed src: {src}")
                except Exception:
                    continue
                    
            for obj in objects:
                try:
                    data = await obj.get_attribute("data")
                    if data:
                        logger.info(f"  Object data: {data}")
                except Exception:
                    continue
            
        finally:
            await page.close()
            
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Clean up
        await navigator.close()

if __name__ == "__main__":
    asyncio.run(test_pdf_extraction())
