# Rwanda Case-Law Scraper - Hybrid Deployment

## 🎯 Architecture Overview

**Hybrid Approach**: Supabase for metadata + Google Cloud Storage for PDFs

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Cloud Run     │    │    Supabase      │    │  Google Cloud   │
│                 │    │                  │    │    Storage      │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │ Case-Law    │ │───▶│ │   Metadata   │ │    │ │    PDFs     │ │
│ │ Scraper     │ │    │ │   Database   │ │    │ │  Documents  │ │
│ │ Service     │ │───▶│ │   Progress   │ │    │ │   Storage   │ │
│ └─────────────┘ │    │ │   Tracking   │ │    │ └─────────────┘ │
└─────────────────┘    │ └──────────────┘ │    └─────────────────┘
                       └──────────────────┘
```

## 🚀 One-Command Deployment

```bash
# Simple deployment command
./deploy-hybrid.sh
```

That's it! The script will:
- ✅ Set up Google Cloud infrastructure
- ✅ Create GCS bucket with lifecycle policies
- ✅ Build and deploy to Cloud Run
- ✅ Configure service accounts and permissions
- ✅ Set up monitoring and logging

## 📋 Prerequisites

1. **Google Cloud Account** with billing enabled
2. **gcloud CLI** installed and authenticated
3. **Docker** installed locally
4. **Supabase project** already configured (from your .env)

## 🔧 Configuration

Your `.env` file is already configured for hybrid deployment:

```env
# Supabase (for metadata)
SUPABASE_URL=https://esjiwdjofswwwmghrlaa.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-key

# Google Cloud Storage (for PDFs)
CASE_LAWS_GCS_BUCKET=rwandan-caselaws
GOOGLE_PROJECT_ID=rwandan-law-bot-440710
```

## 💰 Cost Estimate

| Component | Monthly Cost (USD) |
|-----------|-------------------|
| Cloud Run | $2-5 (scale-to-zero) |
| GCS Storage | $1-3 (50GB PDFs) |
| Supabase | $0 (free tier) |
| **Total** | **$3-8/month** |

## 🎮 Usage Examples

### 1. Deploy the Service
```bash
./deploy-hybrid.sh
```

### 2. Test the Deployment
```bash
# Get service URL
SERVICE_URL=$(gcloud run services describe rwanda-caselaw-scraper --region=us-central1 --format="value(status.url)")

# Health check
curl $SERVICE_URL/health

# Service info
curl $SERVICE_URL/
```

### 3. Trigger Scraping Jobs

#### Via HTTP API:
```bash
# Scrape all courts
curl -X POST $SERVICE_URL/scrape

# Scrape specific court
curl -X POST "$SERVICE_URL/scrape?court=Supreme%20Court&year=2025&month=2"
```

#### Via CLI (local):
```bash
# Install dependencies
poetry install

# Scrape specific target
poetry run python -m gazette_scraper.caselaw.cli scrape --court "Supreme Court" --year 2025 --month 2

# Test configuration
poetry run python -m gazette_scraper.caselaw.cli test
```

## 🔄 Automated Scheduling

Set up Cloud Scheduler for regular scraping:

```bash
# Create daily scraping job
gcloud scheduler jobs create http daily-caselaw-scrape \
    --schedule="0 2 * * *" \
    --uri="$SERVICE_URL/scrape" \
    --http-method=POST \
    --time-zone="Africa/Kigali"
```

## 📊 Monitoring

### Cloud Run Metrics
- Request count and latency
- Memory and CPU usage
- Error rates

### GCS Metrics
- Storage usage and costs
- Download/upload operations

### Supabase Metrics
- Database connections
- Query performance
- Storage usage

## 🔧 Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   gcloud auth login
   gcloud config set project rwandan-law-bot-440710
   ```

2. **Docker Build Fails**
   ```bash
   # Clean Docker cache
   docker system prune -a
   ```

3. **Service Won't Start**
   ```bash
   # Check logs
   gcloud logs read --service=rwanda-caselaw-scraper --limit=50
   ```

### Debug Mode
```bash
# Enable debug logging
gcloud run services update rwanda-caselaw-scraper \
    --set-env-vars "CASELAW_DEBUG_MODE=true" \
    --region=us-central1
```

## 🔒 Security Features

- ✅ Non-root container user
- ✅ Service account with minimal permissions
- ✅ Secret Manager for sensitive data
- ✅ VPC connector for network isolation
- ✅ HTTPS-only endpoints

## 📈 Scaling

The service automatically scales:
- **Scale to zero**: No cost when idle
- **Auto-scaling**: Up to 3 instances under load
- **Resource limits**: 4GB RAM, 2 vCPUs per instance

## 🎯 Next Steps

1. **Deploy**: Run `./deploy-hybrid.sh`
2. **Test**: Verify with health checks
3. **Schedule**: Set up automated scraping
4. **Monitor**: Check Cloud Console dashboards
5. **Optimize**: Adjust resources based on usage

## 📞 Support

For issues or questions:
1. Check Cloud Run logs
2. Verify Supabase connection
3. Test GCS permissions
4. Review configuration files
