# Make Target: `historical-pull`

## Overview

The `make historical-pull` target runs a complete historical scraping of Rwanda Official Gazettes from 2004 to present with automatic Google Cloud Storage integration.

## Usage

```bash
make historical-pull
```

## What it does

1. **Environment Setup**: Automatically exports `GOOGLE_APPLICATION_CREDENTIALS` if the service account key is present at `~/keys/minijust-scraper.json`

2. **Scraper Execution**: Runs the gazette scraper with:
   - `GAZETTE_CRAWLER_DEPTH=2` (month-level traversal)
   - `--since 2004` (complete historical coverage)
   - `--out ./state-prod` (production output directory)

3. **Summary Report**: Prints detailed metrics including:
   - Files discovered
   - Files downloaded
   - Files skipped  
   - Errors encountered
   - Success rate percentage
   - Manifest file location

## Output

- **Files**: Downloaded PDFs saved to `./state-prod/`
- **Manifest**: CSV file at `./state-prod/gazettes.csv`
- **Logs**: Complete output logged to `./state-prod/scraper-output.log`
- **GCS**: Automatic uploads to `gs://rwandan_laws/gazette_pdfs/` (if credentials available)

## Compatibility

- ✅ macOS
- ✅ CI runners (GitHub Actions, etc.)
- ✅ Works with or without GCS credentials
- ✅ No additional dependencies required

## Example Output

```bash
🚀 Starting Historical Gazette Pull
==================================
✅ Google Cloud credentials found and exported
   Path: /Users/<USER>/keys/minijust-scraper.json
📁 Output directory: ./state-prod

📋 Configuration:
   Since year: 2004
   Crawler depth: 2 (month-level)
   Output directory: ./state-prod
   GCS enabled: Yes

🔄 Starting scraper...
[... scraping progress ...]

📊 HISTORICAL PULL SUMMARY
=========================
📈 Files discovered: 1,247
⬇️  Files downloaded: 1,195
⏭️  Files skipped: 47
❌ Errors: 5
✅ Success rate: 95%
📄 Manifest entries: 1,195
📄 Manifest file: ./state-prod/gazettes.csv

🎉 Historical pull completed successfully!

📋 Next steps:
   • Review files in: ./state-prod
   • Check manifest: ./state-prod/gazettes.csv
   • Verify GCS uploads: gs://rwandan_laws/gazette_pdfs/
   • Monitor logs: ./state-prod/scraper-output.log
```

## Script Location

The underlying bash script is located at `./scripts/historical-pull.sh` and can also be run directly if needed.