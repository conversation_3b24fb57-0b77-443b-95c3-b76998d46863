#!/usr/bin/env python3
"""
COMPLETE PDF DOWNLOAD WORKFLOW TEST

This test verifies the complete end-to-end workflow:
1. Navigate to a day node (we know this works)
2. Discover cases (we know this works)
3. Click on a case to open PDF viewer
4. Find and click download button
5. Download the actual PDF file
6. Verify file integrity

Based on the user's recording, this should work perfectly.
"""

import asyncio
import logging
from pathlib import Path

from gazette_scraper.caselaw.pipeline import Case<PERSON>aw<PERSON><PERSON>eline
from gazette_scraper.caselaw.models import CaseLawConfig, CourtNode, NodeType
from gazette_scraper.caselaw.downloader import CaseLawDownloader

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_complete_pdf_workflow():
    """Test the complete PDF download workflow end-to-end."""
    logger.info("🚨 COMPLETE PDF DOWNLOAD WORKFLOW TEST")
    
    # Initialize configuration
    config = CaseLawConfig(
        output_dir=Path("test_complete_workflow"),
        headless=False,  # Use non-headless to see the process
        debug_mode=True,
        screenshot_on_error=True,
        max_concurrent_downloads=1,
        browser_timeout=60000,
        max_retry_attempts=3
    )

    # Ensure output directory exists
    config.output_dir.mkdir(exist_ok=True)
    
    pipeline = CaseLawPipeline(config)
    
    try:
        await pipeline.start()
        
        # Use a known working day node from our successful tests
        test_day_node = CourtNode(
            court_name=" Supreme Court (911)",
            year=2025,
            month=2,
            day=12,
            node_type=NodeType.DAY,
            full_path=" Supreme Court (911)/2025/February (7)/12 (7)",
            document_count=7
        )
        
        logger.info("=" * 80)
        logger.info("STEP 1: CASE DISCOVERY (We know this works)")
        logger.info("=" * 80)
        
        # Discover cases for this day
        cases = await pipeline.navigator.discover_cases_for_day(test_day_node)
        
        if not cases:
            logger.error("❌ No cases discovered - this should not happen!")
            return False
            
        logger.info(f"✅ Discovered {len(cases)} cases")
        
        # Pick the first case for testing
        test_case = cases[0]
        logger.info(f"Testing with case: {test_case.case_title}")
        logger.info(f"Case page URL: {test_case.case_page_url}")
        
        logger.info("=" * 80)
        logger.info("STEP 2: PDF DOWNLOAD WORKFLOW")
        logger.info("=" * 80)
        
        # Initialize the downloader
        downloader = CaseLawDownloader(config, pipeline.state)
        
        # Get browser context from pipeline
        context = pipeline.navigator.contexts[0] if pipeline.navigator.contexts else None
        if not context:
            logger.error("❌ No browser context available")
            return False
            
        # Test the complete download process
        logger.info("Testing complete case file processing...")
        success = await downloader.process_case_file(context, test_case)
        
        if success:
            logger.info("✅ COMPLETE PDF DOWNLOAD WORKFLOW SUCCESSFUL!")
            
            # Verify the file was actually saved
            expected_dir = (
                config.output_dir 
                / test_case.court_name.replace(" ", "_")
                / str(test_case.year)
                / f"{test_case.month:02d}"
            )
            
            # Look for the saved file
            if expected_dir.exists():
                pdf_files = list(expected_dir.glob("*.pdf"))
                if pdf_files:
                    saved_file = pdf_files[0]
                    file_size = saved_file.stat().st_size
                    logger.info(f"✅ PDF saved successfully: {saved_file}")
                    logger.info(f"✅ File size: {file_size:,} bytes")
                    
                    # Verify it's a valid PDF
                    with open(saved_file, 'rb') as f:
                        header = f.read(4)
                        if header == b'%PDF':
                            logger.info("✅ File is a valid PDF")
                            return True
                        else:
                            logger.error("❌ File is not a valid PDF")
                            return False
                else:
                    logger.error("❌ No PDF files found in expected directory")
                    return False
            else:
                logger.error("❌ Expected output directory not created")
                return False
        else:
            logger.error("❌ PDF download workflow failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        await pipeline.close()


async def main():
    """Main test function."""
    logger.info("Starting Complete PDF Download Workflow Test")
    
    success = await test_complete_pdf_workflow()
    
    if success:
        logger.info("🎉 ALL TESTS PASSED! Complete PDF download workflow is working!")
        print("\n" + "="*80)
        print("🎉 SUCCESS: COMPLETE PDF DOWNLOAD WORKFLOW VERIFIED!")
        print("✅ Navigation: Working")
        print("✅ Case Discovery: Working") 
        print("✅ PDF Download: Working")
        print("✅ File Verification: Working")
        print("="*80)
    else:
        logger.error("❌ Test failed - PDF download workflow needs debugging")
        print("\n" + "="*80)
        print("❌ FAILURE: PDF download workflow needs investigation")
        print("="*80)


if __name__ == "__main__":
    asyncio.run(main())
