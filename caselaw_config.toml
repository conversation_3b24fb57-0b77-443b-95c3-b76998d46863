# Case-Law Scraper Configuration

# General output settings
output_dir = "./caselaw_data"

# Browser configuration
[caselaw]
# Browser timeouts (milliseconds)
browser_timeout = 30000
page_load_timeout = 15000
element_timeout = 10000

# Navigation settings
navigation_delay = 2.0
max_retry_attempts = 3
retry_delay = 5.0
click_retry_attempts = 3

# Concurrency settings
max_browser_contexts = 3
max_concurrent_downloads = 2

# Error handling
screenshot_on_error = true
debug_mode = false
headless = true

# File management
screenshots_dir = "./screenshots"
verify_pdf_content = true
min_pdf_size = 1024

# Resume and state management
resume_from_state = true
state_save_interval = 10

# Rate limiting
request_delay = 1.0
burst_protection = true

# URLs
base_url = "https://amategeko.gov.rw"
caselaw_path = "/laws/judgement/2"

# Optional: Google Cloud Storage integration
[gcs]
# bucket = "your-gcs-bucket"
# project_id = "your-gcp-project"

# Optional: Supabase integration
[supabase]
# url = "https://your-project.supabase.co"
# service_role_key = "${SUPABASE_SERVICE_ROLE_KEY}"