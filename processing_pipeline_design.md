# Rwanda Legal Documents GraphRAG Processing Pipeline

## Architecture Overview

### Current State
- 22 years of PDFs (2004-2025) in GCS storage
- Classified documents with types, categories, keywords
- Supabase metadata database with analytics
- ~1000-5000 estimated documents (based on discovery patterns)

### Target State
- Vector database with legal document embeddings
- Knowledge graph with entities, relationships, citations
- GraphRAG system for legal question answering
- Multi-language support (Kinyarwanda, English, French)

## Processing Pipeline Design

### Phase 1: PDF Text Extraction
```
GCS PDFs → PyPDF2/pdfplumber → Structured Text → Legal Entity Recognition
```

**Components:**
- `PDFProcessor`: Extract text preserving legal structure
- `LanguageDetector`: Handle trilingual content
- `LegalStructureParser`: Identify articles, sections, amendments
- `EntityExtractor`: Names, organizations, locations, dates, legal references

### Phase 2: Intelligent Chunking
```
Structured Text → Legal-Aware Chunking → Semantic Chunks → Metadata Enrichment
```

**Chunking Strategies by Document Type:**
- **Names Registration**: Per individual/company entry
- **Land Use Plans**: Per planning zone/district
- **Appointments**: Per role/position announcement  
- **Cooperatives**: Per organization registration
- **Financial Regulations**: Per regulation section
- **Legal Statutes**: Per article/clause

### Phase 3: Embedding Generation
```
Semantic Chunks → Multilingual Embeddings → Vector Database → Similarity Indexing
```

**Embedding Strategy:**
- **Primary**: multilingual-e5-large for cross-language support
- **Fallback**: text-embedding-3-large for English content
- **Specialized**: Legal domain fine-tuned models if available

### Phase 4: Knowledge Graph Construction
```
Entities + Relationships → Graph Database → Entity Linking → Citation Networks
```

**Graph Schema:**
```
Nodes:
- Document (with classification metadata)
- Person (names, roles, appointments)
- Organization (cooperatives, companies, government bodies)
- Location (districts, sectors, planning zones)
- LegalConcept (regulations, statutes, procedures)
- TimeReference (publication dates, effective dates)

Relationships:
- CITES (document → document)
- AMENDS (document → document) 
- APPOINTS (document → person)
- REGULATES (document → organization/location)
- SUPERSEDES (document → document)
- IMPLEMENTS (document → legal_concept)
```

### Phase 5: GraphRAG Integration
```
Vector DB + Knowledge Graph → RAG Pipeline → Legal Q&A System
```

## Implementation Strategy

### Option A: Cloud-Native Processing (Recommended)
**Pros:** Scalable, managed services, parallel processing
**Cons:** Higher cost, vendor lock-in

**Stack:**
- **Compute**: Google Cloud Run/Functions for serverless processing
- **Vector DB**: Pinecone or ChromaDB hosted
- **Graph DB**: Neo4j AuraDB or Google Cloud Graph
- **Orchestration**: Google Cloud Workflows or Airflow

**Processing Flow:**
1. Cloud Function triggered by GCS uploads
2. Parallel PDF processing with batch jobs
3. Vector embeddings stored in Pinecone
4. Graph construction via Neo4j ingestion
5. GraphRAG API served via Cloud Run

### Option B: Hybrid Local/Cloud Processing  
**Pros:** Cost-effective, flexible, data control
**Cons:** More infrastructure management

**Stack:**
- **Local Processing**: Heavy PDF/NLP work on local machines
- **Cloud Storage**: Vectors and graphs in cloud databases
- **Hybrid Orchestration**: Local scripts + cloud APIs

**Processing Flow:**
1. Download PDFs from GCS in batches
2. Local processing for text extraction + chunking
3. Cloud API calls for embeddings (OpenAI/Anthropic)
4. Upload vectors and graph data to cloud databases

### Option C: Fully Local Processing
**Pros:** Complete control, no API costs, privacy
**Cons:** Limited by local compute, harder scaling

**Stack:**
- **Local Models**: Sentence-transformers for embeddings
- **Local Databases**: PostgreSQL + pgvector + Neo4j Community
- **Local Processing**: Python pipeline with multiprocessing

## Recommended Implementation Plan

### Phase 1: Foundation (Week 1-2)
```bash
# Add processing modules to existing scraper
gazette_scraper/
├── processors/
│   ├── pdf_processor.py      # PDF text extraction
│   ├── chunking.py           # Legal-aware chunking  
│   ├── embedding.py          # Vector generation
│   └── graph_builder.py      # Knowledge graph construction
├── models/
│   └── processed_document.py # New data models
└── config/
    └── processing.toml       # Processing configuration
```

### Phase 2: Batch Processing System (Week 3-4)
```bash
# New CLI commands
python -m gazette_scraper process --mode text_extraction --batch-size 100
python -m gazette_scraper process --mode chunking --document-type land_use_plans  
python -m gazette_scraper process --mode embedding --model multilingual-e5-large
python -m gazette_scraper process --mode graph_build --relationship-type citations
```

### Phase 3: GraphRAG Integration (Week 5-6)
```bash
# GraphRAG query interface
python -m gazette_scraper query "What are the land use regulations for Kigali?"
python -m gazette_scraper query "List all cooperative registrations in 2023"
python -m gazette_scraper query "Who was appointed to Ministry of Justice in 2022?"
```

## Performance Considerations

### Estimated Data Volume:
- **Documents**: ~3,000 PDFs (average estimate)
- **Text Content**: ~500MB extracted text
- **Chunks**: ~50,000 semantic chunks
- **Vectors**: ~1.2GB (768-dim embeddings)
- **Graph Nodes**: ~100,000 entities
- **Graph Relationships**: ~500,000 relationships

### Processing Time Estimates:
- **PDF Extraction**: ~1-2 minutes per document (3-6 hours total)
- **Chunking**: ~10 seconds per document (30 minutes total)
- **Embedding**: ~5 seconds per chunk (4-5 hours with API calls)
- **Graph Construction**: ~2-3 hours for full dataset

### Cost Estimates (Option A - Cloud Native):
- **Embedding API**: ~$50-100 (OpenAI text-embedding-3-large)
- **Vector Storage**: ~$20/month (Pinecone)
- **Graph Database**: ~$50-100/month (Neo4j AuraDB)
- **Compute**: ~$30-50 (Cloud Run processing)

**Total Monthly**: ~$100-150 operational costs

## Next Steps

1. **Start with Option B (Hybrid)** - good balance of cost/performance
2. **Implement Phase 1** - PDF processing foundation
3. **Test with 2025 documents** - validate pipeline before historical backfill
4. **Scale incrementally** - process year by year to monitor resource usage
5. **Optimize based on results** - adjust chunking/embedding strategies

This approach leverages the existing classification system while building a robust foundation for legal document analysis and querying.