#!/bin/bash

# Rwanda Case-Law Scraper - Hybrid Deployment Script
# Deploys to Cloud Run with Supabase (metadata) + GCS (PDFs)

set -euo pipefail

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration from .env
PROJECT_ID="rwandan-law-bot-440710"
SERVICE_NAME="rwanda-caselaw-scraper"
REGION="us-central1"
GCS_BUCKET="rwandan-caselaws"
ARTIFACT_REGISTRY_REPO="caselaw-scraper"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if gcloud is installed and configured
check_gcloud() {
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI is not installed. Please install it first:"
        print_error "https://cloud.google.com/sdk/docs/install"
        exit 1
    fi

    # Set project
    print_status "Setting project to $PROJECT_ID..."
    gcloud config set project $PROJECT_ID
    print_success "Project set to $PROJECT_ID"
}

# Function to enable required APIs
enable_apis() {
    print_status "Enabling required Google Cloud APIs..."
    
    local apis=(
        "cloudbuild.googleapis.com"
        "run.googleapis.com"
        "storage.googleapis.com"
        "artifactregistry.googleapis.com"
        "secretmanager.googleapis.com"
        "logging.googleapis.com"
        "monitoring.googleapis.com"
    )
    
    for api in "${apis[@]}"; do
        print_status "Enabling $api..."
        gcloud services enable $api --quiet
    done
    
    print_success "All APIs enabled"
}

# Function to create GCS bucket
setup_gcs() {
    print_status "Setting up Google Cloud Storage..."
    
    # Check if bucket exists
    if gsutil ls gs://$GCS_BUCKET &>/dev/null; then
        print_success "GCS bucket gs://$GCS_BUCKET already exists"
    else
        print_status "Creating GCS bucket gs://$GCS_BUCKET..."
        gsutil mb -p $PROJECT_ID -c STANDARD -l $REGION gs://$GCS_BUCKET
        print_success "GCS bucket created"
    fi
    
    # Set lifecycle policy for cost optimization
    print_status "Setting up lifecycle policy for cost optimization..."
    cat > lifecycle.json << EOF
{
  "lifecycle": {
    "rule": [
      {
        "action": {"type": "SetStorageClass", "storageClass": "NEARLINE"},
        "condition": {"age": 30}
      },
      {
        "action": {"type": "SetStorageClass", "storageClass": "COLDLINE"},
        "condition": {"age": 90}
      },
      {
        "action": {"type": "SetStorageClass", "storageClass": "ARCHIVE"},
        "condition": {"age": 365}
      }
    ]
  }
}
EOF
    
    gsutil lifecycle set lifecycle.json gs://$GCS_BUCKET
    rm lifecycle.json
    print_success "Lifecycle policy applied"
}

# Function to create Artifact Registry repository
setup_artifact_registry() {
    print_status "Setting up Artifact Registry..."
    
    # Check if repository exists
    if gcloud artifacts repositories describe $ARTIFACT_REGISTRY_REPO --location=$REGION &>/dev/null; then
        print_success "Artifact Registry repository already exists"
    else
        print_status "Creating Artifact Registry repository..."
        gcloud artifacts repositories create $ARTIFACT_REGISTRY_REPO \
            --repository-format=docker \
            --location=$REGION \
            --description="Rwanda Case-Law Scraper container images"
        print_success "Artifact Registry repository created"
    fi
}

# Function to create service account
setup_service_account() {
    print_status "Setting up service account..."
    
    local sa_name="caselaw-scraper-sa"
    local sa_email="${sa_name}@${PROJECT_ID}.iam.gserviceaccount.com"
    
    # Check if service account exists
    if gcloud iam service-accounts describe $sa_email &>/dev/null; then
        print_success "Service account already exists"
    else
        print_status "Creating service account..."
        gcloud iam service-accounts create $sa_name \
            --display-name="Case-Law Scraper Service Account" \
            --description="Service account for Rwanda case-law scraper"
        print_success "Service account created"
    fi
    
    # Grant necessary permissions
    print_status "Granting permissions to service account..."
    
    local roles=(
        "roles/storage.objectAdmin"
        "roles/logging.logWriter"
        "roles/monitoring.metricWriter"
        "roles/secretmanager.secretAccessor"
    )
    
    for role in "${roles[@]}"; do
        gcloud projects add-iam-policy-binding $PROJECT_ID \
            --member="serviceAccount:$sa_email" \
            --role="$role" \
            --quiet
    done
    
    print_success "Service account permissions configured"
}

# Function to build and push container
build_and_push() {
    print_status "Building and pushing container image..."
    
    local image_url="${REGION}-docker.pkg.dev/${PROJECT_ID}/${ARTIFACT_REGISTRY_REPO}/${SERVICE_NAME}:latest"
    
    # Configure Docker for Artifact Registry
    gcloud auth configure-docker ${REGION}-docker.pkg.dev --quiet
    
    # Build image
    print_status "Building Docker image..."
    docker build --platform linux/amd64 -t $image_url .
    
    # Push image
    print_status "Pushing image to Artifact Registry..."
    docker push $image_url
    
    print_success "Container image built and pushed: $image_url"
}

# Function to deploy to Cloud Run
deploy_cloud_run() {
    print_status "Deploying to Cloud Run..."
    
    local image_url="${REGION}-docker.pkg.dev/${PROJECT_ID}/${ARTIFACT_REGISTRY_REPO}/${SERVICE_NAME}:latest"
    local sa_email="caselaw-scraper-sa@${PROJECT_ID}.iam.gserviceaccount.com"
    
    gcloud run deploy $SERVICE_NAME \
        --image $image_url \
        --platform managed \
        --region $REGION \
        --memory 4Gi \
        --cpu 2 \
        --timeout 3600 \
        --concurrency 1 \
        --min-instances 0 \
        --max-instances 3 \
        --service-account $sa_email \
        --set-env-vars "GOOGLE_CLOUD_PROJECT=${PROJECT_ID}" \
        --set-env-vars "CASE_LAWS_GCS_BUCKET=${GCS_BUCKET}" \
        --set-env-vars "CASELAW_HEADLESS=true" \
        --set-env-vars "CASELAW_DEBUG_MODE=false" \
        --set-env-vars "CASELAW_MAX_CONCURRENT_DOWNLOADS=2" \
        --allow-unauthenticated \
        --quiet
    
    print_success "Deployed to Cloud Run"
    
    # Get service URL
    local service_url=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")
    print_success "Service URL: $service_url"
}

# Function to create secrets (if needed)
setup_secrets() {
    print_status "Setting up secrets (if needed)..."
    
    # Check if Supabase key secret exists
    if ! gcloud secrets describe supabase-service-key &>/dev/null; then
        print_warning "Supabase service key secret not found."
        print_warning "You'll need to create it manually:"
        print_warning "echo 'your-supabase-key' | gcloud secrets create supabase-service-key --data-file=-"
    else
        print_success "Supabase secret already exists"
    fi
}

# Main deployment function
main() {
    echo "🚀 Rwanda Case-Law Scraper - Hybrid Deployment"
    echo "=============================================="
    echo "📋 Configuration:"
    echo "   Project ID: $PROJECT_ID"
    echo "   Service: $SERVICE_NAME"
    echo "   Region: $REGION"
    echo "   GCS Bucket: $GCS_BUCKET"
    echo "   Approach: Supabase (metadata) + GCS (PDFs)"
    echo ""
    
    check_gcloud
    enable_apis
    setup_gcs
    setup_artifact_registry
    setup_service_account
    setup_secrets
    build_and_push
    deploy_cloud_run
    
    echo ""
    print_success "🎉 Deployment completed successfully!"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Add Supabase credentials to Secret Manager (if not done)"
    echo "2. Test the deployment with a small scraping job"
    echo "3. Set up Cloud Scheduler for automated runs"
    echo ""
    echo "💡 Test your deployment:"
    echo "   curl \$(gcloud run services describe $SERVICE_NAME --region=$REGION --format='value(status.url)')/health"
}

# Run main function
main "$@"
