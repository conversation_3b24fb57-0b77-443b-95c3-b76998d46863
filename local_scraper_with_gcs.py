#!/usr/bin/env python3
"""
Local Supreme Court scraper with GCS upload and cleanup.
Runs locally for reliable discovery, uploads to GCS, then cleans up.
"""

import asyncio
import logging
import os
import shutil
from pathlib import Path
from typing import List, Optional
from datetime import datetime

import argparse

from google.cloud import storage
from gazette_scraper.caselaw.pipeline import CaseLawPipeline
from gazette_scraper.caselaw.models import CourtNode, NodeType, CaseLawConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LocalScraperWithGCS:
    """Local scraper that uploads to GCS and cleans up."""

    def __init__(self, gcs_bucket: str, gcs_project_id: Optional[str] = None):
        self.gcs_bucket = gcs_bucket
        self.gcs_project_id = gcs_project_id
        self.storage_client = None
        self.bucket = None

    def _init_gcs(self):
        """Initialize GCS client and bucket."""
        try:
            if self.gcs_project_id:
                self.storage_client = storage.Client(project=self.gcs_project_id)
            else:
                self.storage_client = storage.Client()

            self.bucket = self.storage_client.bucket(self.gcs_bucket)
            logger.info(f"✅ GCS initialized: bucket={self.gcs_bucket}")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to initialize GCS: {e}")
            return False

    def upload_file_to_gcs(self, local_path: Path, gcs_path: str) -> bool:
        """Upload a single file to GCS."""
        try:
            blob = self.bucket.blob(gcs_path)
            blob.upload_from_filename(str(local_path))
            logger.info(f"✅ Uploaded: {gcs_path}")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to upload {local_path}: {e}")
            return False

    def upload_directory_to_gcs(self, local_dir: Path, gcs_prefix: str = "case_law_pdfs/") -> List[str]:
        """Upload all PDFs in directory to GCS and return uploaded paths."""
        uploaded_files = []

        if not local_dir.exists():
            logger.warning(f"Directory does not exist: {local_dir}")
            return uploaded_files

        # Find all PDF files
        pdf_files = list(local_dir.rglob("*.pdf"))
        logger.info(f"Found {len(pdf_files)} PDF files to upload")

        for pdf_file in pdf_files:
            # Create GCS path maintaining directory structure
            relative_path = pdf_file.relative_to(local_dir.parent)
            gcs_path = f"{gcs_prefix}{relative_path.as_posix()}"

            if self.upload_file_to_gcs(pdf_file, gcs_path):
                uploaded_files.append(gcs_path)

        return uploaded_files

    def cleanup_local_files(self, local_dir: Path, uploaded_files: List[str]) -> int:
        """Delete local files that were successfully uploaded."""
        deleted_count = 0

        for gcs_path in uploaded_files:
            # Convert GCS path back to local path
            relative_path = gcs_path.replace("case_law_pdfs/", "")
            local_file = local_dir.parent / relative_path

            if local_file.exists():
                try:
                    local_file.unlink()
                    deleted_count += 1
                    logger.info(f"🗑️  Deleted: {local_file}")
                except Exception as e:
                    logger.error(f"❌ Failed to delete {local_file}: {e}")

        # Clean up empty directories
        self._cleanup_empty_dirs(local_dir)

        return deleted_count

    def _cleanup_empty_dirs(self, directory: Path):
        """Remove empty directories recursively."""
        try:
            for item in directory.rglob("*"):
                if item.is_dir() and not any(item.iterdir()):
                    item.rmdir()
                    logger.debug(f"Removed empty directory: {item}")
        except Exception as e:
            logger.debug(f"Error cleaning empty directories: {e}")

    async def scrape_and_upload(self, court_name: str, year: Optional[int] = None,
                               month: Optional[int] = None, day: Optional[int] = None) -> dict:
        """Main method: scrape locally, upload to GCS, cleanup."""

        start_time = datetime.now()
        logger.info(f"🚀 Starting local scrape and GCS upload")
        logger.info(f"🎯 Target: {court_name}" + (f"/{year}" if year else "") +
                   (f"/{month}" if month else "") + (f"/{day}" if day else ""))

        # Initialize GCS
        if not self._init_gcs():
            return {"success": False, "error": "Failed to initialize GCS"}

        # Create target node
        node_type = NodeType.DAY if day else (NodeType.MONTH if month else (NodeType.YEAR if year else NodeType.COURT))
        target_node = CourtNode(
            court_name=court_name,
            year=year,
            month=month,
            day=day,
            node_type=node_type,
            full_path=f"{court_name}" + (f"/{year}" if year else "") +
                     (f"/{month}" if month else "") + (f"/{day}" if day else ""),
            document_count=0
        )

        try:
            # Step 1: Run local scraping
            logger.info("📥 Step 1: Running local scraping...")
            config = CaseLawConfig(
                output_dir=Path("caselaw_data"),
                headless=True,  # Can be False for debugging
                debug_mode=False,
                screenshot_on_error=True
            )

            # Run scraping pipeline
            async with CaseLawPipeline(config) as pipeline:
                result = await pipeline.scrape_node(target_node)

            # Check if scraping was successful
            if result.errors > 0:
                return {"success": False, "error": f"Scraping failed with {result.errors} errors"}

            downloaded_count = result.downloaded
            logger.info(f"✅ Scraping completed: {downloaded_count} files downloaded")

            if downloaded_count == 0:
                duration = datetime.now() - start_time
                return {"success": True, "downloaded": 0, "uploaded": 0, "deleted": 0, "message": "No files to process", "duration_seconds": duration.total_seconds(), "gcs_files": []}

            # Step 2: Upload to GCS
            logger.info("☁️  Step 2: Uploading to GCS...")
            # Use the actual directory structure created by the scraper
            local_dir = config.output_dir / court_name.replace(" ", "_").replace("(", "_").replace(")", "_").replace("__", "_")
            if not local_dir.exists():
                # Try alternative naming patterns
                alt_names = [
                    court_name.replace(" ", "_"),
                    court_name.replace("(", "_").replace(")", "_"),
                    court_name.replace(" (", "_(").replace(")", ")")
                ]
                for alt_name in alt_names:
                    alt_dir = config.output_dir / alt_name
                    if alt_dir.exists():
                        local_dir = alt_dir
                        break
                else:
                    # List all directories to find the correct one
                    if config.output_dir.exists():
                        dirs = [d for d in config.output_dir.iterdir() if d.is_dir()]
                        logger.info(f"Available directories: {[d.name for d in dirs]}")
                        # Use the first directory that contains the court name
                        for d in dirs:
                            if "Supreme" in d.name and "Court" in d.name:
                                local_dir = d
                                break

            uploaded_files = self.upload_directory_to_gcs(local_dir)

            logger.info(f"✅ Upload completed: {len(uploaded_files)} files uploaded")

            # Step 3: Cleanup local files
            logger.info("🗑️  Step 3: Cleaning up local files...")
            deleted_count = self.cleanup_local_files(local_dir, uploaded_files)

            logger.info(f"✅ Cleanup completed: {deleted_count} files deleted")

            # Summary
            duration = datetime.now() - start_time
            summary = {
                "success": True,
                "downloaded": downloaded_count,
                "uploaded": len(uploaded_files),
                "deleted": deleted_count,
                "duration_seconds": duration.total_seconds(),
                "gcs_files": uploaded_files
            }

            logger.info(f"🎉 Process completed successfully in {duration.total_seconds():.1f}s")
            logger.info(f"📊 Summary: {downloaded_count} downloaded, {len(uploaded_files)} uploaded, {deleted_count} deleted")

            return summary

        except Exception as e:
            logger.error(f"❌ Error during scrape and upload: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}

async def main():
    """Main entry point for running the scraper."""

    parser = argparse.ArgumentParser(description="Local Supreme Court scraper with GCS upload and cleanup")
    parser.add_argument("--court", "-c", type=str, default="Supreme Court (911)", help="Court name as displayed on the site, e.g., 'Supreme Court (911)'")
    parser.add_argument("--year", "-y", type=int, default=2023, help="Target year (e.g., 2023)")
    parser.add_argument("--month", "-m", type=int, default=None, help="Optional target month (1-12)")
    parser.add_argument("--day", "-d", type=int, default=None, help="Optional target day of the month (1-31)")
    parser.add_argument("--bucket", "-b", type=str, default="rwandan-caselaws", help="GCS bucket name")
    parser.add_argument("--project-id", "-p", type=str, default=None, help="GCP project ID (optional; uses default credentials if omitted)")
    args = parser.parse_args()

    # Configuration
    GCS_BUCKET = args.bucket
    GCS_PROJECT_ID = args.project_id

    # Target
    COURT_NAME = args.court
    YEAR = args.year
    MONTH = args.month
    DAY = args.day

    # Create scraper instance
    scraper = LocalScraperWithGCS(
        gcs_bucket=GCS_BUCKET,
        gcs_project_id=GCS_PROJECT_ID
    )

    # Run scraping and upload
    result = await scraper.scrape_and_upload(
        court_name=COURT_NAME,
        year=YEAR,
        month=MONTH,
        day=DAY,
    )

    # Print results
    if result["success"]:
        print(f"\n🎉 SUCCESS!")
        print(f"📥 Downloaded: {result['downloaded']} files")
        print(f"☁️  Uploaded: {result['uploaded']} files")
        print(f"🗑️  Deleted: {result['deleted']} files")
        print(f"⏱️  Duration: {result['duration_seconds']:.1f} seconds")
        print(f"\n📁 GCS Files:")
        for gcs_file in result.get('gcs_files', []):
            print(f"  - {gcs_file}")
    else:
        print(f"\n❌ FAILED: {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    asyncio.run(main())
