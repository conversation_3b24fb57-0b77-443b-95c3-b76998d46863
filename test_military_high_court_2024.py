#!/usr/bin/env python3
"""
Test script to explore Military High Court 2024 and validate the fixed scraper.
"""

import asyncio
import logging
from pathlib import Path
from gazette_scraper.caselaw.navigator import CaseLawNavigator
from gazette_scraper.caselaw.models import CourtNode, NodeType
from gazette_scraper.config import load_caselaw_config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_military_high_court_2024():
    """Test Military High Court 2024 - discover all months, days, and validate no phantom data."""
    
    logger.info("🚀 Testing Military High Court 2024")
    logger.info("🎯 Goal: Discover all months, days, and validate no phantom data")
    
    # Load configuration
    config = load_caselaw_config()
    
    # Initialize navigator
    navigator = CaseLawNavigator(config)
    
    try:
        await navigator.start()
        logger.info("✅ Browser initialized successfully")
        
        # Step 1: Navigate and find Military High Court
        context = navigator.contexts[0]
        page = await context.new_page()
        
        url = f"{config.base_url}{config.caselaw_path}"
        logger.info(f"🌐 Navigating to: {url}")
        await page.goto(url, wait_until="networkidle", timeout=config.page_load_timeout)
        await asyncio.sleep(config.navigation_delay)
        
        # Find Military High Court
        logger.info("🔍 Looking for Military High Court...")
        court_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
        
        military_court_element = None
        for element in court_elements:
            text = await element.text_content()
            if text and "Military High Court" in text:
                logger.info(f"📋 Found: {text}")
                military_court_element = element
                break
        
        if not military_court_element:
            logger.error("❌ Military High Court not found!")
            return
        
        # Check if Military High Court has any content
        military_text = await military_court_element.text_content()
        logger.info(f"📊 Military High Court text: '{military_text}'")
        
        # Look for document count in parentheses
        import re
        doc_count_match = re.search(r'\((\d+)\)', military_text)
        if doc_count_match:
            doc_count = int(doc_count_match.group(1))
            logger.info(f"📄 Military High Court total documents: {doc_count}")
            
            if doc_count == 0:
                logger.info("ℹ️  Military High Court has 0 documents - this is expected")
                logger.info("✅ VALIDATION PASSED: Empty court correctly shows 0 documents")
                return
        else:
            logger.warning("⚠️  Could not parse document count from Military High Court")
        
        # Expand Military High Court
        logger.info("🔄 Expanding Military High Court...")
        await military_court_element.click()
        await asyncio.sleep(config.navigation_delay)
        
        # Find 2024
        logger.info("🔍 Looking for 2024...")
        year_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
        
        year_2024_element = None
        for element in year_elements:
            text = await element.text_content()
            if text and "2024" in text:
                logger.info(f"📅 Found: {text}")
                year_2024_element = element
                break
        
        if not year_2024_element:
            logger.info("ℹ️  2024 not found in Military High Court - this may be expected if no documents exist")
            logger.info("✅ VALIDATION PASSED: Empty court correctly shows no years")
            return
        
        # Expand 2024
        logger.info("🔄 Expanding 2024...")
        await year_2024_element.click()
        await asyncio.sleep(config.navigation_delay)
        
        # Create year node for testing
        year_node = CourtNode(
            court_name="Military High Court",
            year=2024,
            node_type=NodeType.YEAR,
            full_path="Military High Court/2024",
            document_count=0  # Will be updated
        )
        
        # Step 2: Discover all months
        logger.info("📅 Discovering all months in 2024...")
        discovered_months = await navigator._discover_months(page, year_node)
        
        logger.info(f"📊 MONTHS DISCOVERED: {len(discovered_months)}")
        
        if len(discovered_months) == 0:
            logger.info("ℹ️  No months found in Military High Court 2024")
            logger.info("✅ VALIDATION PASSED: Empty year correctly shows no months")
            return
        
        month_names = ["", "January", "February", "March", "April", "May", "June",
                      "July", "August", "September", "October", "November", "December"]
        
        total_days = 0
        total_documents = 0
        all_day_nodes = []
        month_summary = {}
        
        for month in discovered_months:
            month_name = month_names[month.month] if month.month <= 12 else f"Month{month.month}"
            logger.info(f"  📆 {month_name} ({month.document_count} documents)")
            
            # Expand this month to discover days
            logger.info(f"🔄 Expanding {month_name}...")
            
            # Find and click the month element
            month_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
            for element in month_elements:
                text = await element.text_content()
                if text and month_name.lower() in text.lower() and "(" in text:
                    await element.click()
                    await asyncio.sleep(config.navigation_delay)
                    break
            
            # Discover days for this month
            discovered_days = await navigator._discover_days(page, month)
            logger.info(f"    📄 Days in {month_name}: {len(discovered_days)}")
            
            day_numbers = []
            month_doc_count = 0
            
            for day in discovered_days:
                day_num = day.day
                logger.info(f"      Day {day_num}: {day.document_count} documents")
                day_numbers.append(day_num)
                month_doc_count += day.document_count
                all_day_nodes.append(day)
            
            day_numbers.sort()
            month_summary[month_name] = {
                'days': day_numbers,
                'day_count': len(discovered_days),
                'document_count': month_doc_count
            }
            
            total_days += len(discovered_days)
            total_documents += month_doc_count
        
        # Step 3: Summary and Validation
        logger.info("\n" + "="*60)
        logger.info("📊 MILITARY HIGH COURT 2024 SUMMARY")
        logger.info("="*60)
        logger.info(f"🗓️  Total Months: {len(discovered_months)}")
        logger.info(f"📅 Total Days: {total_days}")
        logger.info(f"📄 Total Documents: {total_documents}")
        
        # Detailed breakdown
        logger.info(f"\n📋 DETAILED BREAKDOWN:")
        for month_name, data in month_summary.items():
            logger.info(f"📆 {month_name}:")
            logger.info(f"  - Days: {data['day_count']} ({data['days']})")
            logger.info(f"  - Documents: {data['document_count']}")
        
        # Validation checks
        logger.info(f"\n🔍 VALIDATION CHECKS:")
        
        # Check for reasonable day counts
        validation_passed = True
        for month_name, data in month_summary.items():
            max_days_in_month = 31  # Simplified check
            if data['day_count'] > max_days_in_month:
                logger.error(f"❌ {month_name}: Too many days ({data['day_count']} > {max_days_in_month})")
                validation_passed = False
            else:
                logger.info(f"✅ {month_name}: Reasonable day count ({data['day_count']} days)")
        
        # Check for valid day numbers
        for month_name, data in month_summary.items():
            invalid_days = [day for day in data['days'] if day < 1 or day > 31]
            if invalid_days:
                logger.error(f"❌ {month_name}: Invalid day numbers: {invalid_days}")
                validation_passed = False
            else:
                logger.info(f"✅ {month_name}: All day numbers valid (1-31)")
        
        # Overall validation result
        if validation_passed:
            logger.info(f"\n🎉 VALIDATION PASSED!")
            logger.info(f"✅ No phantom months or days detected")
            logger.info(f"✅ All day counts are reasonable")
            logger.info(f"✅ All day numbers are valid")
            logger.info(f"✅ Military High Court 2024 structure is accurate")
        else:
            logger.error(f"\n❌ VALIDATION FAILED!")
            logger.error(f"Issues detected in the discovered structure")
        
        # Step 4: Sample document discovery (first few days)
        if total_documents > 0:
            logger.info(f"\n📥 SAMPLE DOCUMENT DISCOVERY:")
            sample_count = min(3, len(all_day_nodes))
            
            for i, day_node in enumerate(all_day_nodes[:sample_count]):
                try:
                    logger.info(f"📄 Sample {i+1}: {day_node.full_path}")
                    
                    # Discover cases for this day
                    cases = await navigator.discover_cases_for_day(day_node)
                    logger.info(f"  Found {len(cases)} cases")
                    
                    for j, case in enumerate(cases[:2]):  # Show first 2 cases
                        logger.info(f"    {j+1}. {case.title}")
                        
                except Exception as e:
                    logger.error(f"  ❌ Error discovering cases: {e}")
        else:
            logger.info(f"\n📥 No documents to sample")
        
    except Exception as e:
        logger.error(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await navigator.close()
        logger.info("🧹 Cleanup completed")

if __name__ == "__main__":
    asyncio.run(test_military_high_court_2024())
