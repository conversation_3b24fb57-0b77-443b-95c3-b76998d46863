#!/bin/bash
set -e

# Google Cloud Storage Setup Script for Rwanda Gazette Scraper
# This script sets up GCS integration for the gazette scraper

echo "🚀 Setting up Google Cloud Storage for Rwanda Gazette Scraper"
echo "=============================================================="

# Configuration
PROJECT_ID="rwandan-law-bot-440710"
BUCKET_NAME="rwandan_laws"
SA_NAME="minijust-scraper-sa"
SA_EMAIL="${SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"
KEY_FILE="$HOME/keys/minijust-scraper.json"

# Check if gcloud is installed and authenticated
echo "📋 Step 1: Checking Google Cloud CLI setup..."
if ! command -v gcloud &> /dev/null; then
    echo "❌ Google Cloud CLI not found. Please install it first:"
    echo "   curl https://sdk.cloud.google.com | bash"
    exit 1
fi

echo "✅ Google Cloud CLI found"

# Authenticate if needed
echo "📋 Step 2: Checking authentication..."
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
    echo "🔐 Please authenticate with Google Cloud:"
    gcloud auth login
else
    echo "✅ Already authenticated"
fi

# Set project
echo "📋 Step 3: Setting project to $PROJECT_ID..."
gcloud config set project $PROJECT_ID
echo "✅ Project set to $PROJECT_ID"

# Check if service account exists
echo "📋 Step 4: Checking service account..."
if gcloud iam service-accounts describe $SA_EMAIL &>/dev/null; then
    echo "✅ Service account $SA_NAME already exists"
else
    echo "🔨 Creating service account $SA_NAME..."
    gcloud iam service-accounts create $SA_NAME \
        --display-name="Minijust Scraper Service Account" \
        --description="Service account for Rwanda gazette scraper GCS uploads"
    echo "✅ Service account created"
fi

# Check if bucket exists and grant permissions
echo "📋 Step 5: Checking bucket permissions..."
if gsutil ls gs://$BUCKET_NAME &>/dev/null; then
    echo "✅ Bucket gs://$BUCKET_NAME exists"
    
    # Grant bucket permissions
    echo "🔨 Granting objectAdmin permissions to service account..."
    gsutil iam ch serviceAccount:$SA_EMAIL:objectAdmin gs://$BUCKET_NAME
    echo "✅ Permissions granted"
else
    echo "❌ Bucket gs://$BUCKET_NAME does not exist or is not accessible"
    echo "   Please create the bucket first or check permissions"
    exit 1
fi

# Create keys directory
echo "📋 Step 6: Setting up service account key..."
mkdir -p "$(dirname "$KEY_FILE")"

# Generate service account key if it doesn't exist
if [[ -f "$KEY_FILE" ]]; then
    echo "✅ Service account key already exists at $KEY_FILE"
else
    echo "🔑 Creating service account key..."
    gcloud iam service-accounts keys create "$KEY_FILE" \
        --iam-account=$SA_EMAIL
    echo "✅ Service account key created at $KEY_FILE"
fi

# Set file permissions
chmod 600 "$KEY_FILE"
echo "✅ Key file permissions set to 600"

# Set environment variable
echo "📋 Step 7: Setting up environment..."
echo "export GOOGLE_APPLICATION_CREDENTIALS=\"$KEY_FILE\"" >> ~/.bashrc
echo "export GOOGLE_APPLICATION_CREDENTIALS=\"$KEY_FILE\"" >> ~/.zshrc 2>/dev/null || true

# For current session
export GOOGLE_APPLICATION_CREDENTIALS="$KEY_FILE"
echo "✅ Environment variable set for current session"

# Test the setup
echo "📋 Step 8: Testing setup..."
python test_gcs_setup.py

echo ""
echo "🎉 GCS Setup Complete!"
echo "======================="
echo ""
echo "Configuration:"
echo "  Project ID: $PROJECT_ID"
echo "  Bucket: gs://$BUCKET_NAME"
echo "  Service Account: $SA_EMAIL"
echo "  Key File: $KEY_FILE"
echo ""
echo "🧪 Quick Tests:"
echo ""
echo "1. Dry run (discovery only):"
echo "   GAZETTE_CRAWLER_DEPTH=2 python -m gazette_scraper fetch --since 2025 --dry-run --out ./runs/2025-08-06"
echo ""
echo "2. Single file test:"
echo "   python -m gazette_scraper fetch --since 2025 --max 1 --out ./"
echo "   (Check gs://$BUCKET_NAME/gazette_pdfs/ for the uploaded file)"
echo ""
echo "3. Full historical pull:"
echo "   GAZETTE_CRAWLER_DEPTH=2 python -m gazette_scraper fetch --since 2004 --out ./state-prod"
echo ""
echo "⚠️  Remember to restart your shell or run:"
echo "   source ~/.bashrc  # or ~/.zshrc"
echo "   to load the environment variable in new sessions."