#!/usr/bin/env python3
"""
Test script to validate the fixed case-law scraper against known ground truth.
"""

import asyncio
import logging
from gazette_scraper.caselaw.navigator import CaseLawNavigator
from gazette_scraper.config import load_caselaw_config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Known ground truth for validation
GROUND_TRUTH = {
    "High Court": {
        2024: ["January"],  # Only January exists, NOT March
        2023: [],  # Will be discovered
    },
    "Supreme Court": {
        # Will be discovered
    }
}

async def test_fixed_scraper():
    """Test the fixed scraper against known ground truth."""
    
    logger.info("🚀 Testing Fixed Rwanda Case-Law Scraper")
    logger.info("🎯 Validating against known ground truth")
    
    # Load configuration
    config = load_caselaw_config()
    
    # Initialize navigator
    navigator = CaseLawNavigator(config)
    
    try:
        await navigator.start()
        logger.info("✅ Browser initialized successfully")
        
        # Test specific known cases
        await test_high_court_2024(navigator)
        await test_discovery_accuracy(navigator)
        
        logger.info("🎉 All tests completed!")
        
    except Exception as e:
        logger.error(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await navigator.close()
        logger.info("🧹 Cleanup completed")

async def test_high_court_2024(navigator):
    """Test the specific case: High Court 2024 should only have January."""
    logger.info("\n🧪 TEST 1: High Court 2024 Month Discovery")
    logger.info("Expected: Only January (1 document)")
    
    # Get a page
    context = navigator.contexts[0]
    page = await context.new_page()
    
    # Navigate to case-law page
    url = f"{navigator.config.base_url}{navigator.config.caselaw_path}"
    await page.goto(url, wait_until="networkidle", timeout=navigator.config.page_load_timeout)
    await asyncio.sleep(navigator.config.navigation_delay)
    
    # Find and expand High Court
    high_court_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
    high_court_element = None
    
    for element in high_court_elements:
        text = await element.text_content()
        if text and "High Court" in text and "Commercial" not in text:
            high_court_element = element
            break
    
    if not high_court_element:
        logger.error("❌ High Court not found!")
        return False
    
    # Expand High Court
    await high_court_element.click()
    await asyncio.sleep(navigator.config.navigation_delay)
    
    # Find and expand 2024
    year_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
    year_2024_element = None
    
    for element in year_elements:
        text = await element.text_content()
        if text and "2024" in text:
            year_2024_element = element
            break
    
    if not year_2024_element:
        logger.error("❌ 2024 not found in High Court!")
        return False
    
    # Expand 2024
    await year_2024_element.click()
    await asyncio.sleep(navigator.config.navigation_delay)
    
    # Create a mock year node for testing
    from gazette_scraper.caselaw.models import CourtNode, NodeType
    year_node = CourtNode(
        court_name="High Court",
        year=2024,
        node_type=NodeType.YEAR,
        full_path="High Court/2024",
        document_count=1
    )
    
    # Test the fixed month discovery
    discovered_months = await navigator._discover_months(page, year_node)
    
    # Validate results
    logger.info(f"📊 Discovered {len(discovered_months)} months:")
    for month in discovered_months:
        month_names = ["", "January", "February", "March", "April", "May", "June",
                      "July", "August", "September", "October", "November", "December"]
        month_name = month_names[month.month] if month.month <= 12 else f"Month{month.month}"
        logger.info(f"  - {month_name} ({month.document_count} documents)")
    
    # Validation
    if len(discovered_months) == 1:
        month = discovered_months[0]
        if month.month == 1:  # January
            logger.info("✅ PASS: Correctly found only January")
            return True
        else:
            month_names = ["", "January", "February", "March", "April", "May", "June",
                          "July", "August", "September", "October", "November", "December"]
            found_month = month_names[month.month] if month.month <= 12 else f"Month{month.month}"
            logger.error(f"❌ FAIL: Found {found_month} instead of January")
            return False
    elif len(discovered_months) == 0:
        logger.error("❌ FAIL: No months discovered")
        return False
    else:
        logger.error(f"❌ FAIL: Found {len(discovered_months)} months, expected 1")
        return False

async def test_discovery_accuracy(navigator):
    """Test overall discovery accuracy."""
    logger.info("\n🧪 TEST 2: Overall Discovery Accuracy")
    
    try:
        # Run a limited discovery
        court_nodes = await navigator.discover_court_tree()
        
        # Analyze results
        courts = [node for node in court_nodes if node.node_type.name == "COURT"]
        years = [node for node in court_nodes if node.node_type.name == "YEAR"]
        months = [node for node in court_nodes if node.node_type.name == "MONTH"]
        days = [node for node in court_nodes if node.node_type.name == "DAY"]
        
        logger.info(f"📊 Discovery Results:")
        logger.info(f"  Courts: {len(courts)}")
        logger.info(f"  Years: {len(years)}")
        logger.info(f"  Months: {len(months)}")
        logger.info(f"  Days: {len(days)}")
        
        # Check for specific known issues
        high_court_2024_months = [m for m in months if m.court_name == "High Court" and m.year == 2024]
        
        if len(high_court_2024_months) == 1 and high_court_2024_months[0].month == 1:
            logger.info("✅ PASS: High Court 2024 correctly shows only January")
            return True
        else:
            logger.error(f"❌ FAIL: High Court 2024 shows {len(high_court_2024_months)} months")
            for month in high_court_2024_months:
                month_names = ["", "January", "February", "March", "April", "May", "June",
                              "July", "August", "September", "October", "November", "December"]
                month_name = month_names[month.month] if month.month <= 12 else f"Month{month.month}"
                logger.error(f"  - {month_name}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Discovery test failed: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_fixed_scraper())
