#!/usr/bin/env python3
"""Test script for the new structured output approach."""

import sys
import time
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from gazette_scraper.extractor.gemini_structured_client import GeminiStructuredClient


def test_structured_output():
    """Test the new structured output approach vs old JSON repair."""
    print("🧪 Testing Structured Output vs JSON Repair Approach")
    print("=" * 60)
    
    # Find a test PDF
    data_dir = Path("data")
    pdf_files = list(data_dir.rglob("*.pdf"))
    
    if not pdf_files:
        print("❌ No PDF files found in data directory")
        return
    
    # Use the smallest PDF for testing
    test_pdf = min(pdf_files, key=lambda p: p.stat().st_size)
    print(f"📄 Testing with: {test_pdf.name}")
    print(f"📊 File size: {test_pdf.stat().st_size / 1024:.1f} KB")
    
    # Create output directory
    output_dir = Path("test_structured_comparison")
    output_dir.mkdir(exist_ok=True)

    # Test 1: New Structured Output Approach
    print("\n🚀 Testing NEW Structured Output Approach...")
    start_time = time.time()

    try:
        client = GeminiStructuredClient()
        response = client.extract_pages(
            pdf_path=test_pdf,
            source_filename=test_pdf.name,
            doc_type_hint="presidential_decree"
        )
        
        structured_time = time.time() - start_time
        structured_success = True
        structured_pages = len(response.pages)
        structured_blocks = sum(len(page.blocks) for page in response.pages)
        structured_errors = sum(len(page.errors or []) for page in response.pages)
        
        print(f"✅ SUCCESS! Structured output completed in {structured_time:.2f}s")
        print(f"   • Pages: {structured_pages}")
        print(f"   • Blocks: {structured_blocks}")
        print(f"   • Errors: {structured_errors}")
        
        # Save structured output results
        
        structured_file = output_dir / "structured_output.json"
        with open(structured_file, "w", encoding="utf-8") as f:
            f.write(response.model_dump_json(indent=2))
        
        print(f"   • Saved to: {structured_file}")
        
    except Exception as e:
        print(f"❌ FAILED: {e}")
        structured_success = False
        structured_time = time.time() - start_time
        structured_pages = 0
        structured_blocks = 0
        structured_errors = 1
    
    # Test 2: Old JSON Repair Approach (for comparison)
    print("\n🔧 Testing OLD JSON Repair Approach...")
    start_time = time.time()
    
    try:
        from gazette_scraper.extractor.gemini_client import GeminiClient
        
        old_client = GeminiClient()
        old_response = old_client.extract_pages(
            pdf_path=test_pdf,
            source_filename=test_pdf.name,
            batch_size=1
        )
        
        repair_time = time.time() - start_time
        repair_success = True
        repair_pages = len(old_response.pages)
        repair_blocks = sum(len(page.blocks) for page in old_response.pages)
        repair_errors = sum(len(page.errors or []) for page in old_response.pages)
        
        print(f"✅ SUCCESS! JSON repair completed in {repair_time:.2f}s")
        print(f"   • Pages: {repair_pages}")
        print(f"   • Blocks: {repair_blocks}")
        print(f"   • Errors: {repair_errors}")
        
        # Save repair output results
        repair_file = output_dir / "json_repair_output.json"
        with open(repair_file, "w", encoding="utf-8") as f:
            f.write(old_response.model_dump_json(indent=2))
        
        print(f"   • Saved to: {repair_file}")
        
    except Exception as e:
        print(f"❌ FAILED: {e}")
        repair_success = False
        repair_time = time.time() - start_time
        repair_pages = 0
        repair_blocks = 0
        repair_errors = 1
    
    # Comparison Results
    print("\n📊 COMPARISON RESULTS")
    print("=" * 60)
    
    print(f"{'Metric':<20} {'Structured':<15} {'JSON Repair':<15} {'Improvement':<15}")
    print("-" * 65)
    
    # Success rate
    structured_rate = "100%" if structured_success else "0%"
    repair_rate = "100%" if repair_success else "0%"
    print(f"{'Success Rate':<20} {structured_rate:<15} {repair_rate:<15} {'✅' if structured_success >= repair_success else '❌':<15}")
    
    # Processing time
    time_improvement = f"{((repair_time - structured_time) / repair_time * 100):.1f}%" if repair_time > 0 else "N/A"
    print(f"{'Processing Time':<20} {structured_time:.2f}s{'':<7} {repair_time:.2f}s{'':<7} {time_improvement:<15}")
    
    # Content extraction
    if structured_success and repair_success:
        block_diff = structured_blocks - repair_blocks
        block_improvement = f"{block_diff:+d} blocks" if block_diff != 0 else "Same"
        print(f"{'Blocks Extracted':<20} {structured_blocks:<15} {repair_blocks:<15} {block_improvement:<15}")
        
        error_diff = repair_errors - structured_errors
        error_improvement = f"{error_diff:+d} errors" if error_diff != 0 else "Same"
        print(f"{'Error Count':<20} {structured_errors:<15} {repair_errors:<15} {error_improvement:<15}")
    
    # Summary
    print("\n🎯 SUMMARY")
    print("=" * 60)
    
    if structured_success:
        print("✅ Structured Output: WORKING")
        if repair_success:
            if structured_time < repair_time:
                improvement = (repair_time - structured_time) / repair_time * 100
                print(f"🚀 Performance: {improvement:.1f}% faster than JSON repair")
            if structured_errors <= repair_errors:
                print("🎯 Quality: Equal or better error handling")
            print("💰 Cost: Eliminates retry loops and JSON repair overhead")
        print("🔧 Recommendation: DEPLOY structured output approach")
    else:
        print("❌ Structured Output: FAILED")
        print("🔧 Recommendation: Debug and fix structured output issues")
    
    print(f"\n📁 Results saved to: {output_dir}/")


if __name__ == "__main__":
    test_structured_output()
