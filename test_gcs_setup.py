#!/usr/bin/env python3
"""Test script for GCS setup validation."""

import os
import sys
from pathlib import Path

# Add the project to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from gazette_scraper.config import load_config
from gazette_scraper.pipeline import Gazette<PERSON><PERSON>eline


def test_config_loading():
    """Test that config loads properly with GCS settings."""
    print("🔧 Testing configuration loading...")

    try:
        config = load_config()
        print("✅ Config loaded successfully")
        print(f"   GCS Bucket: {config.gcs_bucket}")
        print(f"   GCS Project ID: {config.gcs_project_id}")
        print(f"   GCS Prefix: {config.gcs_prefix}")
        print(f"   Crawler Depth: {config.crawler_depth}")
        return config
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        return None


def test_gcs_initialization(config):
    """Test GCS storage initialization."""
    print("\n🚀 Testing GCS storage initialization...")

    try:
        pipeline = GazettePipeline(config)

        if pipeline.gcs_storage:
            print(f"✅ GCS storage initialized for bucket: {config.gcs_bucket}")
            # Test that we can access the bucket property without authentication for now
            try:
                bucket_name = pipeline.gcs_storage.bucket_name
                print(f"   Bucket name: {bucket_name}")
                print("⚠️  Note: Actual GCS access requires authentication")
                return True
            except Exception as e:
                print(f"⚠️  GCS client requires authentication: {e}")
                return True  # This is expected without credentials
        else:
            print("ℹ️  GCS storage not configured (gcs_bucket not set)")
            return True

    except ImportError as e:
        print(f"⚠️  GCS dependencies not installed: {e}")
        print("   Install with: pip install google-cloud-storage")
        return False
    except Exception as e:
        print(f"❌ GCS initialization failed: {e}")
        return False


def test_environment_variables():
    """Test environment variable configuration."""
    print("\n🌍 Testing environment variable configuration...")

    env_vars = [
        "GOOGLE_APPLICATION_CREDENTIALS",
        "GAZETTE_GCS_BUCKET",
        "GAZETTE_GCS_PROJECT_ID",
        "GAZETTE_GCS_PREFIX",
        "GAZETTE_CRAWLER_DEPTH"
    ]

    for var in env_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value}")
        else:
            print(f"ℹ️  {var}: not set")

    # Test if GOOGLE_APPLICATION_CREDENTIALS points to valid file
    cred_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
    if cred_path:
        if Path(cred_path).exists():
            print(f"✅ Credentials file exists: {cred_path}")
        else:
            print(f"❌ Credentials file not found: {cred_path}")


def main():
    """Run all tests."""
    print("🧪 Testing GCS Setup Configuration\n")
    print("=" * 50)

    # Test 1: Config loading
    config = test_config_loading()
    if not config:
        return 1

    # Test 2: Environment variables
    test_environment_variables()

    # Test 3: GCS initialization
    gcs_ok = test_gcs_initialization(config)

    print("\n" + "=" * 50)
    print("📋 Summary:")
    print("   Configuration: ✅ OK")
    print(f"   GCS Setup: {'✅ OK' if gcs_ok else '❌ NEEDS ATTENTION'}")

    if config.gcs_bucket:
        print(f"\n🎯 Ready for testing with bucket: {config.gcs_bucket}")
        print("Next steps:")
        print("1. Run: gcloud auth login")
        print("2. Run: export GOOGLE_APPLICATION_CREDENTIALS=~/keys/minijust-scraper.json")
        print("3. Test with: GAZETTE_CRAWLER_DEPTH=2 python -m gazette_scraper fetch --since 2025 --dry-run")
    else:
        print("\n⚠️  GCS bucket not configured. Update config.toml [gcs] section.")

    return 0 if gcs_ok else 1


if __name__ == "__main__":
    sys.exit(main())
