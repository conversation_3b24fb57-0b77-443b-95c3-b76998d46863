name: CI
on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-test:
    name: Lint • Types • Tests (matrix)
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest]
        python-version: ['3.11', '3.12']

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
          cache: 'pip'

      - name: Install Poetry
        run: |
          python -m pip install --upgrade pip
          pip install poetry

      - name: Configure Poetry (no venv)
        run: poetry config virtualenvs.create false

      - name: Install deps
        run: |
          poetry install --no-interaction

      - name: Ruff (lint & format check)
        run: |
          poetry run ruff check . --select TCH
          poetry run ruff check .
          poetry run ruff format --check .

      - name: Mypy (strict)
        run: poetry run mypy --strict .

      - name: Run tests with coverage (no network)
        env:
          PYTEST_ADDOPTS: "-q"
        run: |
          poetry run pytest --cov=gazette_scraper --cov-report=xml
          python - <<'PY'
import sys, xml.etree.ElementTree as ET
tree = ET.parse('coverage.xml')
line_rate = float(tree.getroot().attrib['line-rate'])
threshold = 0.70
print(f"Coverage: {line_rate*100:.2f}% (threshold {threshold*100:.0f}%)")
sys.exit(0 if line_rate >= threshold else 1)
PY

      - name: Build artifacts
        if: success()
        run: poetry build

      - name: Upload dist
        if: success()
        uses: actions/upload-artifact@v4
        with:
          name: dist-${{ matrix.os }}-${{ matrix.python-version }}
          path: dist/*

  release:
    name: Release on tag
    if: startsWith(github.ref, 'refs/tags/')
    needs: build-test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          pattern: dist-*
          merge-multiple: true
          path: dist/
      
      - uses: softprops/action-gh-release@v2
        with:
          generate_release_notes: true
          files: dist/*