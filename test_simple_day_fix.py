#!/usr/bin/env python3
"""
Simple test to validate day discovery fix without re-expansion.
"""

import asyncio
import logging
from gazette_scraper.caselaw.navigator import Case<PERSON>awNavigator
from gazette_scraper.caselaw.models import CourtNode, NodeType
from gazette_scraper.config import load_caselaw_config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_simple_day_fix():
    """Simple test of day discovery fix."""
    
    logger.info("🚀 Testing Day Discovery Fix - Simple Version")
    logger.info("🎯 Expected: November(16), September(17), February(27)")
    
    config = load_caselaw_config()
    navigator = CaseLawNavigator(config)
    
    try:
        await navigator.start()
        
        context = navigator.contexts[0]
        page = await context.new_page()
        
        url = f"{config.base_url}{config.caselaw_path}"
        await page.goto(url, wait_until="networkidle", timeout=config.page_load_timeout)
        await asyncio.sleep(config.navigation_delay)
        
        # Find and expand International Tribunal for Rwanda
        court_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
        
        for element in court_elements:
            text = await element.text_content()
            if text and "International Tribunal for Rwanda" in text:
                await element.click()
                await asyncio.sleep(config.navigation_delay)
                break
        
        # Find and expand 2009
        year_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
        
        for element in year_elements:
            text = await element.text_content()
            if text and "2009" in text:
                await element.click()
                await asyncio.sleep(config.navigation_delay)
                break
        
        # Create year node and discover months
        year_node = CourtNode(
            court_name="International Tribunal for Rwanda",
            year=2009,
            node_type=NodeType.YEAR,
            full_path="International Tribunal for Rwanda/2009",
            document_count=0
        )
        
        discovered_months = await navigator._discover_months(page, year_node)
        logger.info(f"📅 Discovered {len(discovered_months)} months")
        
        month_names = ["", "January", "February", "March", "April", "May", "June",
                      "July", "August", "September", "October", "November", "December"]
        
        # Test each month's day discovery ONE TIME ONLY
        total_days_found = 0
        results = {}
        
        for month in discovered_months:
            month_name = month_names[month.month]
            logger.info(f"\n🔍 Testing {month_name}...")
            
            # Expand the month
            month_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
            for element in month_elements:
                text = await element.text_content()
                if text and month_name.lower() in text.lower() and "(" in text:
                    await element.click()
                    await asyncio.sleep(config.navigation_delay)
                    break
            
            # Discover days for this month
            discovered_days = await navigator._discover_days(page, month)
            
            day_numbers = [day.day for day in discovered_days]
            day_numbers.sort()
            
            results[month_name] = day_numbers
            total_days_found += len(discovered_days)
            
            logger.info(f"  📄 {month_name}: {len(discovered_days)} days - {day_numbers}")
        
        # Validation against expected structure
        logger.info("\n" + "="*60)
        logger.info("🎯 VALIDATION RESULTS")
        logger.info("="*60)
        
        expected = {
            "November": [16],
            "September": [17], 
            "February": [27]
        }
        
        all_passed = True
        
        for month_name, expected_days in expected.items():
            actual_days = results.get(month_name, [])
            
            if actual_days == expected_days:
                logger.info(f"✅ {month_name}: PASS - Found days {actual_days}")
            else:
                logger.error(f"❌ {month_name}: FAIL - Expected {expected_days}, got {actual_days}")
                all_passed = False
        
        logger.info(f"\n📊 SUMMARY:")
        logger.info(f"  Total days found: {total_days_found}")
        logger.info(f"  Expected total: 3")
        
        if all_passed and total_days_found == 3:
            logger.info("\n🎉 SUCCESS! Day discovery fix is working correctly!")
            logger.info("✅ No phantom days detected!")
            logger.info("✅ All expected days found!")
        else:
            logger.error(f"\n❌ Issues detected:")
            if total_days_found != 3:
                logger.error(f"  - Wrong total count: {total_days_found} vs 3 expected")
            if not all_passed:
                logger.error(f"  - Wrong days found for some months")
        
    except Exception as e:
        logger.error(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await navigator.close()

if __name__ == "__main__":
    asyncio.run(test_simple_day_fix())
