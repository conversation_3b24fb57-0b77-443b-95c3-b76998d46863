# Amategeko Case-Law Downloader

A comprehensive scraper for downloading court decisions and judgments from Rwanda's Amategeko legal portal. This tool navigates the JavaScript-driven hierarchical tree structure to discover and download all available case-law PDFs while maintaining state for resumable operations.

## Features

### 🌳 **Dynamic Tree Navigation**
- Navigates the complex court → year → month → day hierarchy
- Handles JavaScript-rendered content using Playwright
- Automatically discovers and expands tree nodes
- Adapts to structure changes on the website

### 📄 **Intelligent PDF Extraction**
- Multiple strategies for finding PDF download URLs
- Handles JavaScript-triggered downloads
- Extracts case metadata (case numbers, parties, keywords)
- Validates PDF content and structure

### 💾 **Persistent State Management**
- Tracks navigation progress at every level
- Records document counts vs. actual discoveries
- Maintains download status for each case
- Enables seamless resume after interruptions

### 🔄 **Robust Error Handling**
- Automatic retry with exponential backoff
- Browser crash recovery
- Rate limiting protection
- Circuit breaker pattern for failing operations

### 🎯 **Comprehensive Validation**
- File integrity verification (size, hash, readability)
- Navigation completeness checking
- Coverage analysis across courts and time periods
- Duplicate detection

## Quick Start

### 1. Installation

```bash
# Install with Playwright support
pip install -e .
playwright install chromium
```

### 2. Basic Usage

```bash
# Start case-law scraping
gazette-scraper caselaw scrape

# Resume from previous state
gazette-scraper caselaw scrape --resume

# Dry run to discover structure without downloading
gazette-scraper caselaw scrape --dry-run

# Check current status
gazette-scraper caselaw status

# Validate downloaded files
gazette-scraper caselaw validate
```

### 3. Configuration

Create `caselaw_config.toml`:

```toml
# Output directory
output_dir = "./caselaw_data"

[caselaw]
# Browser settings
browser_timeout = 30000
headless = true
max_concurrent_downloads = 2

# Navigation settings
navigation_delay = 2.0
max_retry_attempts = 3

# Error handling
screenshot_on_error = true
debug_mode = false
```

## Architecture

### Core Components

```
gazette_scraper/caselaw/
├── models.py          # Data models (CaseLawFile, CourtNode, etc.)
├── navigator.py       # Playwright-based tree navigation
├── downloader.py      # PDF extraction and download
├── pipeline.py        # Main orchestration pipeline
├── state.py           # SQLite-based state management
├── error_handler.py   # Retry and recovery mechanisms
└── validator.py       # Validation and monitoring
```

### Data Flow

1. **Tree Discovery**: Navigate court hierarchy, expand nodes, record structure
2. **Case Discovery**: Extract case lists from day nodes
3. **PDF Extraction**: Navigate to case pages, find download URLs
4. **Download & Validation**: Download PDFs, verify integrity, save locally
5. **State Persistence**: Record progress for resumability

## Command Reference

### Main Commands

```bash
# Scraping
gazette-scraper caselaw scrape [OPTIONS]
  --output-dir PATH          Output directory
  --max-concurrent INT       Concurrent downloads (default: 2)
  --browser-timeout INT      Browser timeout in ms (default: 30000)
  --headless/--no-headless   Run browser in headless mode
  --debug                    Enable debug mode with screenshots
  --resume                   Resume from previous state
  --dry-run                  Discover without downloading

# Status and monitoring
gazette-scraper caselaw status             # Show progress
gazette-scraper caselaw validate           # Validate files
gazette-scraper caselaw list-cases         # List discovered cases

# State management
gazette-scraper caselaw export-state FILE  # Export state backup
gazette-scraper caselaw import-state FILE  # Import state backup

# Utilities
gazette-scraper caselaw generate-manifest  # Generate manifest file
```

### Filtering Options

```bash
# Filter by court
gazette-scraper caselaw list-cases --court "Supreme Court"

# Filter by year
gazette-scraper caselaw list-cases --year 2023

# Limit results
gazette-scraper caselaw list-cases --limit 50
```

## Configuration Options

### Browser Settings
- `browser_timeout`: Browser timeout in milliseconds
- `page_load_timeout`: Page load timeout
- `element_timeout`: Element wait timeout
- `headless`: Run browser in headless mode
- `debug_mode`: Enable debug features

### Navigation
- `navigation_delay`: Delay between clicks (seconds)
- `max_retry_attempts`: Maximum retry attempts
- `retry_delay`: Base retry delay (seconds)
- `click_retry_attempts`: Click-specific retries

### Concurrency
- `max_browser_contexts`: Maximum browser contexts
- `max_concurrent_downloads`: Concurrent downloads

### Error Handling
- `screenshot_on_error`: Take screenshots on errors
- `verify_pdf_content`: Validate PDF structure
- `min_pdf_size`: Minimum valid PDF size (bytes)

### File Management
- `output_dir`: Base output directory
- `screenshots_dir`: Screenshot storage
- `resume_from_state`: Enable state resumption
- `state_save_interval`: State save frequency

## State Management

The scraper uses SQLite to track:

### Navigation State
```sql
-- Court tree nodes with expansion status
court_nodes (
    full_path TEXT PRIMARY KEY,
    court_name, year, month, day,
    node_type, status, document_count,
    actual_documents, click_selector,
    last_attempt, attempt_count
)
```

### Case Files
```sql
-- Individual case files with download status
case_files (
    id TEXT PRIMARY KEY,
    case_title, court_name, year, month, day,
    download_url, case_page_url, navigation_path,
    sha256, local_path, size_bytes,
    discovered_at, downloaded_at, status
)
```

### Resume Logic
1. Check for incomplete navigation paths
2. Resume from first pending/failed node
3. Continue case discovery for completed nodes
4. Skip already downloaded cases

## Error Handling

### Retry Mechanisms
- **Exponential Backoff**: Increasing delays between retries
- **Circuit Breaker**: Prevent cascading failures
- **Browser Recovery**: Restart crashed browser contexts
- **Rate Limiting**: Automatic throttling and recovery

### Error Types
- `NavigationError`: Tree navigation failures
- `ElementNotFoundError`: Missing DOM elements
- `DownloadError`: PDF download failures
- `ValidationError`: File integrity issues
- `BrowserCrashError`: Browser/context crashes

### Recovery Strategies
```python
# Automatic retry with recovery
@resilient_operation(error_handler, recovery_manager, max_attempts=3)
async def navigate_to_node(page, node):
    # Navigation logic with automatic error handling
```

## Validation & Monitoring

### File Validation
- **Integrity**: Size and hash verification
- **Structure**: PDF readability and page count
- **Content**: Text extraction capability

### Completeness Monitoring
- **Navigation**: Node discovery vs. expansion
- **Documents**: Expected vs. actual counts
- **Downloads**: Success rates and failures
- **Coverage**: Court and temporal coverage

### Real-time Stats
```bash
gazette-scraper caselaw status
```
```
Navigation Progress
├── Nodes: 1,234 discovered, 1,200 expanded, 34 pending
├── Cases: 5,678 discovered, 5,400 downloaded, 278 pending
└── Rate: 45 downloads/hour, ~6h remaining
```

## Docker Deployment

### Build with Playwright Support
```dockerfile
# Dockerfile includes Playwright dependencies
FROM python:3.12-slim

# Install Playwright browser dependencies
RUN apt-get update && apt-get install -y \
    libnss3 libatk-bridge2.0-0 libdrm2 \
    libxkbcommon0 libgtk-3-0 libgbm1

# Install Playwright browsers
RUN python -m playwright install chromium
```

### Run in Container
```bash
# Build image
docker build -t caselaw-scraper .

# Run scraping
docker run -v ./data:/app/caselaw_data caselaw-scraper \
    gazette-scraper caselaw scrape --headless
```

## Integration Examples

### Custom Pipeline
```python
from gazette_scraper.caselaw import CaseLawPipeline, CaseLawConfig

# Configure scraper
config = CaseLawConfig(
    output_dir=Path("./legal_docs"),
    max_concurrent_downloads=3,
    headless=True
)

# Run pipeline
async with CaseLawPipeline(config) as pipeline:
    result = await pipeline.run_full_scrape()
    print(f"Downloaded {result.downloaded} cases")
```

### State Monitoring
```python
from gazette_scraper.caselaw import CaseLawState, CaseLawMonitor

state = CaseLawState("./caselaw_state.db")
monitor = CaseLawMonitor(config, state)

# Get real-time statistics
stats = monitor.get_real_time_stats()
print(f"Progress: {stats['progress']['download_rate']:.1f}%")
```

### Custom Validation
```python
from gazette_scraper.caselaw import CaseLawValidator

validator = CaseLawValidator(config, state)

# Generate completeness report
report = validator.generate_completeness_report()

# Check file integrity
for case in downloaded_cases:
    validation = validator.validate_file_integrity(case)
    if validation['errors']:
        print(f"Issues with {case.filename}: {validation['errors']}")
```

## Troubleshooting

### Common Issues

**Browser Crashes**
```bash
# Enable debug mode for screenshots
gazette-scraper caselaw scrape --debug --no-headless

# Check browser dependencies
playwright install --help
```

**Navigation Failures**
```bash
# Check network connectivity
curl -I https://amategeko.gov.rw/laws/judgement/2

# Increase timeouts
export CASELAW_BROWSER_TIMEOUT=60000
export CASELAW_NAVIGATION_DELAY=5.0
```

**Download Issues**
```bash
# Validate existing files
gazette-scraper caselaw validate

# Check disk space
df -h ./caselaw_data

# Resume interrupted downloads
gazette-scraper caselaw scrape --resume
```

**Path Format Issues (Fixed in v1.1.0)**
If you encounter "0 cases found" when you know documents exist:
```bash
# This issue was resolved in v1.1.0 - ensure you're using the latest version
# The CLI now correctly formats paths as: "Court/Year/MonthName/Day_N"
# instead of the old format: "Court/Year/MonthNumber/DayNumber"

# Test specific path to verify fix
poetry run python -m gazette_scraper.caselaw.cli scrape \
    --court "Supreme Court" --year 2025 --month 2 --day 12 --dry-run

# Expected output should show: "Target: Supreme Court/2025/February/Day_12"
# and successfully find all available cases
```

## Changelog

### v1.1.0 (2025-09-22)
**🐛 Critical Fix: Path Format Mismatch**
- **Fixed**: CLI path format mismatch that caused "0 cases found" when documents existed
- **Root Cause**: CLI created paths as `"Court/Year/MonthNumber/DayNumber"` while navigator expected `"Court/Year/MonthName/Day_N"`
- **Solution**: Updated CLI to use correct format: `"Supreme Court/2025/February/Day_12"`
- **Impact**: All targeted scraping operations now work correctly
- **Testing**: Verified with "Supreme Court/2025/Feb/12" - successfully found and downloaded all 7 documents

**🔧 Technical Changes**:
- Updated `gazette_scraper/caselaw/cli.py` with `month_num_to_name()` helper function
- Fixed target node creation logic in CLI to match navigator expectations
- Enhanced debugging output for case extraction process
- Added comprehensive path format validation

**📋 Validation**:
- ✅ Successfully navigates to correct day pages
- ✅ Extracts all available cases using CSS selector `div.col-md-8 a`
- ✅ Downloads PDFs with proper blob URL handling
- ✅ Maintains hierarchical directory structure

### Debug Mode

Enable comprehensive debugging:
```bash
export CASELAW_DEBUG_MODE=true
export CASELAW_SCREENSHOT_ON_ERROR=true
gazette-scraper caselaw scrape --debug --no-headless
```

This will:
- Take screenshots on errors → `./screenshots/`
- Show browser window for manual inspection
- Enable verbose logging
- Save detailed error traces

### State Recovery

If state becomes corrupted:
```bash
# Export current state
gazette-scraper caselaw export-state backup.json

# Reset and import clean state
rm caselaw_state.db
gazette-scraper caselaw import-state backup.json

# Or start fresh (loses progress)
rm caselaw_state.db
gazette-scraper caselaw scrape
```

## Performance Tuning

### Concurrency Settings
```toml
[caselaw]
max_browser_contexts = 3      # More contexts = more memory
max_concurrent_downloads = 2   # Balance speed vs. server load
```

### Resource Usage
- **Memory**: ~200MB per browser context
- **Disk**: ~1-5MB per case PDF
- **Network**: Respects 1-2 second delays between requests

### Optimization Tips
1. **Use SSD storage** for better database performance
2. **Increase timeouts** on slower connections
3. **Monitor memory usage** with multiple contexts
4. **Use resume functionality** for large datasets

## Contributing

The case-law scraper is designed to be modular and extensible:

### Adding New Courts
Extend `CourtLevel` enum and update navigation selectors in `navigator.py`.

### Custom Metadata Extraction
Extend `CaseMetadata` model and modify `_extract_case_metadata()` in `downloader.py`.

### Additional Validation
Add custom validators to `validator.py` and integrate with the pipeline.

## License

Internal use only. This tool is designed for legal research and archival purposes in compliance with website terms of service.

---

For support or questions, check the main repository documentation or create an issue with detailed logs and configuration.