#!/usr/bin/env python3
"""
Deep inspection of case page to find PDF extraction method.
"""

import asyncio
import logging
import json
from pathlib import Path
from gazette_scraper.caselaw.navigator import CaseLawNavigator
from gazette_scraper.caselaw.models import CaseLawConfig

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def deep_inspect_case_page():
    """Deep inspection of case page structure."""
    
    # Create a basic config
    config = CaseLawConfig(
        base_url="https://amategeko.gov.rw",
        caselaw_path="/laws/judgement/2",
        navigation_delay=1.0,
        max_retries=3
    )
    
    # Create navigator
    navigator = CaseLawNavigator(config)
    
    try:
        # Initialize browser
        await navigator.start()
        
        context = navigator.contexts[0]
        page = await context.new_page()
        
        # Navigate to case page
        case_url = "https://amategeko.gov.rw/view/doc/8893/9229"
        logger.info(f"Navigating to: {case_url}")
        
        await page.goto(case_url, wait_until="networkidle")
        await asyncio.sleep(3)
        
        # 1. Check page source for PDF URLs
        logger.info("\n=== Checking Page Source for PDF URLs ===")
        content = await page.content()
        
        # Look for PDF-related patterns in the HTML
        pdf_patterns = [
            ".pdf",
            "download",
            "blob:",
            "data:application/pdf",
            "viewer.js",
            "pdf.js",
            "pdfjs",
            "application/pdf"
        ]
        
        for pattern in pdf_patterns:
            if pattern.lower() in content.lower():
                logger.info(f"Found pattern '{pattern}' in page source")
                # Find the context around the pattern
                content_lower = content.lower()
                index = content_lower.find(pattern.lower())
                if index != -1:
                    start = max(0, index - 100)
                    end = min(len(content), index + 100)
                    context_snippet = content[start:end]
                    logger.info(f"  Context: ...{context_snippet}...")
        
        # 2. Check network requests
        logger.info("\n=== Monitoring Network Requests ===")
        requests = []
        
        def handle_request(request):
            if any(pattern in request.url.lower() for pattern in ['.pdf', 'download', 'blob']):
                requests.append({
                    'url': request.url,
                    'method': request.method,
                    'headers': dict(request.headers),
                    'resource_type': request.resource_type
                })
                logger.info(f"PDF-related request: {request.method} {request.url}")
        
        page.on("request", handle_request)
        
        # Reload page to capture all requests
        await page.reload(wait_until="networkidle")
        await asyncio.sleep(2)
        
        # 3. Check for JavaScript variables and functions
        logger.info("\n=== Checking JavaScript Context ===")
        
        # Look for common PDF viewer variables
        js_checks = [
            "window.PDFViewerApplication",
            "window.pdfjs",
            "window.viewer",
            "document.querySelector('embed')",
            "document.querySelector('object')",
            "document.querySelector('iframe')",
            "document.querySelector('[src*=\"pdf\"]')",
            "document.querySelector('[data-src*=\"pdf\"]')",
            "document.querySelector('[href*=\"pdf\"]')",
        ]
        
        for js_check in js_checks:
            try:
                result = await page.evaluate(f"({js_check})")
                if result:
                    logger.info(f"JS Check '{js_check}': {result}")
            except Exception as e:
                logger.debug(f"JS Check '{js_check}' failed: {e}")
        
        # 4. Look for specific elements with more detailed inspection
        logger.info("\n=== Detailed Element Inspection ===")
        
        # Get all elements with potential PDF-related attributes
        elements_to_check = await page.query_selector_all("*[src], *[data-src], *[href], *[data-href], *[data-url], *[onclick]")
        
        logger.info(f"Found {len(elements_to_check)} elements with potential PDF attributes")
        
        pdf_candidates = []
        for i, element in enumerate(elements_to_check[:50]):  # Check first 50
            try:
                tag_name = await element.evaluate("el => el.tagName")
                attributes = await element.evaluate("""
                    el => {
                        const attrs = {};
                        for (let attr of el.attributes) {
                            attrs[attr.name] = attr.value;
                        }
                        return attrs;
                    }
                """)
                
                # Check if any attribute contains PDF-related content
                has_pdf_content = False
                for attr_name, attr_value in attributes.items():
                    if attr_value and any(pattern in attr_value.lower() for pattern in ['.pdf', 'download', 'blob:', 'viewer']):
                        has_pdf_content = True
                        break
                
                if has_pdf_content:
                    pdf_candidates.append({
                        'tag': tag_name,
                        'attributes': attributes
                    })
                    logger.info(f"PDF Candidate {len(pdf_candidates)}: {tag_name} - {attributes}")
                    
            except Exception as e:
                continue
        
        # 5. Check for React/Vue components or data attributes
        logger.info("\n=== Checking for Modern Web App Patterns ===")
        
        # Look for data attributes that might contain PDF info
        data_attrs = await page.evaluate("""
            () => {
                const elements = document.querySelectorAll('*');
                const dataAttrs = [];
                elements.forEach(el => {
                    for (let attr of el.attributes) {
                        if (attr.name.startsWith('data-') && 
                            (attr.value.includes('pdf') || attr.value.includes('download') || attr.value.includes('doc'))) {
                            dataAttrs.push({
                                tag: el.tagName,
                                attr: attr.name,
                                value: attr.value
                            });
                        }
                    }
                });
                return dataAttrs;
            }
        """)
        
        if data_attrs:
            logger.info("Found data attributes with PDF/download content:")
            for attr in data_attrs:
                logger.info(f"  {attr['tag']} {attr['attr']}='{attr['value']}'")
        
        # 6. Check for form elements that might trigger downloads
        logger.info("\n=== Checking Forms ===")
        forms = await page.query_selector_all("form")
        for i, form in enumerate(forms):
            try:
                action = await form.get_attribute("action")
                method = await form.get_attribute("method")
                logger.info(f"Form {i+1}: action='{action}' method='{method}'")
                
                # Check form inputs
                inputs = await form.query_selector_all("input")
                for j, input_elem in enumerate(inputs):
                    input_type = await input_elem.get_attribute("type")
                    input_name = await input_elem.get_attribute("name")
                    input_value = await input_elem.get_attribute("value")
                    logger.info(f"  Input {j+1}: type='{input_type}' name='{input_name}' value='{input_value}'")
                    
            except Exception as e:
                continue
        
        # 7. Save detailed page info
        logger.info("\n=== Saving Detailed Page Info ===")
        
        # Save full HTML
        html_file = Path("debug_case_page.html")
        html_file.write_text(content, encoding='utf-8')
        logger.info(f"Saved full HTML to: {html_file}")
        
        # Save network requests
        if requests:
            requests_file = Path("debug_network_requests.json")
            requests_file.write_text(json.dumps(requests, indent=2), encoding='utf-8')
            logger.info(f"Saved network requests to: {requests_file}")
        
        # Save PDF candidates
        if pdf_candidates:
            candidates_file = Path("debug_pdf_candidates.json")
            candidates_file.write_text(json.dumps(pdf_candidates, indent=2), encoding='utf-8')
            logger.info(f"Saved PDF candidates to: {candidates_file}")
        
        await page.close()
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Clean up
        await navigator.close()

if __name__ == "__main__":
    asyncio.run(deep_inspect_case_page())
