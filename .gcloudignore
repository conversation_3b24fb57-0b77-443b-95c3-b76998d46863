# Google Cloud Build Ignore File
# Specifies which files should be ignored during Cloud Build uploads
# This optimizes build performance by excluding unnecessary files

# =============================================================================
# Development and Testing Files
# =============================================================================

# Test files and directories
tests/
test_*/
*_test.py
test.py
conftest.py
pytest.ini
coverage.xml

# Development logs
logs/
*.log

# Development database
scrape_state.db
*.db
*.sqlite

# =============================================================================
# Development Dependencies and Caches
# =============================================================================

# Python cache files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# Poetry
poetry.lock.backup

# MyPy
.mypy_cache/
.dmypy.json
dmypy.json

# =============================================================================
# IDE and Editor Files
# =============================================================================

# VS Code
.vscode/

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-*

# Vim
*~
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# Documentation and Examples
# =============================================================================

# Documentation files (build them separately if needed)
docs/
*.md
*.rst
LICENSE
CHANGELOG.md
CONTRIBUTING.md
CODEOWNERS

# Example files
examples/

# =============================================================================
# Data and State Files
# =============================================================================

# Downloaded data (should be in GCS, not container)
data/
state-smoke/
debug_folder.html
debug_folder.py

# Processing outputs and temp files
test_extraction*/
test_header_footer_results/
test_integrated_results/
test_structured_comparison/
*.pdf
*.csv

# =============================================================================
# Environment and Configuration
# =============================================================================

# Environment files (use Secret Manager instead)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Local configuration backups
*.toml.backup
config.local.toml

# =============================================================================
# Git and Version Control
# =============================================================================

# Git files
.git/
.gitignore
.gitattributes
.gitmodules

# =============================================================================
# System Files
# =============================================================================

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Linux
*~

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp

# =============================================================================
# Temporary Files
# =============================================================================

# Temporary files
temp/
tmp/
*.tmp
*.temp
*.swp
*.swo

# =============================================================================
# Additional Project-Specific Files
# =============================================================================

# Scripts for local development only
setup_gcs.sh
validate_gcs.py
test_*.py

# Validation and testing scripts
*_test.py
test_connectivity.py
test_extractor.py
test_real_site.py

# Cloud-specific configs (managed separately)
gcp-key.json
service-account-key.json

# Prompts and schemas (include in build)
# prompts/
# schemas/

# Development artifacts
mypy.ini
mypy_errors.log
.ruff_cache/