#!/usr/bin/env python3
"""
Test script to run the Rwanda case-law scraper locally and validate at least 10 full paths.
This will verify that the fixes work correctly before deploying to Cloud Run.
"""

import asyncio
import logging
from pathlib import Path
from typing import List

from gazette_scraper.caselaw.navigator import CaseLawNavigator
from gazette_scraper.caselaw.models import CourtNode, NodeType
from gazette_scraper.config import load_caselaw_config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_scraper_discovery():
    """Test the scraper discovery logic and validate at least 10 full paths."""
    
    # Load configuration - this returns a CaseLawConfig
    config = load_caselaw_config()

    # Initialize navigator
    navigator = CaseLawNavigator(config)
    
    try:
        # Initialize browser
        await navigator.start()
        logger.info("✅ Browser initialized successfully")
        
        # Discover court structure
        logger.info("🔍 Starting court structure discovery...")
        court_nodes = await navigator.discover_court_tree()

        # Collect all valid paths from the flat list of nodes
        valid_paths = []

        # Group nodes by type for easier processing
        courts = [node for node in court_nodes if node.node_type == NodeType.COURT]
        years = [node for node in court_nodes if node.node_type == NodeType.YEAR]
        months = [node for node in court_nodes if node.node_type == NodeType.MONTH]
        days = [node for node in court_nodes if node.node_type == NodeType.DAY]

        logger.info(f"📊 Discovery Results:")
        logger.info(f"  Courts: {len(courts)}")
        logger.info(f"  Years: {len(years)}")
        logger.info(f"  Months: {len(months)}")
        logger.info(f"  Days: {len(days)}")

        # Process day nodes (these are the complete paths we want)
        for day_node in days:
            # Parse the full path to extract components
            path_parts = day_node.full_path.split('/')
            if len(path_parts) >= 4:
                court_name = path_parts[0]
                year = int(path_parts[1])
                month_name = path_parts[2]
                day_str = path_parts[3].replace('Day_', '')
                day = int(day_str)

                # Convert month name to number
                month_map = {
                    'January': 1, 'February': 2, 'March': 3, 'April': 4,
                    'May': 5, 'June': 6, 'July': 7, 'August': 8,
                    'September': 9, 'October': 10, 'November': 11, 'December': 12
                }
                month_num = month_map.get(month_name, 0)

                if month_num > 0:
                    full_path = f"{court_name}/{year}/{month_num}/{day}"
                    valid_paths.append({
                        'path': full_path,
                        'court': court_name,
                        'year': year,
                        'month': month_num,
                        'day': day,
                        'documents': day_node.document_count
                    })
                    logger.info(f"📄 Valid path: {full_path} ({day_node.document_count} documents)")

                    # Stop after we have enough paths for testing
                    if len(valid_paths) >= 15:
                        break
        
        # Validate results
        logger.info(f"\n🎯 VALIDATION RESULTS:")
        logger.info(f"✅ Total valid paths discovered: {len(valid_paths)}")
        
        if len(valid_paths) >= 10:
            logger.info(f"✅ SUCCESS: Found {len(valid_paths)} valid paths (target: 10+)")
            
            # Show first 10 paths
            logger.info(f"\n📋 First 10 valid paths:")
            for i, path_info in enumerate(valid_paths[:10], 1):
                logger.info(f"  {i:2d}. {path_info['path']} ({path_info['documents']} docs)")
                
                # Validate calendar logic
                if path_info['month'] in [1, 3, 5, 7, 8, 10, 12]:  # 31-day months
                    max_days = 31
                elif path_info['month'] in [4, 6, 9, 11]:  # 30-day months
                    max_days = 30
                else:  # February
                    max_days = 29 if path_info['year'] % 4 == 0 else 28
                
                if path_info['day'] > max_days:
                    logger.error(f"❌ INVALID: Day {path_info['day']} exceeds max {max_days} for month {path_info['month']}")
                    return False
                else:
                    logger.debug(f"✅ Valid: Day {path_info['day']} <= {max_days} for month {path_info['month']}")
            
            # Test actual case extraction for first 3 paths
            logger.info(f"\n🧪 Testing case extraction for first 3 paths...")
            for i, path_info in enumerate(valid_paths[:3], 1):
                try:
                    # Create a day node for testing
                    day_node = CourtNode(
                        court_name=path_info['court'],
                        year=path_info['year'],
                        month=path_info['month'],
                        day=path_info['day'],
                        node_type=NodeType.DAY,
                        full_path=path_info['path'],
                        document_count=path_info['documents'],
                        click_selector="",  # Will be set during navigation
                        url="https://amategeko.gov.rw/laws/judgement/2"
                    )
                    
                    # Test case discovery
                    cases = await navigator.discover_cases_for_day(day_node)
                    logger.info(f"  {i}. {path_info['path']}: Found {len(cases)} cases")
                    
                    if cases:
                        for j, case in enumerate(cases[:2], 1):  # Show first 2 cases
                            logger.info(f"     Case {j}: {case.case_title}")
                    
                except Exception as e:
                    logger.warning(f"  {i}. {path_info['path']}: Error extracting cases - {e}")
            
            return True
            
        else:
            logger.error(f"❌ FAILURE: Only found {len(valid_paths)} valid paths (target: 10+)")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error during testing: {e}")
        return False
        
    finally:
        # Clean up
        await navigator.close()
        logger.info("🧹 Cleanup completed")

async def main():
    """Main test function."""
    logger.info("🚀 Starting Rwanda Case-Law Scraper Local Test")
    logger.info("🎯 Target: Validate at least 10 full court/year/month/day paths")
    
    success = await test_scraper_discovery()
    
    if success:
        logger.info("\n🎉 ✅ ALL TESTS PASSED!")
        logger.info("✅ Scraper logic is working correctly")
        logger.info("✅ Calendar validation is working")
        logger.info("✅ Court structure discovery is accurate")
        logger.info("🚀 Ready for Cloud Run deployment!")
    else:
        logger.error("\n💥 ❌ TESTS FAILED!")
        logger.error("❌ Scraper needs more fixes before deployment")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
