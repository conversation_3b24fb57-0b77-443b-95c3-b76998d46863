#!/bin/bash

# Setup script for GraphRAG + Voyage + MongoDB Atlas integration
# This script demonstrates the complete setup process

set -e

echo "🚀 Setting up GraphRAG + Voyage + MongoDB Atlas integration..."

# Check required environment variables
required_vars=("OPENAI_API_KEY" "VOYAGE_API_KEY" "MONGODB_URI")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ Error: $var environment variable is required"
        echo "Please set it in your .env file or environment"
        exit 1
    fi
done

echo "✅ Environment variables check passed"

# Install dependencies
echo "📦 Installing GraphRAG dependencies..."
poetry install --extras "graphrag evaluation all"

# Create directories
echo "📁 Creating directories..."
mkdir -p ./graphrag_data
mkdir -p ./ailex_rwanda

# Step 1: Build BYOG data from existing gazette JSON files
echo "🔄 Step 1: Building BYOG data from gazette JSON files..."
if [ -d "./data" ] && [ "$(find ./data -name 'gazette.json' | wc -l)" -gt 0 ]; then
    poetry run gazette-scraper graphrag build-byog \
        --input-dir ./data \
        --output-dir ./graphrag_data \
        --tenant-id rwanda_gov \
        --chunk-size 1000 \
        --chunk-overlap 100
    echo "✅ BYOG data generated successfully"
else
    echo "⚠️  No gazette.json files found in ./data directory"
    echo "   Please run the scraper and extractor first:"
    echo "   poetry run gazette-scraper fetch --dry-run"
    echo "   poetry run gazette-scraper extract --pdf path/to/gazette.pdf --out ./data/sample"
    exit 1
fi

# Step 2: Initialize GraphRAG project
echo "🔄 Step 2: Initializing GraphRAG project..."
poetry run gazette-scraper graphrag init-project \
    --project-name ailex_rwanda \
    --byog-dir ./graphrag_data \
    --enable-vector-store

echo "✅ GraphRAG project initialized"

# Step 3: Generate embeddings and store in MongoDB Atlas
echo "🔄 Step 3: Generating Voyage contextual embeddings..."
poetry run gazette-scraper graphrag embed-chunks \
    --text-units-path ./graphrag_data/text_units.parquet \
    --tenant-id rwanda_gov \
    --batch-size 50 \
    --model voyage-context-3 \
    --dimension 1024

echo "✅ Embeddings generated and stored in MongoDB Atlas"

# Step 4: Build GraphRAG index
echo "🔄 Step 4: Building GraphRAG index (communities and reports)..."
poetry run gazette-scraper graphrag build-index \
    --project-root ./ailex_rwanda

echo "✅ GraphRAG index built successfully"

# Step 5: Test queries
echo "🔄 Step 5: Testing query functionality..."

echo "Testing global search..."
poetry run gazette-scraper graphrag query \
    --project-root ./ailex_rwanda \
    --query "What are the main legal frameworks for land use in Rwanda?" \
    --search-type global

echo "Testing local search..."
poetry run gazette-scraper graphrag query \
    --project-root ./ailex_rwanda \
    --query "cooperative registration requirements" \
    --search-type local \
    --language en \
    --max-results 5

echo "Testing DRIFT search..."
poetry run gazette-scraper graphrag query \
    --project-root ./ailex_rwanda \
    --query "Ministry of Justice appointments and regulations" \
    --search-type drift

echo "🎉 GraphRAG setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Review the generated files in ./ailex_rwanda/"
echo "2. Check MongoDB Atlas for stored embeddings"
echo "3. Try more queries with different search types"
echo "4. Integrate into your application using the GraphRAGQueryService"
echo ""
echo "📖 Usage examples:"
echo "  # Global search for high-level questions"
echo "  poetry run gazette-scraper graphrag query --search-type global --query 'legal trends in 2024'"
echo ""
echo "  # Local search for specific facts"
echo "  poetry run gazette-scraper graphrag query --search-type local --query 'land registration process'"
echo ""
echo "  # DRIFT search for comprehensive analysis"
echo "  poetry run gazette-scraper graphrag query --search-type drift --query 'cooperative law changes'"
