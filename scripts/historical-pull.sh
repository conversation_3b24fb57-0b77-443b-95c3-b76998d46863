#!/bin/bash

# historical-pull.sh - Run complete historical gazette scraping with GCS integration
# Works on macOS and CI runners

set -e  # Exit on any error

echo "🚀 Starting Historical Gazette Pull"
echo "=================================="

# Configuration
OUTPUT_DIR="./state-prod"
SINCE_YEAR=2004
CREDENTIALS_PATH="$HOME/keys/minijust-scraper.json"

# 1. Export GOOGLE_APPLICATION_CREDENTIALS if key is present
if [[ -f "$CREDENTIALS_PATH" ]]; then
    export GOOGLE_APPLICATION_CREDENTIALS="$CREDENTIALS_PATH"
    echo "✅ Google Cloud credentials found and exported"
    echo "   Path: $CREDENTIALS_PATH"
else
    echo "⚠️  Google Cloud credentials not found at $CREDENTIALS_PATH"
    echo "   GCS uploads will be skipped"
fi

# Create output directory
mkdir -p "$OUTPUT_DIR"
echo "📁 Output directory: $OUTPUT_DIR"

# Print configuration
echo ""
echo "📋 Configuration:"
echo "   Since year: $SINCE_YEAR"
echo "   Crawler depth: 2 (month-level)"
echo "   Output directory: $OUTPUT_DIR"
echo "   GCS enabled: $([[ -n "$GOOGLE_APPLICATION_CREDENTIALS" ]] && echo "Yes" || echo "No")"
echo ""

# 2. Run gazette-scraper with depth 2, since 2004, out ./state-prod
echo "🔄 Starting scraper..."
start_time=$(date +%s)

# Run the scraper and capture output
if command -v poetry &> /dev/null && [[ -f "pyproject.toml" ]]; then
    # Use poetry if available and in project directory
    GAZETTE_CRAWLER_DEPTH=2 poetry run python -m gazette_scraper fetch \
        --since "$SINCE_YEAR" \
        --out "$OUTPUT_DIR" \
        2>&1 | tee "$OUTPUT_DIR/scraper-output.log"
    exit_code=${PIPESTATUS[0]}
else
    # Fallback to direct python execution 
    GAZETTE_CRAWLER_DEPTH=2 python -m gazette_scraper fetch \
        --since "$SINCE_YEAR" \
        --out "$OUTPUT_DIR" \
        2>&1 | tee "$OUTPUT_DIR/scraper-output.log"
    exit_code=${PIPESTATUS[0]}
fi

end_time=$(date +%s)
duration=$((end_time - start_time))

echo ""
echo "⏱️  Scraping completed in $(date -u -d @${duration} +%H:%M:%S 2>/dev/null || date -r $duration +%H:%M:%S)"

# 3. Parse output and print summary (# discovered / # downloaded / # errors)
echo ""
echo "📊 HISTORICAL PULL SUMMARY"
echo "========================="

# Extract metrics from the log file
log_file="$OUTPUT_DIR/scraper-output.log"

if [[ -f "$log_file" ]]; then
    # Parse the summary section from the logs
    discovered=$(grep "Total discovered:" "$log_file" | tail -1 | sed 's/.*Total discovered: \([0-9][0-9]*\).*/\1/' || echo "0")
    downloaded=$(grep "Downloaded:" "$log_file" | tail -1 | sed 's/.*Downloaded: \([0-9][0-9]*\).*/\1/' || echo "0")
    errors=$(grep "Errors:" "$log_file" | tail -1 | sed 's/.*Errors: \([0-9][0-9]*\).*/\1/' || echo "0")
    skipped=$(grep "Skipped:" "$log_file" | tail -1 | sed 's/.*Skipped: \([0-9][0-9]*\).*/\1/' || echo "0")
    
    echo "📈 Files discovered: $discovered"
    echo "⬇️  Files downloaded: $downloaded"
    echo "⏭️  Files skipped: $skipped"
    echo "❌ Errors: $errors"
    
    # Calculate success rate
    if [[ $discovered -gt 0 ]]; then
        success_rate=$(( (downloaded * 100) / discovered ))
        echo "✅ Success rate: ${success_rate}%"
    fi
    
    # Check for manifest file
    manifest_file="$OUTPUT_DIR/gazettes.csv"
    if [[ -f "$manifest_file" ]]; then
        manifest_lines=$(wc -l < "$manifest_file")
        echo "📄 Manifest entries: $((manifest_lines - 1))"  # Subtract header
        echo "📄 Manifest file: $manifest_file"
    fi
    
    # Check for database state
    if [[ -f "$OUTPUT_DIR/../scrape_state.db" ]] || [[ -f "./scrape_state.db" ]]; then
        echo "💾 State database: Updated"
    fi
    
else
    echo "⚠️  Could not parse scraper output for detailed metrics"
fi

# Final status
echo ""
if [[ $exit_code -eq 0 ]]; then
    echo "🎉 Historical pull completed successfully!"
    
    # Provide next steps
    echo ""
    echo "📋 Next steps:"
    echo "   • Review files in: $OUTPUT_DIR"
    echo "   • Check manifest: $OUTPUT_DIR/gazettes.csv"
    if [[ -n "$GOOGLE_APPLICATION_CREDENTIALS" ]]; then
        echo "   • Verify GCS uploads: gs://rwandan_laws/gazette_pdfs/"
    fi
    echo "   • Monitor logs: $OUTPUT_DIR/scraper-output.log"
    
else
    echo "❌ Historical pull failed with exit code: $exit_code"
    echo "   Check the log file for details: $OUTPUT_DIR/scraper-output.log"
    exit $exit_code
fi