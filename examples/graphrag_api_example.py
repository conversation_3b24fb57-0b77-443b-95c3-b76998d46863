"""Example usage of GraphRAG API for Rwanda Gazette analysis."""

import asyncio
import os
from pathlib import Path

from gazette_scraper.graphrag import <PERSON><PERSON><PERSON><PERSON>ap<PERSON>, VoyageEmbedder, GraphRAGQueryService
from gazette_scraper.graphrag.config import create_graphrag_project


async def main():
    """Demonstrate GraphRAG API usage."""
    
    # Configuration
    input_dir = Path("./data")  # Directory with gazette.json files
    byog_output_dir = Path("./graphrag_data")
    project_name = "ailex_rwanda_demo"
    tenant_id = "rwanda_gov"
    
    print("🚀 GraphRAG API Demo for Rwanda Gazette Analysis")
    print("=" * 60)
    
    # Step 1: Build BYOG data
    print("\n📊 Step 1: Building BYOG data from gazette JSON files...")
    
    mapper = BYOGMapper(chunk_size=800, chunk_overlap=100)
    byog_data = mapper.process_gazette_files(
        input_dir=input_dir,
        output_dir=byog_output_dir,
        tenant_id=tenant_id
    )
    
    print(f"✅ Generated BYOG data:")
    print(f"   • {len(byog_data.text_units)} text units")
    print(f"   • {len(byog_data.entities)} entities")
    print(f"   • {len(byog_data.relationships)} relationships")
    
    # Step 2: Initialize GraphRAG project
    print("\n🏗️  Step 2: Setting up GraphRAG project...")
    
    config = create_graphrag_project(
        project_name=project_name,
        enable_vector_store=True
    )
    
    # Copy BYOG files to GraphRAG project
    config.copy_byog_files(byog_output_dir)
    print(f"✅ GraphRAG project created at {config.project_root}")
    
    # Step 3: Generate embeddings
    print("\n🧠 Step 3: Generating Voyage contextual embeddings...")
    
    embedder = VoyageEmbedder(
        model="voyage-context-3",
        output_dimension=1024
    )
    
    text_units_path = byog_output_dir / "text_units.parquet"
    total_embedded = embedder.embed_text_units(
        text_units_path=str(text_units_path),
        tenant_id=tenant_id,
        batch_size=50
    )
    
    print(f"✅ Generated {total_embedded} embeddings in MongoDB Atlas")
    
    # Step 4: Initialize query service
    print("\n🔍 Step 4: Setting up query service...")
    
    query_service = GraphRAGQueryService(
        graphrag_project_root=config.project_root,
        voyage_embedder=embedder,
        tenant_id=tenant_id
    )
    
    # Build GraphRAG index
    print("Building GraphRAG communities and reports...")
    build_result = await query_service.build_index()
    
    if build_result["status"] == "success":
        print("✅ GraphRAG index built successfully")
    else:
        print(f"❌ Index build failed: {build_result['message']}")
        return
    
    # Step 5: Demonstrate different query types
    print("\n🎯 Step 5: Demonstrating query capabilities...")
    
    # Global search example
    print("\n--- Global Search Example ---")
    global_result = await query_service.global_search(
        query="What are the main legal frameworks governing land use and cooperative registration in Rwanda?",
        community_level=2
    )
    
    print("Query: Land use and cooperative legal frameworks")
    print("Response:", global_result["response"][:500] + "..." if len(global_result["response"]) > 500 else global_result["response"])
    
    # Local search example
    print("\n--- Local Search Example ---")
    local_result = await query_service.local_search(
        query="cooperative registration requirements and procedures",
        language="en",
        max_results=5
    )
    
    print("Query: Cooperative registration requirements")
    print("Response:", local_result["response"][:500] + "..." if len(local_result["response"]) > 500 else local_result["response"])
    print(f"Found {len(local_result.get('context', []))} relevant chunks")
    
    # DRIFT search example
    print("\n--- DRIFT Search Example ---")
    drift_result = await query_service.drift_search(
        query="Ministry of Justice organizational changes and new regulations",
        community_level=2
    )
    
    print("Query: Ministry of Justice changes")
    print("Response:", drift_result["response"][:500] + "..." if len(drift_result["response"]) > 500 else drift_result["response"])
    
    # Step 6: Advanced filtering examples
    print("\n🔧 Step 6: Advanced filtering examples...")
    
    # Language-specific search
    print("\n--- French Language Search ---")
    french_result = await query_service.local_search(
        query="procédures d'enregistrement des coopératives",
        language="fr",
        max_results=3
    )
    
    print("Query (French): Cooperative registration procedures")
    print("Response:", french_result["response"][:300] + "..." if len(french_result["response"]) > 300 else french_result["response"])
    
    # Date-filtered search
    print("\n--- Recent Documents Search ---")
    recent_result = await query_service.local_search(
        query="new appointments and ministerial changes",
        gazette_date_filter={"$gte": "2023-01-01"},
        max_results=5
    )
    
    print("Query: Recent appointments (2023+)")
    print("Response:", recent_result["response"][:300] + "..." if len(recent_result["response"]) > 300 else recent_result["response"])
    
    # Step 7: Entity analysis
    print("\n📈 Step 7: Entity analysis...")
    
    # Find entities related to a topic
    entities = query_service._find_relevant_entities("cooperative", max_entities=5)
    
    print("Entities related to 'cooperative':")
    for entity in entities:
        print(f"  • {entity['title']} ({entity['type']}): {entity['description'][:100]}...")
    
    # Cleanup
    embedder.close()
    
    print("\n🎉 Demo completed successfully!")
    print("\n📋 Summary:")
    print(f"   • Processed gazette documents from {input_dir}")
    print(f"   • Generated {total_embedded} contextual embeddings")
    print(f"   • Built GraphRAG knowledge graph with communities")
    print(f"   • Demonstrated global, local, and DRIFT search")
    print(f"   • Showed multilingual and filtered search capabilities")
    print(f"   • Project files saved to {config.project_root}")


def demonstrate_programmatic_usage():
    """Show how to use the API programmatically in applications."""
    
    print("\n💻 Programmatic Usage Example:")
    print("=" * 40)
    
    code_example = '''
# In your application code:
from gazette_scraper.graphrag import VoyageEmbedder, GraphRAGQueryService
from pathlib import Path

async def search_legal_documents(query: str, search_type: str = "local"):
    """Search Rwanda legal documents."""
    
    # Initialize services
    embedder = VoyageEmbedder()
    query_service = GraphRAGQueryService(
        graphrag_project_root=Path("./ailex_rwanda"),
        voyage_embedder=embedder,
        tenant_id="rwanda_gov"
    )
    
    # Perform search
    if search_type == "global":
        result = await query_service.global_search(query)
    elif search_type == "local":
        result = await query_service.local_search(query, language="en")
    elif search_type == "drift":
        result = await query_service.drift_search(query)
    
    # Cleanup
    embedder.close()
    
    return result

# Usage examples:
# result = await search_legal_documents("land registration process")
# result = await search_legal_documents("legal trends", search_type="global")
# result = await search_legal_documents("ministry changes", search_type="drift")
'''
    
    print(code_example)


if __name__ == "__main__":
    # Check environment variables
    required_vars = ["OPENAI_API_KEY", "VOYAGE_API_KEY", "MONGODB_URI"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("Please set them in your .env file or environment")
        exit(1)
    
    # Run the demo
    asyncio.run(main())
    
    # Show programmatic usage
    demonstrate_programmatic_usage()
