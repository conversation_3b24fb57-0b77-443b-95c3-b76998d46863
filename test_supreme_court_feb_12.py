#!/usr/bin/env python3
"""
Test script for the caselaw scraper targeting Supreme Court/2025/Feb/12
"""

import asyncio
import logging
import sys
from pathlib import Path
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

async def test_supreme_court_feb_12():
    """Test the caselaw scraper for Supreme Court/2025/Feb/12"""
    
    try:
        from gazette_scraper.caselaw.models import CaseLawConfig, CourtNode, NodeType
        from gazette_scraper.caselaw.pipeline import CaseLawPipeline
        
        print("🎯 Testing Case-Law Scraper for Supreme Court/2025/Feb/12")
        print("=" * 60)
        
        # Create test output directory
        test_output_dir = Path("./test_supreme_court_feb_12_output")
        test_output_dir.mkdir(exist_ok=True)
        
        # Configure the scraper
        config = CaseLawConfig(
            output_dir=test_output_dir,
            browser_timeout=45000,  # Increased timeout for testing
            page_load_timeout=20000,
            element_timeout=15000,
            headless=False,  # Set to False to see what's happening
            max_concurrent_downloads=1,  # Conservative for testing
            debug_mode=True,  # Enable debug mode
            screenshot_on_error=True,
            max_retry_attempts=3,
            navigation_delay=3.0,  # Slower for testing
            click_retry_attempts=5,
            verify_pdf_content=True,
            min_pdf_size=1024,
        )
        
        print(f"📁 Output directory: {config.output_dir}")
        print(f"🌐 Browser: {'Headless' if config.headless else 'Visible'}")
        print(f"🔍 Debug mode: {config.debug_mode}")
        print(f"📸 Screenshots on error: {config.screenshot_on_error}")
        
        # Create target node for Supreme Court/2025/Feb/12
        target_node = CourtNode(
            court_name="Supreme Court",
            year=2025,
            month=2,  # February
            day=12,
            node_type=NodeType.DAY,
            full_path="Supreme Court/2025/2/12",  # Note: month as number
            document_count=0
        )
        
        print(f"\n🎯 Target path: {target_node.full_path}")
        print(f"📅 Date: February 12, 2025")
        print(f"🏛️ Court: {target_node.court_name}")
        
        # Run the scraper
        start_time = datetime.now()
        
        async with CaseLawPipeline(config) as pipeline:
            print("\n🚀 Starting targeted scraping...")
            
            # First, let's try to discover the structure to see what's available
            print("\n🔍 Step 1: Discovering court structure...")
            court_nodes = await pipeline.discover_tree_structure(resume=False)
            
            # Find Supreme Court node
            supreme_court_node = None
            for node in court_nodes:
                if "Supreme Court" in node.court_name:
                    supreme_court_node = node
                    break
            
            if supreme_court_node:
                print(f"✅ Found Supreme Court node: {supreme_court_node.court_name} ({supreme_court_node.document_count} docs)")
            else:
                print("❌ Supreme Court node not found in discovered structure")
                print("Available courts:")
                for node in court_nodes[:5]:  # Show first 5
                    print(f"   - {node.court_name} ({node.document_count} docs)")
                return
            
            # Now try to scrape the specific target
            print(f"\n🎯 Step 2: Scraping target node: {target_node.full_path}")
            result = await pipeline.scrape_node(target_node)
            
            end_time = datetime.now()
            duration = end_time - start_time
            
            print(f"\n📊 SCRAPING RESULTS:")
            print(f"   📋 Total discovered: {result.total_discovered}")
            print(f"   ⬇️  Downloaded: {result.downloaded}")
            print(f"   ⏭️  Skipped: {result.skipped}")
            print(f"   ❌ Errors: {result.errors}")
            print(f"   ⏱️  Duration: {duration}")
            
            if result.errors > 0:
                print(f"\n❌ Error details:")
                for error in result.error_messages:
                    print(f"   - {error}")
            
            # Check what files were downloaded
            print(f"\n📁 Checking downloaded files...")
            downloaded_files = list(test_output_dir.rglob("*.pdf"))
            if downloaded_files:
                print(f"✅ Found {len(downloaded_files)} PDF files:")
                for file_path in downloaded_files:
                    file_size = file_path.stat().st_size
                    print(f"   - {file_path.name} ({file_size:,} bytes)")
            else:
                print("❌ No PDF files found")
            
            # Check state database
            state_db = test_output_dir / "caselaw_state.db"
            if state_db.exists():
                print(f"✅ State database created: {state_db}")
            else:
                print("❌ No state database found")
            
            # Check for screenshots (if any errors occurred)
            screenshots_dir = Path("./screenshots")
            if screenshots_dir.exists():
                screenshots = list(screenshots_dir.glob("*.png"))
                if screenshots:
                    print(f"📸 Found {len(screenshots)} screenshots:")
                    for screenshot in screenshots[-3:]:  # Show last 3
                        print(f"   - {screenshot.name}")
            
            return result
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("\n📦 Make sure dependencies are installed:")
        print("   pip install playwright aiohttp")
        print("   playwright install chromium")
        return None
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Full error details:")
        return None

async def test_cli_approach():
    """Test using the CLI approach"""
    print("\n🖥️  CLI APPROACH TEST")
    print("=" * 40)
    
    print("You can also test using the CLI:")
    print("gazette-scraper caselaw scrape --court 'Supreme Court' --year 2025 --month 2 --day 12 --debug --no-headless")
    
    print("\nOr using the new CLI:")
    print("python -m gazette_scraper.caselaw.cli scrape --court 'Supreme Court' --year 2025 --month 2 --day 12")

def main():
    """Main test function"""
    print("🧪 Case-Law Scraper End-to-End Test")
    print("Target: Supreme Court/2025/Feb/12")
    print("=" * 60)
    
    # Run the async test
    result = asyncio.run(test_supreme_court_feb_12())
    
    if result:
        print("\n✅ Test completed successfully!")
        if result.downloaded > 0:
            print(f"🎉 Successfully downloaded {result.downloaded} case(s)")
        elif result.total_discovered > 0:
            print(f"📋 Discovered {result.total_discovered} case(s) but none downloaded")
        else:
            print("📭 No cases found for the specified date")
    else:
        print("\n❌ Test failed")
        sys.exit(1)
    
    # Show CLI alternatives
    test_cli_approach()

if __name__ == "__main__":
    main()
