#!/usr/bin/env python3
"""Extract the actual court structure from the website."""

import asyncio
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup

async def extract_court_structure():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()
        
        print("🌐 Navigating to the case-law page...")
        await page.goto("https://amategeko.gov.rw/laws/judgement/2", wait_until="networkidle")
        
        # Wait for dynamic content to load
        print("⏳ Waiting for dynamic content...")
        await asyncio.sleep(5)
        
        # Get the HTML content
        html_content = await page.content()
        
        # Parse with BeautifulSoup for better readability
        soup = BeautifulSoup(html_content, 'html.parser')
        
        print("\n🏛️ COURT STRUCTURE FOUND:")
        print("=" * 50)
        
        # Look for the court links specifically
        court_links = soup.find_all('a', href='/laws/judgement/2')
        
        for i, link in enumerate(court_links):
            # Get the text content
            text_content = link.get_text(strip=True)
            
            # Look for court names with numbers in parentheses
            if '(' in text_content and ')' in text_content:
                print(f"{i+1}. {text_content}")
                
                # Get the parent structure to understand the HTML pattern
                parent = link.parent
                if parent:
                    print(f"   Parent tag: {parent.name}")
                    print(f"   Parent classes: {parent.get('class', [])}")
                    
                # Get any icon elements
                icon = link.find('i')
                if icon:
                    print(f"   Icon classes: {icon.get('class', [])}")
                
                print(f"   Full HTML: {str(link)[:200]}...")
                print()
        
        print("\n🔍 ANALYZING SELECTORS:")
        print("=" * 50)
        
        # Test the actual selectors that should work
        test_selectors = [
            'a[href="/laws/judgement/2"]',
            '.fw-bold.text-decoration-none',
            'a:has(.bi-plus-square)',
            'div.my-2 > a',
            '.text-start a',
        ]
        
        for selector in test_selectors:
            try:
                elements = await page.query_selector_all(selector)
                print(f"✅ Selector '{selector}': Found {len(elements)} elements")
                
                # Get text of first few elements
                for i, element in enumerate(elements[:3]):
                    try:
                        text = await element.text_content()
                        if text and text.strip() and ('Court' in text or '(' in text):
                            print(f"   [{i}]: {text.strip()}")
                    except:
                        pass
            except Exception as e:
                print(f"❌ Selector '{selector}': Error - {e}")
        
        print("\n📋 RECOMMENDED SELECTOR:")
        print("=" * 50)
        
        # Test the most specific selector
        court_selector = 'a[href="/laws/judgement/2"]:has(.bi-plus-square)'
        try:
            court_elements = await page.query_selector_all(court_selector)
            print(f"🎯 Best selector: '{court_selector}'")
            print(f"   Found {len(court_elements)} court elements")
            
            for i, element in enumerate(court_elements):
                text = await element.text_content()
                if text and text.strip():
                    print(f"   Court {i+1}: {text.strip()}")
                    
        except Exception as e:
            print(f"❌ Recommended selector failed: {e}")
            
            # Fallback to simpler selector
            fallback_selector = 'a[href="/laws/judgement/2"]'
            try:
                fallback_elements = await page.query_selector_all(fallback_selector)
                print(f"🔄 Fallback selector: '{fallback_selector}'")
                print(f"   Found {len(fallback_elements)} elements")
                
                court_count = 0
                for element in fallback_elements:
                    text = await element.text_content()
                    if text and 'Court' in text and '(' in text:
                        court_count += 1
                        print(f"   Court {court_count}: {text.strip()}")
                        
            except Exception as e2:
                print(f"❌ Fallback selector also failed: {e2}")
        
        await browser.close()
        print("\n✅ Analysis complete!")

if __name__ == "__main__":
    asyncio.run(extract_court_structure())
