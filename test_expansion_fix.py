#!/usr/bin/env python3
"""
Test script to verify the expansion fix for the Rwanda laws scraper.
"""

import asyncio
import logging
from pathlib import Path
from gazette_scraper.caselaw.navigator import CaseLawNavigator
from gazette_scraper.caselaw.models import CaseLawConfig

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_expansion_fix():
    """Test the expansion functionality with the fixed code."""
    
    # Create a basic config
    config = CaseLawConfig(
        base_url="https://amategeko.gov.rw",
        caselaw_path="/laws/judgement/2",
        navigation_delay=1.0,
        max_retries=3
    )
    
    # Create navigator
    navigator = CaseLawNavigator(config)
    
    try:
        # Initialize browser
        await navigator.start()
        
        # Discover the complete court tree (this will test expansion) - limited to first court only
        logger.info("Discovering court tree (limited test)...")

        # Get just the first court to test expansion quickly
        context = navigator.contexts[0]
        page = await context.new_page()

        try:
            await page.goto("https://amategeko.gov.rw/laws/judgement/2")
            await asyncio.sleep(3)

            # Discover courts
            courts = await navigator._discover_courts(page)
            logger.info(f"Found {len(courts)} courts")

            if courts:
                # Test expansion on first court only
                first_court = courts[0]
                logger.info(f"Testing expansion on: {first_court.court_name}")

                success = await navigator._expand_node(page, first_court)
                if success:
                    logger.info(f"✅ Successfully expanded: {first_court.court_name}")

                    # Discover years for this court
                    years = await navigator._discover_years(page, first_court)
                    logger.info(f"Found {len(years)} years for {first_court.court_name}")

                    if years and len(years) > 0:
                        # Test month expansion on first year
                        first_year = years[0]
                        logger.info(f"Testing month expansion on: {first_year.full_path}")

                        success = await navigator._expand_node(page, first_year)
                        if success:
                            logger.info(f"✅ Successfully expanded: {first_year.full_path}")

                            months = await navigator._discover_months(page, first_year)
                            logger.info(f"Found {len(months)} months for {first_year.full_path}")

                            if months and len(months) > 0:
                                # Test day expansion on first month
                                first_month = months[0]
                                logger.info(f"Testing day expansion on: {first_month.full_path}")

                                success = await navigator._expand_node(page, first_month)
                                if success:
                                    logger.info(f"✅ Successfully expanded: {first_month.full_path}")

                                    days = await navigator._discover_days(page, first_month)
                                    logger.info(f"Found {len(days)} days for {first_month.full_path}")

                                    if days and len(days) > 0:
                                        # Test document discovery on first day
                                        first_day = days[0]
                                        logger.info(f"Testing document discovery on: {first_day.full_path}")

                                        # Use the public method for document discovery
                                        documents = await navigator.discover_cases_for_day(first_day)
                                        logger.info(f"Found {len(documents)} documents for {first_day.full_path}")

                                        if documents and len(documents) > 0:
                                            # Test PDF URL extraction on first document
                                            first_doc = documents[0]
                                            logger.info(f"Testing PDF extraction for: {first_doc.case_title}")

                                            pdf_url = await navigator.extract_pdf_download_url(first_doc)
                                            if pdf_url:
                                                logger.info(f"✅ Successfully extracted PDF URL: {pdf_url}")
                                            else:
                                                logger.warning(f"❌ Failed to extract PDF URL for: {first_doc.case_title}")
                                        else:
                                            logger.warning("❌ No documents found for day")

                                        court_nodes = courts + years + months + days
                                    else:
                                        logger.warning("❌ No days found after month expansion")
                                        court_nodes = courts + years + months
                                else:
                                    logger.error(f"❌ Failed to expand month: {first_month.full_path}")
                                    court_nodes = courts + years + months
                            else:
                                logger.warning("❌ No months found after year expansion")
                                court_nodes = courts + years
                        else:
                            logger.error(f"❌ Failed to expand year: {first_year.full_path}")
                            court_nodes = courts + years
                    else:
                        logger.warning("❌ No years found after court expansion")
                        court_nodes = courts
                else:
                    logger.error(f"❌ Failed to expand court: {first_court.court_name}")
                    court_nodes = courts
            else:
                logger.error("❌ No courts discovered")
                court_nodes = []

        finally:
            await page.close()

        logger.info(f"Total nodes discovered: {len(court_nodes)}")

        # Analyze the results
        courts = [node for node in court_nodes if node.node_type.value == "court"]
        years = [node for node in court_nodes if node.node_type.value == "year"]
        months = [node for node in court_nodes if node.node_type.value == "month"]
        days = [node for node in court_nodes if node.node_type.value == "day"]

        logger.info(f"Breakdown: {len(courts)} courts, {len(years)} years, {len(months)} months, {len(days)} days")

        if courts:
            logger.info("✅ Court discovery successful!")
            for court in courts[:3]:  # Show first 3 courts
                logger.info(f"  - {court.court_name}")
        else:
            logger.error("❌ No courts discovered")

        if years:
            logger.info("✅ Year expansion and discovery successful!")
            for year in years[:5]:  # Show first 5 years
                logger.info(f"  - {year.full_path} ({year.document_count} documents)")
        else:
            logger.warning("❌ No years found - expansion may have failed")

        if months:
            logger.info("✅ Month expansion and discovery successful!")
            for month in months[:5]:  # Show first 5 months
                logger.info(f"  - {month.full_path} ({month.document_count} documents)")
        else:
            logger.warning("❌ No months found")

        if days:
            logger.info("✅ Day expansion and discovery successful!")
            for day in days[:5]:  # Show first 5 days
                logger.info(f"  - {day.full_path} ({day.document_count} documents)")
        else:
            logger.warning("❌ No days found")
            
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Clean up
        await navigator.close()

if __name__ == "__main__":
    asyncio.run(test_expansion_fix())
