# Rwanda Gazette Scraper Configuration

# Base URL for the minijust website
base_url = "https://www.minijust.gov.rw"

# Rate limiting (requests per second) - Conservative for production
rate_limit = 0.5

# Maximum number of retries for failed requests
max_retries = 5

# Output directory for downloaded files
output_dir = "./data"

# SQLite database path for state management
db_path = "./scrape_state.db"

# Maximum number of concurrent download threads - Conservative for production
max_threads = 2

# User agent string for requests
user_agent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

# Optional: Start scraping from this year (leave empty for all years)  
since_year = 2004

# Crawler depth: 1=year-level only, 2=month-level traversal
crawler_depth = 2

# Optional: Proxy list for requests (useful for Rwanda IP requirements)
# proxies = [
#     "socks5://proxy1.example.com:1080",
#     "http://proxy2.example.com:8080"
# ]

# Google Cloud Storage configuration
[gcs]
bucket = "rwandan_laws"
project_id = "rwandan-law-bot-440710"
# folder prefix inside the bucket (optional)
prefix = "gazette_pdfs/"

# Supabase configuration
[supabase]
url = "https://esjiwdjofswwwmghrlaa.supabase.co"
service_role_key = "${SUPABASE_SERVICE_ROLE_KEY}"