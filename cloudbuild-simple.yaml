# Simple Cloud Build Configuration for Rwanda Case-Law Scraper
steps:
  # Step 1: Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/caselaw-scraper/rwanda-gazette-scraper:latest'
      - '.'

  # Step 2: Push the container image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/caselaw-scraper/rwanda-gazette-scraper:latest'

  # Step 3: Deploy to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'rwanda-gazette-scraper'
      - '--image'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/caselaw-scraper/rwanda-gazette-scraper:latest'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--service-account'
      - 'rwanda-gazette-scraper@$PROJECT_ID.iam.gserviceaccount.com'
      - '--memory'
      - '4Gi'
      - '--cpu'
      - '2'
      - '--timeout'
      - '3600'
      - '--concurrency'
      - '1'
      - '--min-instances'
      - '0'
      - '--max-instances'
      - '3'
      - '--port'
      - '8080'
      - '--set-env-vars'
      - 'GOOGLE_CLOUD_PROJECT=$PROJECT_ID,GCS_BUCKET=rwandan-caselaws'
      - '--set-secrets'
      - 'SUPABASE_SERVICE_ROLE_KEY=supabase-service-key:latest,GEMINI_API_KEY=gemini-api-key:latest'

timeout: '1200s'
images:
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/caselaw-scraper/rwanda-gazette-scraper:latest'
