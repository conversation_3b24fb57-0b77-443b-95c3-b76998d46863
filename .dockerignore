# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Testing
.coverage
.pytest_cache/
.tox/
htmlcov/
coverage.xml
*.cover
.hypothesis/

# Development
.git/
.gitignore
.github/
.mypy_cache/
.ruff_cache/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs/
*.log

# Documentation (keep README.md for Poetry)
CONTRIBUTING.md
docs/

# Data directories (keep structure but not contents)
data/*
!data/.gitkeep