#!/usr/bin/env python3
"""
Background scraper that runs continuously and processes different courts/years.
Can be run as a daemon process.
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any
import json

from local_scraper_with_gcs import LocalScraperWithGCS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('background_scraper.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class BackgroundScraper:
    """Background scraper that processes multiple targets."""
    
    def __init__(self, gcs_bucket: str, gcs_project_id: str = None):
        self.gcs_bucket = gcs_bucket
        self.gcs_project_id = gcs_project_id
        self.scraper = LocalScraperWithGCS(gcs_bucket, gcs_project_id)
        self.running = False
        self.stats_file = Path("scraper_stats.json")
        self.stats = self._load_stats()
        
    def _load_stats(self) -> Dict[str, Any]:
        """Load scraping statistics from file."""
        if self.stats_file.exists():
            try:
                with open(self.stats_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load stats: {e}")
        
        return {
            "total_runs": 0,
            "successful_runs": 0,
            "failed_runs": 0,
            "total_downloaded": 0,
            "total_uploaded": 0,
            "last_run": None,
            "targets_completed": [],
            "targets_failed": []
        }
    
    def _save_stats(self):
        """Save scraping statistics to file."""
        try:
            with open(self.stats_file, 'w') as f:
                json.dump(self.stats, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Failed to save stats: {e}")
    
    def _update_stats(self, target: str, result: Dict[str, Any]):
        """Update statistics after a scraping run."""
        self.stats["total_runs"] += 1
        self.stats["last_run"] = datetime.now().isoformat()
        
        if result["success"]:
            self.stats["successful_runs"] += 1
            self.stats["total_downloaded"] += result.get("downloaded", 0)
            self.stats["total_uploaded"] += result.get("uploaded", 0)
            
            if target not in self.stats["targets_completed"]:
                self.stats["targets_completed"].append(target)
            
            # Remove from failed list if it was there
            if target in self.stats["targets_failed"]:
                self.stats["targets_failed"].remove(target)
        else:
            self.stats["failed_runs"] += 1
            if target not in self.stats["targets_failed"]:
                self.stats["targets_failed"].append(target)
        
        self._save_stats()
    
    def get_scraping_targets(self) -> List[Dict[str, Any]]:
        """Define the list of scraping targets."""
        targets = []
        
        # Supreme Court - all years from 2020-2025
        for year in range(2020, 2026):
            targets.append({
                "court_name": "Supreme Court (911)",
                "year": year,
                "priority": 1 if year >= 2024 else 2,
                "description": f"Supreme Court {year}"
            })
        
        # High Court - recent years
        for year in range(2022, 2026):
            targets.append({
                "court_name": "High Court (1471)",
                "year": year,
                "priority": 3,
                "description": f"High Court {year}"
            })
        
        # Commercial High Court - recent years
        for year in range(2022, 2026):
            targets.append({
                "court_name": "Commercial High Court (312)",
                "year": year,
                "priority": 4,
                "description": f"Commercial High Court {year}"
            })
        
        # Sort by priority (lower number = higher priority)
        targets.sort(key=lambda x: x["priority"])
        
        return targets
    
    def _get_target_key(self, target: Dict[str, Any]) -> str:
        """Generate a unique key for a target."""
        return f"{target['court_name']}/{target['year']}"
    
    async def process_target(self, target: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single scraping target."""
        target_key = self._get_target_key(target)
        
        logger.info(f"🎯 Processing: {target['description']}")
        
        try:
            result = await self.scraper.scrape_and_upload(
                court_name=target["court_name"],
                year=target["year"]
            )
            
            self._update_stats(target_key, result)
            
            if result["success"]:
                logger.info(f"✅ Completed: {target['description']} - "
                           f"{result['downloaded']} downloaded, {result['uploaded']} uploaded")
            else:
                logger.error(f"❌ Failed: {target['description']} - {result.get('error', 'Unknown error')}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Exception processing {target['description']}: {e}")
            result = {"success": False, "error": str(e)}
            self._update_stats(target_key, result)
            return result
    
    async def run_continuous(self, delay_between_targets: int = 300, max_runs: int = None):
        """Run scraper continuously with delays between targets."""
        self.running = True
        targets = self.get_scraping_targets()
        
        logger.info(f"🚀 Starting background scraper")
        logger.info(f"📋 {len(targets)} targets configured")
        logger.info(f"⏱️  Delay between targets: {delay_between_targets} seconds")
        logger.info(f"🔄 Max runs: {max_runs or 'unlimited'}")
        
        run_count = 0
        
        try:
            while self.running:
                for target in targets:
                    if not self.running:
                        break
                    
                    target_key = self._get_target_key(target)
                    
                    # Skip if already completed successfully
                    if target_key in self.stats["targets_completed"]:
                        logger.debug(f"⏭️  Skipping completed target: {target['description']}")
                        continue
                    
                    # Process target
                    await self.process_target(target)
                    
                    run_count += 1
                    if max_runs and run_count >= max_runs:
                        logger.info(f"🏁 Reached max runs limit: {max_runs}")
                        self.running = False
                        break
                    
                    # Delay between targets
                    if self.running and delay_between_targets > 0:
                        logger.info(f"⏸️  Waiting {delay_between_targets} seconds before next target...")
                        await asyncio.sleep(delay_between_targets)
                
                # If we completed all targets, wait longer before starting over
                if self.running:
                    logger.info(f"🔄 Completed all targets. Waiting 1 hour before restart...")
                    await asyncio.sleep(3600)  # 1 hour
                    
        except KeyboardInterrupt:
            logger.info("🛑 Received interrupt signal")
        except Exception as e:
            logger.error(f"❌ Unexpected error: {e}")
        finally:
            self.running = False
            logger.info("🏁 Background scraper stopped")
    
    def print_stats(self):
        """Print current statistics."""
        print(f"\n📊 SCRAPER STATISTICS:")
        print(f"Total runs: {self.stats['total_runs']}")
        print(f"Successful: {self.stats['successful_runs']}")
        print(f"Failed: {self.stats['failed_runs']}")
        print(f"Total downloaded: {self.stats['total_downloaded']}")
        print(f"Total uploaded: {self.stats['total_uploaded']}")
        print(f"Last run: {self.stats['last_run']}")
        print(f"Completed targets: {len(self.stats['targets_completed'])}")
        print(f"Failed targets: {len(self.stats['targets_failed'])}")
        
        if self.stats['targets_failed']:
            print(f"\n❌ Failed targets:")
            for target in self.stats['targets_failed']:
                print(f"  - {target}")
    
    def stop(self):
        """Stop the background scraper."""
        logger.info("🛑 Stopping background scraper...")
        self.running = False

def signal_handler(signum, frame, scraper):
    """Handle shutdown signals."""
    logger.info(f"Received signal {signum}")
    scraper.stop()

async def main():
    """Main entry point."""
    
    # Configuration
    GCS_BUCKET = "rwandan-caselaws"
    GCS_PROJECT_ID = None  # Will use default from environment
    
    # Create background scraper
    scraper = BackgroundScraper(
        gcs_bucket=GCS_BUCKET,
        gcs_project_id=GCS_PROJECT_ID
    )
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, lambda s, f: signal_handler(s, f, scraper))
    signal.signal(signal.SIGTERM, lambda s, f: signal_handler(s, f, scraper))
    
    # Print initial stats
    scraper.print_stats()
    
    # Run continuously
    await scraper.run_continuous(
        delay_between_targets=60,  # 1 minute between targets
        max_runs=None  # Run indefinitely
    )
    
    # Print final stats
    scraper.print_stats()

if __name__ == "__main__":
    asyncio.run(main())
