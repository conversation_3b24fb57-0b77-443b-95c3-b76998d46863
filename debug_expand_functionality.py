#!/usr/bin/env python3
"""
Debug script to analyze the expand functionality on the Amategeko website.
This will help us understand how to properly expand court nodes.
"""

import asyncio
from playwright.async_api import async_playwright
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def analyze_expand_functionality():
    """Analyze how court expansion works on the website."""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # Non-headless for visual inspection
        page = await browser.new_page()
        
        try:
            # Navigate to the case law page
            url = "https://amategeko.gov.rw/laws/judgement/2"
            logger.info(f"Navigating to: {url}")
            await page.goto(url, wait_until="networkidle")
            
            # Wait for dynamic content to load
            await asyncio.sleep(3)
            
            # Take a screenshot before any interaction
            await page.screenshot(path="debug_before_expand.png", full_page=True)
            
            # Find all court elements with expand icons
            court_selector = 'a[href="/laws/judgement/2"]:has(.bi-plus-square)'
            courts = await page.query_selector_all(court_selector)
            
            logger.info(f"Found {len(courts)} courts with expand icons")
            
            if courts:
                # Let's analyze the first court (Supreme Court)
                first_court = courts[0]
                
                # Get the court name
                court_text = await first_court.text_content()
                logger.info(f"Analyzing court: {court_text}")
                
                # Look for the expand icon within this court element
                expand_icon = await first_court.query_selector('.bi-plus-square')
                if expand_icon:
                    logger.info("Found expand icon (.bi-plus-square) within court element")
                    
                    # Try to click the expand icon
                    logger.info("Clicking expand icon...")
                    await expand_icon.click()
                    
                    # Wait for expansion
                    await asyncio.sleep(2)
                    
                    # Take screenshot after expansion
                    await page.screenshot(path="debug_after_expand.png", full_page=True)
                    
                    # Check if the icon changed to minus
                    minus_icon = await first_court.query_selector('.bi-dash-square')
                    if minus_icon:
                        logger.info("Expansion successful - icon changed to minus (.bi-dash-square)")
                    else:
                        logger.warning("Icon did not change to minus after click")
                    
                    # Look for newly appeared child elements
                    # Check if any new elements appeared after the court
                    await asyncio.sleep(1)
                    
                    # Try to find year elements that might have appeared
                    year_selectors = [
                        'a[href="/laws/judgement/2"]:has(.bi-plus-square)',  # More courts/years with expand
                        'div.my-2',  # Container divs
                        'a.fw-bold.text-decoration-none',  # Bold links
                    ]
                    
                    for selector in year_selectors:
                        elements = await page.query_selector_all(selector)
                        logger.info(f"After expansion, found {len(elements)} elements with selector: {selector}")
                        
                        # Log first few elements
                        for i, elem in enumerate(elements[:5]):
                            text = await elem.text_content()
                            logger.info(f"  Element {i+1}: {text.strip()}")
                    
                else:
                    logger.error("No expand icon found within court element")
                    
                    # Let's check what icons are actually present
                    all_icons = await first_court.query_selector_all('[class*="bi-"]')
                    logger.info(f"Found {len(all_icons)} Bootstrap icons in court element:")
                    for icon in all_icons:
                        class_name = await icon.get_attribute('class')
                        logger.info(f"  Icon class: {class_name}")
            
            # Wait a bit more to see the final state
            await asyncio.sleep(5)
            
            # Take final screenshot
            await page.screenshot(path="debug_final_state.png", full_page=True)
            
        except Exception as e:
            logger.error(f"Error during analysis: {e}")
            await page.screenshot(path="debug_error.png", full_page=True)
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(analyze_expand_functionality())
