#!/usr/bin/env python3
"""
Test script to validate the fixed day discovery logic.
Should eliminate phantom days and only find real ones.
"""

import asyncio
import logging
from gazette_scraper.caselaw.navigator import CaseLawNavigator
from gazette_scraper.caselaw.models import CourtNode, NodeType
from gazette_scraper.config import load_caselaw_config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_day_discovery_fix():
    """Test the fixed day discovery against International Tribunal for Rwanda 2009."""
    
    logger.info("🚀 Testing Fixed Day Discovery")
    logger.info("🎯 Expected: November(16), September(17), February(27) - NO phantom days")
    
    # Load configuration
    config = load_caselaw_config()
    
    # Initialize navigator
    navigator = CaseLawNavigator(config)
    
    try:
        await navigator.start()
        logger.info("✅ Browser initialized successfully")
        
        # Navigate to the page
        context = navigator.contexts[0]
        page = await context.new_page()
        
        url = f"{config.base_url}{config.caselaw_path}"
        await page.goto(url, wait_until="networkidle", timeout=config.page_load_timeout)
        await asyncio.sleep(config.navigation_delay)
        
        # Find and expand International Tribunal for Rwanda
        court_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
        
        tribunal_element = None
        for element in court_elements:
            text = await element.text_content()
            if text and "International Tribunal for Rwanda" in text:
                tribunal_element = element
                break
        
        if not tribunal_element:
            logger.error("❌ International Tribunal for Rwanda not found!")
            return
        
        # Expand International Tribunal for Rwanda
        await tribunal_element.click()
        await asyncio.sleep(config.navigation_delay)
        
        # Find and expand 2009
        year_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
        
        year_2009_element = None
        for element in year_elements:
            text = await element.text_content()
            if text and "2009" in text:
                year_2009_element = element
                break
        
        if not year_2009_element:
            logger.error("❌ 2009 not found!")
            return
        
        # Expand 2009
        await year_2009_element.click()
        await asyncio.sleep(config.navigation_delay)
        
        # Create year node
        year_node = CourtNode(
            court_name="International Tribunal for Rwanda",
            year=2009,
            node_type=NodeType.YEAR,
            full_path="International Tribunal for Rwanda/2009",
            document_count=0
        )
        
        # Discover months
        discovered_months = await navigator._discover_months(page, year_node)
        
        logger.info(f"\n📅 DISCOVERED MONTHS: {len(discovered_months)}")
        month_names = ["", "January", "February", "March", "April", "May", "June",
                      "July", "August", "September", "October", "November", "December"]
        
        # Test each month's day discovery
        for month in discovered_months:
            month_name = month_names[month.month] if month.month <= 12 else f"Month{month.month}"
            logger.info(f"\n🔍 Testing {month_name}...")
            
            # Expand the month
            month_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
            for element in month_elements:
                text = await element.text_content()
                if text and month_name.lower() in text.lower() and "(" in text:
                    await element.click()
                    await asyncio.sleep(config.navigation_delay)
                    break
            
            # Discover days for this month
            discovered_days = await navigator._discover_days(page, month)
            
            logger.info(f"  📄 Days discovered: {len(discovered_days)}")
            for day in discovered_days:
                logger.info(f"    Day {day.day}: {day.document_count} documents")
        
        # Validation
        logger.info("\n" + "="*60)
        logger.info("🎯 VALIDATION RESULTS")
        logger.info("="*60)
        
        expected_structure = {
            11: [16],  # November: day 16
            9: [17],   # September: day 17  
            2: [27]    # February: day 27
        }
        
        all_passed = True
        
        for month in discovered_months:
            month_name = month_names[month.month]
            expected_days = expected_structure.get(month.month, [])
            
            # Re-discover days for validation
            month_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
            for element in month_elements:
                text = await element.text_content()
                if text and month_name.lower() in text.lower() and "(" in text:
                    await element.click()
                    await asyncio.sleep(config.navigation_delay)
                    break
            
            discovered_days = await navigator._discover_days(page, month)
            actual_days = [day.day for day in discovered_days]
            actual_days.sort()
            
            if actual_days == expected_days:
                logger.info(f"✅ {month_name}: PASS - Found days {actual_days}")
            else:
                logger.error(f"❌ {month_name}: FAIL - Expected {expected_days}, got {actual_days}")
                all_passed = False
        
        if all_passed:
            logger.info("\n🎉 ALL TESTS PASSED! Day discovery fix is working correctly!")
            logger.info("✅ No more phantom days!")
        else:
            logger.error("\n❌ Some tests failed. Day discovery still has issues.")
        
    except Exception as e:
        logger.error(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await navigator.close()
        logger.info("🧹 Cleanup completed")

if __name__ == "__main__":
    asyncio.run(test_day_discovery_fix())
