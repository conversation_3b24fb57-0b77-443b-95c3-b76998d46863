#!/usr/bin/env python3
"""
Debug court expansion to understand why it's failing.
"""

import asyncio
import logging
from pathlib import Path

from gazette_scraper.caselaw.pipeline import CaseLawPipeline
from gazette_scraper.caselaw.models import CaseLawConfig

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def debug_court_expansion():
    """Debug court expansion step by step."""
    logger.info("🔍 DEBUGGING COURT EXPANSION")
    
    # Initialize configuration
    config = CaseLawConfig(
        output_dir=Path("debug_court_expansion"),
        headless=False,  # Use non-headless to see what's happening
        debug_mode=True,
        screenshot_on_error=True,
        max_concurrent_downloads=1,
        browser_timeout=60000,
        max_retry_attempts=3
    )

    # Ensure output directory exists
    config.output_dir.mkdir(exist_ok=True)
    
    pipeline = CaseLawPipeline(config)
    
    try:
        await pipeline.start()
        
        # Create a new page for debugging
        context = pipeline.navigator.contexts[0]
        page = await context.new_page()
        
        # Navigate to the case-law page
        url = "https://amategeko.gov.rw/laws/judgement/2"
        logger.info(f"Navigating to: {url}")
        await page.goto(url, wait_until="networkidle")
        await asyncio.sleep(3)
        
        # Take initial screenshot
        await page.screenshot(path="debug_initial.png")
        logger.info("Screenshot saved: debug_initial.png")
        
        # Find all court-related elements
        court_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
        logger.info(f"Found {len(court_elements)} court-related elements")
        
        for i, element in enumerate(court_elements):
            text = await element.text_content()
            logger.info(f"Element {i+1}: '{text}'")
            
            # Check if this looks like a court (has document count in parentheses)
            if "(" in text and ")" in text and any(court in text for court in ["Supreme", "Court", "Appeal", "High", "Commercial"]):
                logger.info(f"Found court element: '{text}'")
                
                # Look for plus icon next to this element
                try:
                    # Try different ways to find the plus icon
                    plus_selectors = [
                        f'a[href="/laws/judgement/2"]:has-text("{text}") + .bi-plus-square',
                        f'a[href="/laws/judgement/2"]:has-text("{text.strip()}") + .bi-plus-square',
                    ]
                    
                    plus_found = False
                    for selector in plus_selectors:
                        try:
                            plus_element = await page.query_selector(selector)
                            if plus_element:
                                logger.info(f"✅ Found plus icon with selector: {selector}")
                                plus_found = True
                                
                                # Try to click it
                                logger.info("Attempting to click plus icon...")
                                await plus_element.click()
                                await asyncio.sleep(3)
                                
                                # Take screenshot after click
                                await page.screenshot(path=f"debug_after_click_{i}.png")
                                logger.info(f"Screenshot after click: debug_after_click_{i}.png")
                                
                                # Check if anything expanded
                                new_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
                                logger.info(f"After click: {len(new_elements)} elements (was {len(court_elements)})")
                                
                                if len(new_elements) > len(court_elements):
                                    logger.info("🎉 SUCCESS! Court expanded!")
                                    return True
                                else:
                                    logger.warning("⚠️ Click didn't seem to expand anything")
                                
                                break
                        except Exception as e:
                            logger.debug(f"Selector failed: {selector} - {e}")
                    
                    if not plus_found:
                        logger.warning(f"❌ No plus icon found for court: '{text}'")
                        
                        # Try to find any plus icons on the page
                        all_plus_icons = await page.query_selector_all('.bi-plus-square')
                        logger.info(f"Total plus icons on page: {len(all_plus_icons)}")
                        
                        # Try to click the court element directly
                        logger.info("Trying to click court element directly...")
                        try:
                            await element.click()
                            await asyncio.sleep(3)
                            
                            # Take screenshot after direct click
                            await page.screenshot(path=f"debug_direct_click_{i}.png")
                            logger.info(f"Screenshot after direct click: debug_direct_click_{i}.png")
                            
                            # Check if anything expanded
                            new_elements = await page.query_selector_all('a[href="/laws/judgement/2"]')
                            logger.info(f"After direct click: {len(new_elements)} elements (was {len(court_elements)})")
                            
                            if len(new_elements) > len(court_elements):
                                logger.info("🎉 SUCCESS! Court expanded with direct click!")
                                return True
                            
                        except Exception as e:
                            logger.error(f"Direct click failed: {e}")
                
                except Exception as e:
                    logger.error(f"Error processing court element: {e}")
                
                # Only test the first court for now
                break
        
        logger.error("❌ Failed to expand any court")
        return False
        
    except Exception as e:
        logger.error(f"❌ CRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await pipeline.close()


if __name__ == "__main__":
    success = asyncio.run(debug_court_expansion())
    if success:
        print("\n🎉 COURT EXPANSION DEBUG SUCCESSFUL!")
    else:
        print("\n❌ COURT EXPANSION DEBUG FAILED!")
    
    exit(0 if success else 1)
