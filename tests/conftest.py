"""Pytest configuration and fixtures."""

import tempfile
from pathlib import Path

import pytest

from gazette_scraper.models import GazetteFile, GazetteFolder


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests."""
    with tempfile.TemporaryDirectory() as tmp_dir:
        yield Path(tmp_dir)


@pytest.fixture
def sample_gazette_file():
    """Create a sample gazette file for testing."""
    return GazetteFile(
        title="Official Gazette No. 1 of 2024",
        filename="gazette_2024_01.pdf",
        issue_title="Official Gazette No. 1 of 2024",
        size_str="1.2 MB",
        download_url="https://minijust.gov.rw/fileadmin/dumpFile?f=123&t=f&token=abc123",
        source_url="https://minijust.gov.rw/fileadmin/official-gazette/2024/01",
        listing_url="https://minijust.gov.rw/fileadmin/official-gazette/2024/01",
        year=2024,
        month=1,
    )


@pytest.fixture
def sample_gazette_folder():
    """Create a sample gazette folder for testing."""
    return GazetteFolder(
        year=2024,
        month=1,
        folder_path="Official Gazette/2024/01",
        folder_url="https://minijust.gov.rw/fileadmin/official-gazette/2024/01",
    )


@pytest.fixture
def sample_html_folder_listing():
    """Sample HTML content for folder listing page."""
    return """
    <html>
    <head><title>Official Gazette Listing</title></head>
    <body>
        <div class="content">
            <ul>
                <li><a href="?tx_filelist_filelist[path]=Official%20Gazette/2024/01">January 2024</a></li>
                <li><a href="?tx_filelist_filelist[path]=Official%20Gazette/2024/02">February 2024</a></li>
                <li><a href="?tx_filelist_filelist[path]=Official%20Gazette/2023/12">December 2023</a></li>
            </ul>
            <div class="pagination">
                <a href="?currentPage=2">Next ›</a>
            </div>
        </div>
    </body>
    </html>
    """


@pytest.fixture
def sample_html_file_listing():
    """Sample HTML content for file listing page."""
    return """
    <html>
    <head><title>Official Gazette Files - January 2024</title></head>
    <body>
        <div class="filelist">
            <table>
                <thead>
                    <tr><th>File</th><th>Size</th><th>Modified</th></tr>
                </thead>
                <tbody>
                    <tr>
                        <td><a href="index.php?eID=dumpFile&f=12345&t=f&token=abcdef123">OG_2024_01_15.pdf</a></td>
                        <td>1.2 MB</td>
                        <td>15/01/2024</td>
                    </tr>
                    <tr>
                        <td><a href="index.php?eID=dumpFile&f=12346&t=f&token=abcdef124">OG_2024_01_30.pdf</a></td>
                        <td>890 KB</td>
                        <td>30/01/2024</td>
                    </tr>
                    <tr>
                        <td><a href="regular-file.html">not_a_pdf.txt</a></td>
                        <td>5 KB</td>
                        <td>31/01/2024</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </body>
    </html>
    """


@pytest.fixture
def mock_requests_response():
    """Mock response object for requests."""

    class MockResponse:
        def __init__(self, text="", status_code=200, headers=None):
            self.text = text
            self.status_code = status_code
            self.headers = headers or {}

        def raise_for_status(self):
            if self.status_code >= 400:
                raise Exception(f"HTTP {self.status_code}")

        def iter_content(self, chunk_size=8192):
            """Mock streaming content."""
            content = b"Mock PDF content here"
            for i in range(0, len(content), chunk_size):
                yield content[i : i + chunk_size]

    return MockResponse
