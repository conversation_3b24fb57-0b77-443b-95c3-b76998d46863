"""Focused tests for pipeline error handling to increase coverage."""

from __future__ import annotations

import tempfile
from datetime import datetime
from pathlib import Path
from unittest.mock import Mock

from gazette_scraper.models import (
    FileItem,
    Folder,
    FolderType,
    ScrapeResult,
    ScrapingConfig,
)
from gazette_scraper.pipeline import GazettePipeline


class TestPipelineErrorHandling:
    """Test error handling paths in pipeline to increase coverage."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.config = ScrapingConfig(
            output_dir=self.temp_dir,
            dry_run=True,
            since_year=2024,
            max_threads=1,
            rate_limit=10.0,
        )

    def teardown_method(self):
        """Clean up test fixtures."""
        import shutil
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)

    def test_robots_txt_disallow_path(self):
        """Test _check_robots_txt method with disallowed paths."""
        pipeline = GazettePipeline(self.config)

        # Mock client response with disallow
        mock_response = Mock()
        mock_response.text = "User-agent: *\nDisallow: /official-gazette\n"
        pipeline.client.get = Mock(return_value=mock_response)

        # Mock state methods to avoid database interactions
        pipeline.state.get_robots_check = Mock(return_value=None)
        pipeline.state.record_robots_check = Mock()

        result = pipeline._check_robots_txt()
        assert result is False

    def test_robots_txt_wildcard_disallow(self):
        """Test _check_robots_txt with wildcard disallow."""
        pipeline = GazettePipeline(self.config)

        # Mock client response with wildcard disallow
        mock_response = Mock()
        mock_response.text = "User-agent: *\nDisallow: /fileadmin/user_upload/*\n"
        pipeline.client.get = Mock(return_value=mock_response)

        # Mock state methods to avoid database interactions
        pipeline.state.get_robots_check = Mock(return_value=None)
        pipeline.state.record_robots_check = Mock()

        result = pipeline._check_robots_txt()
        assert result is False

    def test_robots_txt_allowed(self):
        """Test _check_robots_txt when allowed."""
        pipeline = GazettePipeline(self.config)

        # Mock client response that allows our paths
        mock_response = Mock()
        mock_response.text = "User-agent: *\nDisallow: /other-path\n"
        pipeline.client.get = Mock(return_value=mock_response)

        # Mock state methods to avoid database interactions
        pipeline.state.get_robots_check = Mock(return_value=None)
        pipeline.state.record_robots_check = Mock()

        result = pipeline._check_robots_txt()
        assert result is True

    def test_robots_txt_exception_handling(self):
        """Test _check_robots_txt handles exceptions."""
        pipeline = GazettePipeline(self.config)

        # Mock state to avoid cached result
        pipeline.state.get_robots_check = Mock(return_value=None)

        # Mock client to raise exception
        pipeline.client.get = Mock(side_effect=Exception("Network error"))

        result = pipeline._check_robots_txt()
        assert result is True  # Should default to allowed

    def test_extract_date_invalid_date(self):
        """Test _extract_pub_date_from_filename with invalid dates."""
        pipeline = GazettePipeline(self.config)

        # Test invalid date that causes ValueError
        result = pipeline._extract_pub_date_from_filename("gazette_32-13-2024.pdf", 2024, 1)
        assert result is not None
        assert result.year == 2024
        assert result.month == 1
        assert result.day == 1  # Should fallback to first of month

    def test_extract_date_invalid_year_month(self):
        """Test _extract_pub_date_from_filename with invalid year/month."""
        pipeline = GazettePipeline(self.config)

        # Test with invalid year/month that causes ValueError
        result = pipeline._extract_pub_date_from_filename("test.pdf", 0, 13)
        assert result is None

    def test_convert_file_item_exception(self):
        """Test _convert_file_item handles exceptions."""
        pipeline = GazettePipeline(self.config)

        # Create invalid file item
        invalid_item = FileItem(
            title="",
            href="invalid-url-format",
            listing_url="also-invalid",
        )

        month_folder = Folder(
            name="January 2024",
            href="https://test.com/2024/01",
            folder_type=FolderType.MONTH,
            year=2024,
            month=1,
        )

        result = pipeline._convert_file_item(invalid_item, month_folder)
        assert result is None

    def test_discover_months_error_handling(self):
        """Test discover_months handles client errors."""
        pipeline = GazettePipeline(self.config)

        # Mock client to raise exception
        pipeline.client.get = Mock(side_effect=Exception("HTTP error"))

        year_folder = Folder(
            name="2024",
            href="https://test.com/2024",
            folder_type=FolderType.YEAR,
            year=2024,
        )

        result = pipeline.discover_months(year_folder)
        assert result == []

    def test_discover_files_error_handling(self):
        """Test discover_files handles client errors."""
        pipeline = GazettePipeline(self.config)

        # Mock client to raise exception
        pipeline.client.get = Mock(side_effect=Exception("Connection error"))

        month_folder = Folder(
            name="January 2024",
            href="https://test.com/2024/01",
            folder_type=FolderType.MONTH,
            year=2024,
            month=1,
        )

        result = pipeline.discover_files(month_folder)
        assert result == []

    def test_build_manifest_month_discovery_error(self):
        """Test build_manifest handles month discovery errors."""
        pipeline = GazettePipeline(self.config)

        # Mock discover_years to return a year folder
        year_folder = Folder(
            name="2024",
            href="https://test.com/2024",
            folder_type=FolderType.YEAR,
            year=2024,
        )
        pipeline.discover_years = Mock(return_value=[year_folder])

        # Mock discover_months to raise exception
        pipeline.discover_months = Mock(side_effect=Exception("Month discovery failed"))

        manifest = pipeline.build_manifest(dry_run=True, since_year=None, out_dir=self.temp_dir)

        # Should handle error gracefully
        assert manifest.total_discovered == 0

    def test_build_manifest_file_discovery_error(self):
        """Test build_manifest handles file discovery errors."""
        config_depth_2 = ScrapingConfig(
            output_dir=self.temp_dir,
            dry_run=True,
            crawler_depth=2,
        )
        pipeline = GazettePipeline(config_depth_2)

        # Mock discover_years and discover_months to succeed
        year_folder = Folder(name="2024", href="https://test.com/2024", folder_type=FolderType.YEAR, year=2024)
        month_folder = Folder(name="January", href="https://test.com/2024/01", folder_type=FolderType.MONTH, year=2024, month=1)

        pipeline.discover_years = Mock(return_value=[year_folder])
        pipeline.discover_months = Mock(return_value=[month_folder])

        # Mock discover_files to raise exception
        pipeline.discover_files = Mock(side_effect=Exception("File discovery failed"))

        manifest = pipeline.build_manifest(dry_run=True, since_year=None, out_dir=self.temp_dir)

        # Should handle error gracefully
        assert manifest.total_discovered == 0

    def test_run_general_exception(self):
        """Test run method handles general exceptions."""
        pipeline = GazettePipeline(self.config)

        # Mock _check_robots_txt to raise exception
        pipeline._check_robots_txt = Mock(side_effect=Exception("General error"))

        result = pipeline.run(dry_run=True)

        # Should handle exception gracefully
        assert result.errors > 0
        assert len(result.error_messages) > 0
        assert result.end_time is not None

    def test_log_summary_with_errors_and_duration(self):
        """Test _log_summary includes error messages and handles edge cases."""
        pipeline = GazettePipeline(self.config)

        # Create result with many error messages
        error_messages = [f"Error {i}" for i in range(15)]  # More than 10 to test truncation
        result = ScrapeResult(
            start_time=datetime.now(),
            end_time=datetime.now(),
            total_discovered=10,
            downloaded=5,
            skipped=3,
            errors=len(error_messages),
            error_messages=error_messages,
        )

        # Mock state stats with empty values
        pipeline.state.get_stats = Mock(return_value={
            'total_downloaded': 0,
            'total_discovered': 0,
            'discovered_by_year': {},
            'discovered_by_month': {},
        })

        # Should not raise exception
        pipeline._log_summary(result)

    def test_log_summary_no_end_time(self):
        """Test _log_summary handles missing end_time."""
        pipeline = GazettePipeline(self.config)

        result = ScrapeResult(
            start_time=datetime.now(),
            # end_time is None
            total_discovered=5,
            downloaded=2,
            skipped=1,
            errors=2,
        )

        # Mock state stats
        pipeline.state.get_stats = Mock(return_value={
            'total_downloaded': 100,
            'total_discovered': 150,
            'discovered_by_year': {2024: 50},
            'discovered_by_month': {'2024-01': 25},
        })

        # Should not raise exception even without end_time
        pipeline._log_summary(result)

    def test_write_manifest_csv_with_empty_fields(self):
        """Test _write_manifest_csv handles files with empty/None fields."""
        pipeline = GazettePipeline(self.config)

        from pydantic import HttpUrl

        from gazette_scraper.models import GazetteFile, Manifest

        # Create file with minimal/empty fields
        test_file = GazetteFile(
            title="Test Gazette",
            filename="test.pdf",
            issue_title="Test Issue",
            download_url=HttpUrl("https://example.com/test.pdf"),
            source_url=HttpUrl("https://example.com/test.pdf"),
            listing_url=HttpUrl("https://example.com/listing"),
            year=2024,
            month=1,
            # size_bytes=None, pub_date=None, sha256=None (defaults)
        )

        manifest = Manifest(files=[test_file], total_discovered=1)
        csv_path = self.temp_dir / "test_manifest.csv"

        # Test both dry_run True and False
        pipeline._write_manifest_csv(manifest, csv_path, dry_run=True)

        # Verify CSV was created
        assert csv_path.exists()
        content = csv_path.read_text()
        assert "Test Issue" in content  # issue_title field is what gets written

    def test_gcs_storage_initialization(self):
        """Test GCS storage initialization when bucket is configured."""
        config_with_gcs = ScrapingConfig(
            output_dir=self.temp_dir,
            gcs_bucket="test-bucket",
        )

        pipeline = GazettePipeline(config_with_gcs)

        # Should initialize GCS storage
        assert pipeline.gcs_storage is not None
