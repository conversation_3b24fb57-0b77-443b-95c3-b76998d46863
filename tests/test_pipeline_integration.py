"""Comprehensive pipeline integration tests with multi-page HTML fixtures."""

from __future__ import annotations

import tempfile
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest

from gazette_scraper.models import ScrapingConfig
from gazette_scraper.pipeline import GazettePipeline


@pytest.fixture
def multi_page_year_html():
    """Multi-page year listing HTML with pagination."""
    return """
    <html>
    <head><title>Official Gazette - Year Listing</title></head>
    <body>
        <div class="content">
            <ul>
                <li><a href="?tx_filelist_filelist[path]=Official%20Gazette/2024">2024</a></li>
                <li><a href="?tx_filelist_filelist[path]=Official%20Gazette/2023">2023</a></li>
                <li><a href="?tx_filelist_filelist[path]=Official%20Gazette/2022">2022</a></li>
            </ul>
            <div class="pagination">
                <a href="?currentPage=2">Next ›</a>
            </div>
        </div>
    </body>
    </html>
    """


@pytest.fixture
def multi_page_month_html():
    """Multi-page month listing HTML."""
    return """
    <html>
    <head><title>Official Gazette - 2024</title></head>
    <body>
        <div class="content">
            <ul>
                <li><a href="?tx_filelist_filelist[path]=Official%20Gazette/2024/01">January</a></li>
                <li><a href="?tx_filelist_filelist[path]=Official%20Gazette/2024/02">February</a></li>
                <li><a href="?tx_filelist_filelist[path]=Official%20Gazette/2024/03">March</a></li>
                <li><a href="?tx_filelist_filelist[path]=Official%20Gazette/2024/04">April</a></li>
            </ul>
            <div class="pagination">
                <span>Page 1 of 3</span>
                <a href="?currentPage=2">Next ›</a>
            </div>
        </div>
    </body>
    </html>
    """


@pytest.fixture
def multi_page_file_html():
    """Multi-page file listing HTML."""
    return """
    <html>
    <head><title>Official Gazette Files - January 2024</title></head>
    <body>
        <div class="filelist">
            <table>
                <thead>
                    <tr><th>File</th><th>Size</th><th>Modified</th></tr>
                </thead>
                <tbody>
                    <tr>
                        <td><a href="index.php?eID=dumpFile&f=12345&t=f&token=abc1">OG_2024_01_15.pdf</a></td>
                        <td>1.2 MB</td>
                        <td>15/01/2024</td>
                    </tr>
                    <tr>
                        <td><a href="index.php?eID=dumpFile&f=12346&t=f&token=abc2">OG_2024_01_30.pdf</a></td>
                        <td>890 KB</td>
                        <td>30/01/2024</td>
                    </tr>
                    <tr>
                        <td><a href="index.php?eID=dumpFile&f=12347&t=f&token=abc3">OG_2024_01_31.pdf</a></td>
                        <td>2.1 MB</td>
                        <td>31/01/2024</td>
                    </tr>
                </tbody>
            </table>
            <div class="pagination">
                <a href="?currentPage=2">2</a>
                <a href="?currentPage=3">3</a>
                <a href="?currentPage=2">Next ›</a>
            </div>
        </div>
    </body>
    </html>
    """


class TestPipelineIntegration:
    """Test end-to-end pipeline functionality with realistic fixtures."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())

    def teardown_method(self):
        """Clean up test fixtures."""
        import shutil

        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)

    def test_pipeline_basic_initialization(self):
        """Test basic pipeline initialization and configuration."""
        config = ScrapingConfig(
            output_dir=self.temp_dir,
            dry_run=True,
            since_year=2024,
            max_threads=1,
            rate_limit=10.0,
        )

        pipeline = GazettePipeline(config)

        # Verify pipeline is properly configured
        assert pipeline.config.dry_run is True
        assert pipeline.config.output_dir == self.temp_dir
        assert pipeline.config.since_year == 2024
        assert pipeline.config.max_threads == 1

    def test_html_fixture_parsing(self, multi_page_file_html):
        """Test that HTML fixtures can be parsed correctly."""
        from gazette_scraper.parser import GazetteParser

        parser = GazetteParser()
        files = parser.parse_file_page(multi_page_file_html, "https://test.com")

        # Should parse the 3 files from the fixture
        assert len(files) >= 3
        assert any("OG_2024_01_15.pdf" in f.title for f in files)
        assert any("OG_2024_01_30.pdf" in f.title for f in files)
        assert any("OG_2024_01_31.pdf" in f.title for f in files)

    def test_pipeline_config_validation(self):
        """Test pipeline configuration validation."""
        # Test valid configuration
        config = ScrapingConfig(
            output_dir=self.temp_dir,
            dry_run=True,
            since_year=2024,
        )
        assert config.output_dir == self.temp_dir
        assert config.dry_run is True
        assert config.since_year == 2024

        # Test configuration with rate limit
        config2 = ScrapingConfig(
            output_dir=self.temp_dir,
            rate_limit=2.0,
        )
        assert config2.rate_limit == 2.0


class TestSupabasePipelineIntegration:
    """Test Supabase integration within the pipeline."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())

    def teardown_method(self):
        """Clean up test fixtures."""
        import shutil

        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)

    @patch("gazette_scraper.pipeline.upsert_file")
    @patch("gazette_scraper.pipeline.log_error")
    def test_successful_download_triggers_supabase_upsert(
        self, mock_log_error, mock_upsert_file, sample_gazette_file
    ):
        """Test that successful download with GCS upload triggers exactly one Supabase upsert."""
        # Setup
        config = ScrapingConfig(
            output_dir=self.temp_dir,
            gcs_bucket="test-bucket",
            supabase_url="https://test.supabase.co",
            supabase_key="test_key",
        )

        pipeline = GazettePipeline(config)
        mock_upsert_file.return_value = True

        # Mock successful file content
        mock_content = b"Mock PDF content"

        # Execute the single file download method
        with patch.object(pipeline.client, "get") as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.content = mock_content
            mock_get.return_value = mock_response

            with patch.object(pipeline.storage, "save_pdf") as mock_save_pdf:
                mock_save_pdf.return_value = "test_sha256"

                # Mock that file is not already downloaded and has no SHA256 conflicts
                with patch.object(pipeline.state, "has_sha256") as mock_has_sha256:
                    mock_has_sha256.return_value = False

                    with patch.object(pipeline.gcs_storage, "upload_file") as mock_gcs_upload:
                        mock_gcs_upload.return_value = True

                        with patch.object(pipeline.state, "record_download"):
                            with patch.object(pipeline.state, "mark_supabase_synced"):
                                with patch.object(pipeline.manifest, "append_file"):
                                    # Execute
                                    result = pipeline._download_single_file(sample_gazette_file)

        # Assert
        assert result is True

        # Verify Supabase upsert was called exactly once with correct parameters
        mock_upsert_file.assert_called_once()
        args, kwargs = mock_upsert_file.call_args
        assert args[0] == sample_gazette_file  # GazetteFile object
        assert "test-bucket/" in args[1]  # GCS path
        assert args[2] == "test_sha256"  # SHA256

        # Verify error logging was not called for successful download
        mock_log_error.assert_not_called()

    @patch("gazette_scraper.pipeline.upsert_file")
    @patch("gazette_scraper.pipeline.log_error")
    def test_failed_download_logs_to_supabase_errors(self, mock_log_error, mock_upsert_file, sample_gazette_file):
        """Test that failed download writes to Supabase download_errors table."""
        # Setup
        config = ScrapingConfig(
            output_dir=self.temp_dir,
            supabase_url="https://test.supabase.co",
            supabase_key="test_key",
        )

        pipeline = GazettePipeline(config)
        mock_log_error.return_value = True

        # Mock HTTP error response
        with patch.object(pipeline.client, "get") as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 404
            mock_get.return_value = mock_response

            # Execute
            result = pipeline._download_single_file(sample_gazette_file)

        # Assert
        assert result is False

        # Verify error was logged to Supabase exactly once
        mock_log_error.assert_called_once()
        args, kwargs = mock_log_error.call_args
        assert args[0] == str(sample_gazette_file.listing_url)  # listing_url
        assert args[1] == str(sample_gazette_file.download_url)  # download_url
        assert "HTTP 404" in args[2]  # error_message
        assert args[3] == 404  # http_status

        # Verify no upsert was attempted for failed download
        mock_upsert_file.assert_not_called()

    @patch("gazette_scraper.pipeline.upsert_file")
    @patch("gazette_scraper.pipeline.log_error")
    def test_gcs_upload_success_but_supabase_upsert_fails(
        self, mock_log_error, mock_upsert_file, sample_gazette_file
    ):
        """Test that GCS upload succeeds but Supabase upsert fails - file still marked as downloaded."""
        # Setup
        config = ScrapingConfig(
            output_dir=self.temp_dir,
            gcs_bucket="test-bucket",
            supabase_url="https://test.supabase.co",
            supabase_key="test_key",
        )

        pipeline = GazettePipeline(config)
        mock_upsert_file.return_value = False  # Supabase upsert fails

        # Mock successful file content
        mock_content = b"Mock PDF content"

        # Execute the single file download method
        with patch.object(pipeline.client, "get") as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.content = mock_content
            mock_get.return_value = mock_response

            with patch.object(pipeline.storage, "save_pdf") as mock_save_pdf:
                mock_save_pdf.return_value = "test_sha256"

                with patch.object(pipeline.state, "has_sha256") as mock_has_sha256:
                    mock_has_sha256.return_value = False

                    with patch.object(pipeline.gcs_storage, "upload_file") as mock_gcs_upload:
                        mock_gcs_upload.return_value = True

                        with patch.object(pipeline.state, "record_download"):
                            with patch.object(pipeline.state, "mark_supabase_synced") as mock_mark_synced:
                                with patch.object(pipeline.manifest, "append_file"):
                                    # Execute
                                    result = pipeline._download_single_file(sample_gazette_file)

                                    # Assert download was successful despite Supabase failure
                                    assert result is True

                                    # Verify Supabase sync was not marked as successful
                                    mock_mark_synced.assert_not_called()

        # Verify GCS upload was attempted
        # mock_gcs_upload.assert_called_once()

        # Verify Supabase upsert was attempted but failed
        mock_upsert_file.assert_called_once()

    @patch("gazette_scraper.pipeline.upsert_file")
    def test_no_supabase_config_skips_upsert(self, mock_upsert_file, sample_gazette_file):
        """Test that missing Supabase configuration skips upsert entirely."""
        # Setup - no Supabase configuration
        config = ScrapingConfig(
            output_dir=self.temp_dir,
            gcs_bucket="test-bucket",  # GCS configured but not Supabase
            supabase_url=None,
            supabase_key=None,
        )

        pipeline = GazettePipeline(config)

        # Mock successful file content and GCS upload
        mock_content = b"Mock PDF content"

        with patch.object(pipeline.client, "get") as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.content = mock_content
            mock_get.return_value = mock_response

            with patch.object(pipeline.storage, "save_pdf") as mock_save_pdf:
                mock_save_pdf.return_value = "test_sha256"

                with patch.object(pipeline.state, "has_sha256") as mock_has_sha256:
                    mock_has_sha256.return_value = False

                    with patch.object(pipeline.gcs_storage, "upload_file") as mock_gcs_upload:
                        mock_gcs_upload.return_value = True

                        # Execute
                        result = pipeline._download_single_file(sample_gazette_file)

        # Assert
        assert result is True

        # Verify Supabase upsert was never attempted
        mock_upsert_file.assert_not_called()

    @patch("gazette_scraper.pipeline.upsert_file")
    @patch("gazette_scraper.pipeline.log_error")
    def test_exception_during_download_logs_error(self, mock_log_error, mock_upsert_file, sample_gazette_file):
        """Test that exceptions during download are logged to Supabase error table."""
        # Setup
        config = ScrapingConfig(
            output_dir=self.temp_dir,
            supabase_url="https://test.supabase.co",
            supabase_key="test_key",
        )

        pipeline = GazettePipeline(config)
        mock_log_error.return_value = True

        # Mock exception during HTTP request
        with patch.object(pipeline.client, "get") as mock_get:
            mock_get.side_effect = Exception("Connection timeout")

            # Execute
            result = pipeline._download_single_file(sample_gazette_file)

        # Assert
        assert result is False

        # Verify error was logged to Supabase with exception message
        mock_log_error.assert_called_once()
        args, kwargs = mock_log_error.call_args
        assert args[0] == str(sample_gazette_file.listing_url)  # listing_url
        assert args[1] == str(sample_gazette_file.download_url)  # download_url
        assert "Connection timeout" in args[2]  # error_message should contain exception

        # Verify no upsert was attempted
        mock_upsert_file.assert_not_called()
