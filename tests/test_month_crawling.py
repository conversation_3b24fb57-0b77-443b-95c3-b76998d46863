"""Tests for month-level crawling functionality."""

from __future__ import annotations

from unittest.mock import <PERSON>Mock, <PERSON><PERSON>

import pytest

from gazette_scraper.models import Folder, FolderType, ScrapingConfig
from gazette_scraper.parser import GazetteParser
from gazette_scraper.pipeline import GazettePipeline


class TestMonthCrawling:
    """Test month-level crawling functionality."""

    @pytest.fixture
    def config_depth_1(self) -> ScrapingConfig:
        """Config with crawler_depth=1 (year-level only)."""
        return ScrapingConfig(
            base_url="https://test.example.com",
            crawler_depth=1,
            output_dir="/tmp/test",
            dry_run=True,
        )

    @pytest.fixture
    def config_depth_2(self) -> ScrapingConfig:
        """Config with crawler_depth=2 (month-level)."""
        return ScrapingConfig(
            base_url="https://test.example.com",
            crawler_depth=2,
            output_dir="/tmp/test",
            dry_run=True,
        )

    @pytest.fixture
    def sample_month_page_1(self) -> str:
        """HTML for first page of month listings."""
        return '''
        <html>
        <body>
        <table>
            <tr><th>Name</th><th>Modified</th></tr>
            <tr>
                <td><a href="?tx_filelist_filelist[path]=/2024/01/">January 2024</a></td>
                <td>2024-01-15</td>
            </tr>
            <tr>
                <td><a href="?tx_filelist_filelist[path]=/2024/02/">February 2024</a></td>
                <td>2024-02-15</td>
            </tr>
            <tr>
                <td><a href="?tx_filelist_filelist[path]=/2024/03/">March 2024</a></td>
                <td>2024-03-15</td>
            </tr>
        </table>
        <a href="?currentPage=2">next</a>
        </body>
        </html>
        '''

    @pytest.fixture
    def sample_month_page_2(self) -> str:
        """HTML for second page of month listings."""
        return '''
        <html>
        <body>
        <table>
            <tr><th>Name</th><th>Modified</th></tr>
            <tr>
                <td><a href="?tx_filelist_filelist[path]=/2024/04/">April 2024</a></td>
                <td>2024-04-15</td>
            </tr>
            <tr>
                <td><a href="?tx_filelist_filelist[path]=/2024/05/">May 2024</a></td>
                <td>2024-05-15</td>
            </tr>
        </table>
        </body>
        </html>
        '''

    @pytest.fixture
    def sample_file_page(self) -> str:
        """HTML for file listings in a month folder."""
        return '''
        <html>
        <body>
        <table>
            <tr><th>File</th><th>Size</th><th>Modified</th></tr>
            <tr>
                <td><a href="/dumpFile?file=2024_01_gazette1.pdf">OG 2024/01/15</a></td>
                <td>1.2 MB</td>
                <td>2024-01-15</td>
            </tr>
            <tr>
                <td><a href="/dumpFile?file=2024_01_gazette2.pdf">OG 2024/01/30</a></td>
                <td>980 KB</td>
                <td>2024-01-30</td>
            </tr>
        </table>
        </body>
        </html>
        '''

    def test_parser_month_pagination(self, sample_month_page_1, sample_month_page_2):
        """Test parser handles month pagination correctly."""
        parser = GazetteParser("https://test.example.com")
        mock_client = MagicMock()
        mock_client.get().text = sample_month_page_2
        mock_client.get().raise_for_status.return_value = None

        folders = parser.parse_month_page_with_pagination(
            sample_month_page_1, "https://test.example.com/2024", 2024, mock_client
        )

        # Should find 5 total folders across 2 pages
        assert len(folders) == 5

        # Verify folder names
        folder_names = [f.name for f in folders]
        expected_names = ["January 2024", "February 2024", "March 2024", "April 2024", "May 2024"]
        assert folder_names == expected_names

        # Verify all folders have correct year and type
        for folder in folders:
            assert folder.year == 2024
            assert folder.folder_type == FolderType.MONTH

        # Verify client was called for second page (expect 2 calls total due to response handling)
        assert mock_client.get.call_count >= 1

    def test_parser_month_pagination_no_next_page(self, sample_month_page_2):
        """Test parser handles single page correctly."""
        parser = GazetteParser("https://test.example.com")
        mock_client = MagicMock()

        folders = parser.parse_month_page_with_pagination(
            sample_month_page_2, "https://test.example.com/2024", 2024, mock_client
        )

        # Should find 2 folders from single page
        assert len(folders) == 2

        # Client should not be called (no pagination)
        mock_client.get.assert_not_called()

    def test_parser_month_pagination_duplicate_detection(self, sample_month_page_1):
        """Test parser detects duplicate pages and stops."""
        parser = GazetteParser("https://test.example.com")
        mock_client = MagicMock()
        # Return same page (duplicate content)
        mock_client.get().text = sample_month_page_1
        mock_client.get().raise_for_status.return_value = None

        folders = parser.parse_month_page_with_pagination(
            sample_month_page_1, "https://test.example.com/2024", 2024, mock_client
        )

        # Should find 3 folders from first page only (duplicate detection stops pagination)
        assert len(folders) == 3

    def test_pipeline_depth_1_year_level_only(self, config_depth_1):
        """Test pipeline with depth=1 only processes year folders."""
        pipeline = GazettePipeline(config_depth_1)

        # Mock HTTP client
        mock_response = Mock()
        mock_response.text = '<a href="?tx_filelist_filelist[path]=/2024/">2024</a>'
        pipeline.client.get = Mock(return_value=mock_response)

        # Mock state methods
        pipeline.state.record_discovery = MagicMock()
        pipeline.state.record_file_discovery = MagicMock()
        pipeline.state.is_duplicate = MagicMock(return_value=False)

        # Mock _check_robots_txt
        pipeline._check_robots_txt = MagicMock(return_value=True)

        # Mock discover_years to return a test folder
        year_folder = Folder(
            name="2024",
            href="https://test.example.com?tx_filelist_filelist[path]=/2024/",
            folder_type=FolderType.YEAR,
            year=2024,
        )
        pipeline.discover_years = Mock(return_value=[year_folder])

        # Mock discover_files to return empty list
        pipeline.discover_files = Mock(return_value=[])

        manifest = pipeline.build_manifest(dry_run=True, since_year=None, out_dir=config_depth_1.output_dir)

        # Should use year-level traversal (depth=1)
        assert manifest.total_discovered == 0

        # With depth=1, should process year folders directly without month discovery
        # The manifest should be built successfully
        assert manifest is not None

    def test_pipeline_depth_2_month_level_traversal(self, config_depth_2):
        """Test pipeline with depth=2 processes month folders."""
        pipeline = GazettePipeline(config_depth_2)

        # Mock state methods
        pipeline.state.record_discovery = MagicMock()
        pipeline.state.record_file_discovery = MagicMock()
        pipeline.state.is_duplicate = MagicMock(return_value=False)

        # Mock _check_robots_txt
        pipeline._check_robots_txt = MagicMock(return_value=True)

        # Mock discover_years to return a test folder
        year_folder = Folder(
            name="2024",
            href="https://test.example.com?tx_filelist_filelist[path]=/2024/",
            folder_type=FolderType.YEAR,
            year=2024,
        )
        pipeline.discover_years = Mock(return_value=[year_folder])

        # Mock discover_months to return month folders
        month_folders = [
            Folder(
                name="January 2024",
                href="https://test.example.com?tx_filelist_filelist[path]=/2024/01/",
                folder_type=FolderType.MONTH,
                year=2024,
                month=1,
            ),
            Folder(
                name="February 2024",
                href="https://test.example.com?tx_filelist_filelist[path]=/2024/02/",
                folder_type=FolderType.MONTH,
                year=2024,
                month=2,
            ),
        ]
        pipeline.discover_months = Mock(return_value=month_folders)

        # Mock discover_files to return empty list
        pipeline.discover_files = Mock(return_value=[])

        manifest = pipeline.build_manifest(dry_run=True, since_year=None, out_dir=config_depth_2.output_dir)

        # Should use month-level traversal (depth=2)
        assert manifest.total_discovered == 0

        # discover_months should be called due to depth=2
        pipeline.discover_months.assert_called_once_with(year_folder)

    def test_gazette_file_month_folder_field(self, config_depth_2):
        """Test that GazetteFile includes month_folder field."""
        from pydantic import HttpUrl

        from gazette_scraper.models import GazetteFile

        gazette_file = GazetteFile(
            title="Test Gazette",
            filename="test.pdf",
            issue_title="Test Issue",
            download_url=HttpUrl("https://example.com/test.pdf"),
            source_url=HttpUrl("https://example.com/test.pdf"),
            listing_url=HttpUrl("https://example.com/listing"),
            year=2024,
            month=1,
            month_folder="January 2024",
        )

        assert gazette_file.month_folder == "January 2024"
        assert gazette_file.year == 2024
        assert gazette_file.month == 1

    def test_config_crawler_depth_default(self):
        """Test that crawler_depth defaults to 1."""
        config = ScrapingConfig()
        assert config.crawler_depth == 1

    def test_config_crawler_depth_env_override(self, monkeypatch):
        """Test that GAZETTE_CRAWLER_DEPTH environment variable works."""
        monkeypatch.setenv("GAZETTE_CRAWLER_DEPTH", "2")

        from gazette_scraper.config import load_config
        config = load_config()

        assert config.crawler_depth == 2

    def test_integration_end_to_end_month_crawl(self, config_depth_2):
        """Integration test: complete month crawl for 2024 with depth=2."""
        pipeline = GazettePipeline(config_depth_2)

        # Mock _check_robots_txt
        pipeline._check_robots_txt = MagicMock(return_value=True)

        # Mock the entire build_manifest to return empty manifest
        from gazette_scraper.models import Manifest
        mock_manifest = Manifest()
        mock_manifest.total_discovered = 1
        pipeline.build_manifest = Mock(return_value=mock_manifest)

        result = pipeline.run(dry_run=True)

        # Should complete without errors
        assert result.errors == 0
        assert result.total_discovered == 1
        assert result.skipped == 1  # dry_run skips all files

    def test_month_folder_name_extraction(self):
        """Test that month folder names are correctly extracted and stored."""
        from gazette_scraper.models import Folder, FolderType

        folder = Folder(
            name="January 2024",
            href="https://example.com/path",
            folder_type=FolderType.MONTH,
            year=2024,
            month=1,
        )

        assert folder.name == "January 2024"
        assert folder.month == 1
        assert folder.year == 2024
