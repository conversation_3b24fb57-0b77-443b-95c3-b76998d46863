"""Tests for storage module including LocalStorage, ManifestWriter, and GCSStorage."""

from __future__ import annotations

import csv
import tempfile
from datetime import datetime
from pathlib import Path
from unittest.mock import Mock, patch

from gazette_scraper.models import GazetteFile
from gazette_scraper.storage import GCSStorage, LocalStorage, ManifestWriter


class TestLocalStorage:
    """Test LocalStorage functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.storage = LocalStorage(self.temp_dir)

    def teardown_method(self):
        """Clean up test fixtures."""
        import shutil

        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)

    def test_get_file_path_basic(self):
        """Test basic file path generation."""
        file = GazetteFile(
            title="Test Gazette.pdf",
            filename="test_gazette.pdf",
            issue_title="Official Gazette Test Issue",
            download_url="https://minijust.gov.rw/dumpFile?f=123&t=test.pdf",
            source_url="https://minijust.gov.rw/dumpFile?f=123&t=test.pdf",
            listing_url="https://minijust.gov.rw/listing",
            year=2024,
            month=3,
            size_str="1.2 MB",
            modified_date=datetime(2024, 3, 15),
        )

        path = self.storage.get_file_path(file)
        expected_path = self.temp_dir / "2024" / "03" / "test_gazette.pdf"

        assert path == expected_path
        assert path.parent.exists()  # Directory should be created

    def test_get_file_path_with_pub_date(self):
        """Test file path generation with publication date."""
        file = GazetteFile(
            title="Official Gazette.pdf",
            filename="gazette.pdf",
            issue_title="Official Gazette Issue #2",
            download_url="https://minijust.gov.rw/dumpFile?f=124&t=gazette.pdf",
            source_url="https://minijust.gov.rw/dumpFile?f=124&t=gazette.pdf",
            listing_url="https://minijust.gov.rw/listing",
            year=2024,
            month=2,
            pub_date=datetime(2024, 2, 14),
            size_str="856 KB",
            modified_date=datetime(2024, 2, 15),
        )

        path = self.storage.get_file_path(file)
        expected_path = self.temp_dir / "2024" / "02" / "2024-02-14_gazette.pdf"

        assert path == expected_path

    def test_get_file_path_unique_naming(self):
        """Test unique filename generation when conflicts exist."""
        file = GazetteFile(
            title="Duplicate.pdf",
            filename="duplicate.pdf",
            issue_title="Duplicate Issue",
            download_url="https://minijust.gov.rw/dumpFile?f=125&t=dup.pdf",
            source_url="https://minijust.gov.rw/dumpFile?f=125&t=dup.pdf",
            listing_url="https://minijust.gov.rw/listing",
            year=2024,
            month=1,
            size_str="500 KB",
            modified_date=datetime(2024, 1, 10),
        )

        # Create the first file
        first_path = self.storage.get_file_path(file)
        first_path.touch()  # Create the file

        # Get path for second file with same name
        second_path = self.storage.get_file_path(file)
        expected_second = self.temp_dir / "2024" / "01" / "duplicate_1.pdf"

        assert second_path == expected_second
        assert first_path != second_path

    def test_file_exists(self):
        """Test file existence checking."""
        test_file = self.temp_dir / "test.pdf"

        assert not self.storage.file_exists(test_file)

        test_file.touch()
        assert self.storage.file_exists(test_file)

    def test_ensure_dirs(self):
        """Test directory creation."""
        new_dir = self.temp_dir / "new" / "nested" / "directory"

        assert not new_dir.exists()

        self.storage.ensure_dirs(new_dir)
        assert new_dir.exists()
        assert new_dir.is_dir()

    def test_save_pdf(self):
        """Test PDF saving with SHA256 calculation."""
        content = b"PDF content here for testing SHA256 calculation"
        dst_path = self.temp_dir / "subdir" / "test.pdf"

        sha256 = self.storage.save_pdf(content, dst_path)

        # Verify file was created
        assert dst_path.exists()
        assert dst_path.parent.exists()

        # Verify content was written correctly
        with open(dst_path, "rb") as f:
            saved_content = f.read()
        assert saved_content == content

        # Verify SHA256 calculation
        import hashlib

        expected_sha256 = hashlib.sha256(content).hexdigest()
        assert sha256 == expected_sha256
        assert len(sha256) == 64  # SHA256 hex length


class TestManifestWriter:
    """Test ManifestWriter functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.manifest_path = self.temp_dir / "manifest.csv"
        self.manifest = ManifestWriter(self.manifest_path)

    def teardown_method(self):
        """Clean up test fixtures."""
        import shutil

        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)

    def test_write_header(self):
        """Test CSV header writing."""
        self.manifest.write_header()

        assert self.manifest_path.exists()

        with open(self.manifest_path, encoding="utf-8") as f:
            reader = csv.reader(f)
            header = next(reader)

        expected_fields = [
            "year",
            "month",
            "title",
            "filename",
            "pub_date",
            "sha256",
            "source_url",
            "download_url",
            "local_path",
            "size",
            "modified_date",
            "supabase_upserted",
            "document_type",
            "subject_category",
            "keywords",
        ]
        assert header == expected_fields

    def test_write_header_idempotent(self):
        """Test that write_header doesn't overwrite existing file."""
        # Write header first time
        self.manifest.write_header()

        # Add a test line
        with open(self.manifest_path, "a", encoding="utf-8") as f:
            f.write("test,line\\n")

        # Write header again
        self.manifest.write_header()

        # Should still have test line
        with open(self.manifest_path, encoding="utf-8") as f:
            lines = f.readlines()

        assert len(lines) == 2  # Header + test line
        assert "test,line" in lines[1]

    def test_append_file(self):
        """Test appending file records to manifest."""
        file = GazetteFile(
            title="Test Official Gazette",
            filename="test_gazette.pdf",
            issue_title="Official Gazette Test Issue",
            download_url="https://minijust.gov.rw/dumpFile?f=123&t=test.pdf",
            source_url="https://minijust.gov.rw/dumpFile?f=123&t=test.pdf",
            listing_url="https://minijust.gov.rw/gazette/2024/01",
            year=2024,
            month=1,
            pub_date=datetime(2024, 1, 15, 10, 30),
            size_str="1.5 MB",
            modified_date=datetime(2024, 1, 16, 8, 0),
        )

        local_path = Path("/tmp/2024/01/test_gazette.pdf")
        sha256 = "abc123def456"

        self.manifest.append_file(file, local_path, sha256)

        # Verify file was created and contains expected data
        assert self.manifest_path.exists()

        with open(self.manifest_path, encoding="utf-8") as f:
            reader = csv.DictReader(f)
            records = list(reader)

        assert len(records) == 1
        record = records[0]

        assert record["year"] == "2024"
        assert record["month"] == "1"
        assert record["title"] == "Test Official Gazette"
        assert record["filename"] == "test_gazette.pdf"
        assert record["pub_date"] == "2024-01-15T10:30:00"
        assert record["sha256"] == "abc123def456"
        assert (
            record["source_url"] == "https://minijust.gov.rw/dumpFile?f=123&t=test.pdf"
        )
        assert (
            record["download_url"]
            == "https://minijust.gov.rw/dumpFile?f=123&t=test.pdf"
        )
        assert record["local_path"] == "/tmp/2024/01/test_gazette.pdf"
        assert record["size"] == "1.5 MB"
        assert record["modified_date"] == "2024-01-16T08:00:00"

    def test_append_file_optional_fields(self):
        """Test appending file with optional fields missing."""
        file = GazetteFile(
            title="Minimal Gazette",
            filename="minimal.pdf",
            issue_title="Minimal Issue",
            download_url="https://minijust.gov.rw/dumpFile?f=124&t=min.pdf",
            source_url="https://minijust.gov.rw/dumpFile?f=124&t=min.pdf",
            listing_url="https://minijust.gov.rw/gazette/2024/02",
            year=2024,
            month=2,
            modified_date=datetime(2024, 2, 1),
            # pub_date and size_str are None
        )

        local_path = Path("/tmp/minimal.pdf")
        sha256 = "def789ghi012"

        self.manifest.append_file(file, local_path, sha256)

        with open(self.manifest_path, encoding="utf-8") as f:
            reader = csv.DictReader(f)
            record = next(reader)

        assert record["pub_date"] == ""
        assert record["size"] == ""
        assert record["modified_date"] == "2024-02-01T00:00:00"

    def test_multiple_appends(self):
        """Test multiple file appends."""
        files_data = [
            ("File1.pdf", "sha1"),
            ("File2.pdf", "sha2"),
            ("File3.pdf", "sha3"),
        ]

        for filename, sha in files_data:
            file = GazetteFile(
                title=f"Title {filename}",
                filename=filename,
                issue_title=f"Issue {filename}",
                download_url=f"https://minijust.gov.rw/dumpFile?f=123&t={filename}",
                source_url=f"https://minijust.gov.rw/dumpFile?f=123&t={filename}",
                listing_url="https://minijust.gov.rw/listing",
                year=2024,
                month=3,
                modified_date=datetime(2024, 3, 1),
            )

            self.manifest.append_file(file, Path(f"/tmp/{filename}"), sha)

        with open(self.manifest_path, encoding="utf-8") as f:
            reader = csv.DictReader(f)
            records = list(reader)

        assert len(records) == 3
        for i, record in enumerate(records):
            expected_filename = files_data[i][0]
            expected_sha = files_data[i][1]
            assert record["filename"] == expected_filename
            assert record["sha256"] == expected_sha


class TestGCSStorage:
    """Test GCSStorage functionality with mocking."""

    def setup_method(self):
        """Set up test fixtures."""
        self.bucket_name = "test-gazette-bucket"
        self.temp_dir = Path(tempfile.mkdtemp())

    def teardown_method(self):
        """Clean up test fixtures."""
        import shutil

        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)

    def test_client_property_default(self):
        """Test GCS client creation with default credentials."""
        with patch("builtins.__import__") as mock_import:
            mock_storage = Mock()
            mock_client = Mock()
            mock_storage.Client.return_value = mock_client

            def side_effect(name, *args, **kwargs):
                if name == "google.cloud":
                    return Mock(storage=mock_storage)
                return __import__(name, *args, **kwargs)

            mock_import.side_effect = side_effect

            gcs = GCSStorage(self.bucket_name)
            client = gcs.client

            assert client == mock_client
            mock_storage.Client.assert_called_once()

    def test_client_property_with_credentials(self):
        """Test GCS client creation with service account credentials."""
        with patch("builtins.__import__") as mock_import:
            mock_storage = Mock()
            mock_client = Mock()
            mock_storage.Client.from_service_account_json.return_value = mock_client

            def side_effect(name, *args, **kwargs):
                if name == "google.cloud":
                    return Mock(storage=mock_storage)
                return __import__(name, *args, **kwargs)

            mock_import.side_effect = side_effect

            credentials_path = self.temp_dir / "credentials.json"
            credentials_path.write_text('{"type": "service_account"}')

            gcs = GCSStorage(self.bucket_name, credentials_path)
            client = gcs.client

            assert client == mock_client
            mock_storage.Client.from_service_account_json.assert_called_once_with(
                str(credentials_path)
            )

    def test_client_import_error(self):
        """Test handling of missing google-cloud-storage dependency."""
        with patch("builtins.__import__", side_effect=ImportError("No module")):
            gcs = GCSStorage(self.bucket_name)

            try:
                _ = gcs.client
                raise AssertionError("Should have raised ImportError")
            except ImportError as e:
                assert "google-cloud-storage is required" in str(e)

    def test_bucket_property(self):
        """Test GCS bucket property lazy loading."""
        with patch("builtins.__import__") as mock_import:
            mock_storage = Mock()
            mock_client = Mock()
            mock_bucket = Mock()
            mock_client.bucket.return_value = mock_bucket
            mock_storage.Client.return_value = mock_client

            def side_effect(name, *args, **kwargs):
                if name == "google.cloud":
                    return Mock(storage=mock_storage)
                return __import__(name, *args, **kwargs)

            mock_import.side_effect = side_effect

            gcs = GCSStorage(self.bucket_name)
            bucket = gcs.bucket

            assert bucket == mock_bucket
            mock_client.bucket.assert_called_once_with(self.bucket_name)

    def test_upload_file_success(self):
        """Test successful file upload to GCS."""
        with patch("builtins.__import__") as mock_import:
            mock_storage = Mock()
            mock_client = Mock()
            mock_bucket = Mock()
            mock_blob = Mock()

            mock_client.bucket.return_value = mock_bucket
            mock_bucket.blob.return_value = mock_blob
            mock_storage.Client.return_value = mock_client

            def side_effect(name, *args, **kwargs):
                if name == "google.cloud":
                    return Mock(storage=mock_storage)
                return __import__(name, *args, **kwargs)

            mock_import.side_effect = side_effect

            # Create a test file
            test_file = self.temp_dir / "test.pdf"
            test_file.write_bytes(b"test content")

            gcs = GCSStorage(self.bucket_name)
            result = gcs.upload_file(test_file, "remote/path/test.pdf")

            assert result is True
            mock_bucket.blob.assert_called_once_with("remote/path/test.pdf")
            mock_blob.upload_from_filename.assert_called_once_with(str(test_file))

    def test_upload_file_failure(self):
        """Test file upload failure handling."""
        with patch("builtins.__import__") as mock_import:
            mock_storage = Mock()
            mock_client = Mock()
            mock_bucket = Mock()
            mock_blob = Mock()
            mock_blob.upload_from_filename.side_effect = Exception("Upload failed")

            mock_client.bucket.return_value = mock_bucket
            mock_bucket.blob.return_value = mock_blob
            mock_storage.Client.return_value = mock_client

            def side_effect(name, *args, **kwargs):
                if name == "google.cloud":
                    return Mock(storage=mock_storage)
                return __import__(name, *args, **kwargs)

            mock_import.side_effect = side_effect

            test_file = self.temp_dir / "test.pdf"
            test_file.write_bytes(b"test content")

            gcs = GCSStorage(self.bucket_name)
            result = gcs.upload_file(test_file, "remote/path/test.pdf")

            assert result is False

    def test_file_exists_true(self):
        """Test file existence check returning True."""
        with patch("builtins.__import__") as mock_import:
            mock_storage = Mock()
            mock_client = Mock()
            mock_bucket = Mock()
            mock_blob = Mock()
            mock_blob.exists.return_value = True

            mock_client.bucket.return_value = mock_bucket
            mock_bucket.blob.return_value = mock_blob
            mock_storage.Client.return_value = mock_client

            def side_effect(name, *args, **kwargs):
                if name == "google.cloud":
                    return Mock(storage=mock_storage)
                return __import__(name, *args, **kwargs)

            mock_import.side_effect = side_effect

            gcs = GCSStorage(self.bucket_name)
            result = gcs.file_exists("remote/path/test.pdf")

            assert result is True
            mock_bucket.blob.assert_called_once_with("remote/path/test.pdf")
            mock_blob.exists.assert_called_once()

    def test_file_exists_false(self):
        """Test file existence check returning False."""
        with patch("builtins.__import__") as mock_import:
            mock_storage = Mock()
            mock_client = Mock()
            mock_bucket = Mock()
            mock_blob = Mock()
            mock_blob.exists.return_value = False

            mock_client.bucket.return_value = mock_bucket
            mock_bucket.blob.return_value = mock_blob
            mock_storage.Client.return_value = mock_client

            def side_effect(name, *args, **kwargs):
                if name == "google.cloud":
                    return Mock(storage=mock_storage)
                return __import__(name, *args, **kwargs)

            mock_import.side_effect = side_effect

            gcs = GCSStorage(self.bucket_name)
            result = gcs.file_exists("remote/path/nonexistent.pdf")

            assert result is False

    def test_file_exists_error(self):
        """Test file existence check error handling."""
        with patch("builtins.__import__") as mock_import:
            mock_storage = Mock()
            mock_client = Mock()
            mock_bucket = Mock()
            mock_blob = Mock()
            mock_blob.exists.side_effect = Exception("Network error")

            mock_client.bucket.return_value = mock_bucket
            mock_bucket.blob.return_value = mock_blob
            mock_storage.Client.return_value = mock_client

            def side_effect(name, *args, **kwargs):
                if name == "google.cloud":
                    return Mock(storage=mock_storage)
                return __import__(name, *args, **kwargs)

            mock_import.side_effect = side_effect

            gcs = GCSStorage(self.bucket_name)
            result = gcs.file_exists("remote/path/test.pdf")

            assert result is False  # Should return False on error
