"""Tests for data models."""

from datetime import datetime
from pathlib import Path

from gazette_scraper.models import (
    GazetteFile,
    GazetteFolder,
    ScrapeResult,
    ScrapingConfig,
)


class TestGazetteFile:
    """Test GazetteFile model."""

    def test_create_gazette_file(self):
        """Test creating a gazette file with required fields."""
        file = GazetteFile(
            title="Official Gazette No. 1",
            filename="gazette_2024_01.pdf",
            issue_title="Official Gazette No. 1",
            download_url="https://example.com/file.pdf",
            source_url="https://example.com/file.pdf",
            listing_url="https://example.com/folder",
            year=2024,
            month=1,
        )

        assert file.title == "Official Gazette No. 1"
        assert file.issue_title == "Official Gazette No. 1"
        assert file.year == 2024
        assert file.month == 1
        assert file.sha256 is None
        assert file.local_path is None

    def test_gazette_file_with_optional_fields(self):
        """Test gazette file with all optional fields."""
        pub_date = datetime(2024, 1, 15)
        local_path = Path("/data/2024/01/gazette.pdf")

        file = GazetteFile(
            title="Test Gazette",
            filename="test.pdf",
            issue_title="Test Gazette Issue",
            size_str="1.2 MB",
            modified_date=pub_date,
            download_url="https://example.com/test.pdf",
            source_url="https://example.com/folder",
            listing_url="https://example.com/folder",
            year=2024,
            month=1,
            pub_date=pub_date,
            sha256="a1b2c3d4e5f67890123456789012345678901234567890123456789012345678",
            local_path=local_path,
        )

        assert file.size_str == "1.2 MB"
        assert file.pub_date == pub_date
        assert (
            file.sha256
            == "a1b2c3d4e5f67890123456789012345678901234567890123456789012345678"
        )
        assert file.local_path == local_path


class TestGazetteFolder:
    """Test GazetteFolder model."""

    def test_create_gazette_folder(self):
        """Test creating a gazette folder."""
        folder = GazetteFolder(
            year=2024,
            month=1,
            folder_path="Official Gazette/2024/01",
            folder_url="https://example.com/2024/01",
        )

        assert folder.year == 2024
        assert folder.month == 1
        assert folder.files == []

    def test_gazette_folder_with_files(self):
        """Test gazette folder with files."""
        file = GazetteFile(
            title="Test",
            filename="test.pdf",
            issue_title="Test",
            download_url="https://example.com/test.pdf",
            source_url="https://example.com/folder",
            listing_url="https://example.com/folder",
            year=2024,
            month=1,
        )

        folder = GazetteFolder(
            year=2024,
            month=1,
            folder_path="2024/01",
            folder_url="https://example.com/2024/01",
            files=[file],
        )

        assert len(folder.files) == 1
        assert folder.files[0] == file


class TestScrapingConfig:
    """Test ScrapingConfig model."""

    def test_default_config(self):
        """Test default configuration values."""
        config = ScrapingConfig()

        assert config.base_url == "https://www.minijust.gov.rw"
        assert config.rate_limit == 1.0
        assert config.max_retries == 3
        assert config.output_dir == Path("./data")
        assert config.max_threads == 4
        assert config.proxies is None
        assert config.since_year is None

    def test_custom_config(self):
        """Test configuration with custom values."""
        config = ScrapingConfig(
            rate_limit=2.0,
            max_threads=8,
            output_dir=Path("/custom/path"),
            proxies=["http://proxy:8080"],
            since_year=2020,
        )

        assert config.rate_limit == 2.0
        assert config.max_threads == 8
        assert config.output_dir == Path("/custom/path")
        assert config.proxies == ["http://proxy:8080"]
        assert config.since_year == 2020


class TestScrapeResult:
    """Test ScrapeResult model."""

    def test_create_scrape_result(self):
        """Test creating a scrape result."""
        start_time = datetime.now()
        result = ScrapeResult(start_time=start_time)

        assert result.start_time == start_time
        assert result.total_discovered == 0
        assert result.downloaded == 0
        assert result.skipped == 0
        assert result.errors == 0
        assert result.end_time is None
        assert result.error_messages == []

    def test_scrape_result_with_data(self):
        """Test scrape result with data."""
        start_time = datetime.now()
        end_time = datetime.now()

        result = ScrapeResult(
            start_time=start_time,
            end_time=end_time,
            total_discovered=100,
            downloaded=95,
            skipped=3,
            errors=2,
            error_messages=["Error 1", "Error 2"],
        )

        assert result.total_discovered == 100
        assert result.downloaded == 95
        assert result.skipped == 3
        assert result.errors == 2
        assert len(result.error_messages) == 2
