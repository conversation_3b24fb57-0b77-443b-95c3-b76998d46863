"""
Concurrent Operations and Race Conditions Tests

Tests to ensure the scraper handles multiple simultaneous requests 
and browser contexts without conflicts in Cloud Run environment.
"""

import asyncio
import logging
import pytest
import tempfile
import threading
import time
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch
from concurrent.futures import Thread<PERSON>oolExecutor

from gazette_scraper.caselaw.models import (
    CaseLawConfig, CourtNode, NavigationStatus, CaseLawFile
)
from gazette_scraper.caselaw.navigator import CaseLawNavigator
from gazette_scraper.caselaw.downloader import CaseLawDownloader
from gazette_scraper.caselaw.pipeline import CaseLawPipeline
from gazette_scraper.caselaw.state import CaseLawState

logger = logging.getLogger(__name__)


class TestConcurrentOperations:
    """Test concurrent operations and race condition handling."""

    @pytest.fixture
    def concurrent_config(self):
        """Configuration for concurrent operations testing."""
        return CaseLawConfig(
            browser_timeout=30000,
            navigation_delay=0.5,  # Reduced for faster testing
            page_load_timeout=15000,
            element_timeout=10000,
            max_retry_attempts=3,
            retry_delay=1.0,
            headless=True,
            screenshot_on_error=False,
            debug_mode=False,
            max_concurrent_downloads=3,  # Allow concurrent downloads
            max_browser_contexts=2,      # Multiple contexts
        )

    @pytest.fixture
    def temp_state_db(self):
        """Temporary state database for testing."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = Path(f.name)
        yield db_path
        if db_path.exists():
            db_path.unlink()

    @pytest.mark.asyncio
    async def test_concurrent_navigation_operations(self, concurrent_config, temp_state_db):
        """Test concurrent navigation operations don't interfere."""
        state = CaseLawState(temp_state_db)
        
        async def navigate_court(court_id: int):
            """Simulate navigation to a court."""
            navigator = CaseLawNavigator(concurrent_config, state)
            
            node = CourtNode(
                name=f"Court {court_id}",
                full_path=f"Court {court_id}",
                node_type="court",
                status=NavigationStatus.PENDING
            )
            
            # Save node
            state.save_court_node(node)
            
            # Simulate navigation work
            await asyncio.sleep(0.1)
            
            # Update status
            node.status = NavigationStatus.COMPLETED
            state.save_court_node(node)
            
            return court_id
        
        # Run multiple concurrent navigations
        tasks = [navigate_court(i) for i in range(10)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All should complete successfully
        assert len(results) == 10
        assert all(isinstance(r, int) for r in results)
        
        # Verify all nodes were saved
        all_nodes = state.get_all_court_nodes()
        assert len(all_nodes) == 10
        
        # All should be completed
        completed_nodes = [n for n in all_nodes if n.status == NavigationStatus.COMPLETED]
        assert len(completed_nodes) == 10

    @pytest.mark.asyncio
    async def test_concurrent_download_operations(self, concurrent_config, temp_state_db):
        """Test concurrent download operations with proper throttling."""
        state = CaseLawState(temp_state_db)
        downloader = CaseLawDownloader(concurrent_config, state)
        
        # Track concurrent downloads
        active_downloads = []
        max_concurrent = 0
        download_lock = asyncio.Lock()
        
        async def mock_download(case_file, url):
            """Mock download with concurrency tracking."""
            async with download_lock:
                active_downloads.append(case_file.filename)
                nonlocal max_concurrent
                max_concurrent = max(max_concurrent, len(active_downloads))
            
            # Simulate download time
            await asyncio.sleep(0.2)
            
            async with download_lock:
                active_downloads.remove(case_file.filename)
            
            return b"PDF content", "sha256_hash"
        
        # Mock the download method
        with patch.object(downloader, 'download_pdf_file', side_effect=mock_download):
            # Create multiple case files
            case_files = []
            for i in range(8):
                case_file = CaseLawFile(
                    case_title=f"Case {i}",
                    case_page_url=f"https://example.com/case{i}",
                    filename=f"case_{i}.pdf",
                    court_path="Test Court/2024/1/Day 1",
                    discovered_at=None
                )
                case_files.append(case_file)
            
            # Start concurrent downloads
            tasks = [
                downloader.download_pdf_file(case_file, f"https://example.com/{case_file.filename}")
                for case_file in case_files
            ]
            
            results = await asyncio.gather(*tasks)
            
            # All downloads should complete
            assert len(results) == 8
            assert all(r is not None for r in results)
            
            # Should respect max concurrent downloads
            assert max_concurrent <= concurrent_config.max_concurrent_downloads

    @pytest.mark.asyncio
    async def test_concurrent_state_access(self, concurrent_config, temp_state_db):
        """Test concurrent state database access."""
        state = CaseLawState(temp_state_db)
        
        async def concurrent_state_operations(worker_id: int):
            """Perform concurrent state operations."""
            operations_completed = 0
            
            for i in range(5):
                # Create node
                node = CourtNode(
                    name=f"Worker{worker_id}_Node{i}",
                    full_path=f"Worker{worker_id}_Node{i}",
                    node_type="court",
                    status=NavigationStatus.PENDING
                )
                
                # Save node
                state.save_court_node(node)
                operations_completed += 1
                
                # Small delay to increase chance of race conditions
                await asyncio.sleep(0.01)
                
                # Update node
                node.status = NavigationStatus.COMPLETED
                state.save_court_node(node)
                operations_completed += 1
                
                # Create case file
                case_file = CaseLawFile(
                    case_title=f"Worker{worker_id}_Case{i}",
                    case_page_url=f"https://example.com/worker{worker_id}_case{i}",
                    filename=f"worker{worker_id}_case{i}.pdf",
                    court_path=f"Worker{worker_id}_Node{i}",
                    discovered_at=None
                )
                
                state.save_case_file(case_file)
                operations_completed += 1
            
            return operations_completed
        
        # Run multiple workers concurrently
        workers = [concurrent_state_operations(i) for i in range(5)]
        results = await asyncio.gather(*workers)
        
        # All workers should complete all operations
        assert all(r == 15 for r in results)  # 5 nodes * 3 operations each
        
        # Verify data integrity
        all_nodes = state.get_all_court_nodes()
        all_cases = state.get_all_case_files()
        
        assert len(all_nodes) == 25  # 5 workers * 5 nodes each
        assert len(all_cases) == 25  # 5 workers * 5 cases each
        
        # All nodes should be completed
        completed_nodes = [n for n in all_nodes if n.status == NavigationStatus.COMPLETED]
        assert len(completed_nodes) == 25

    @pytest.mark.asyncio
    async def test_browser_context_isolation(self, concurrent_config, temp_state_db):
        """Test that multiple browser contexts don't interfere."""
        state = CaseLawState(temp_state_db)
        
        async def browser_operation(context_id: int):
            """Simulate browser operation in isolated context."""
            navigator = CaseLawNavigator(concurrent_config, state)
            
            # Mock browser context
            with patch('playwright.async_api.async_playwright') as mock_playwright:
                mock_browser = AsyncMock()
                mock_context = AsyncMock()
                mock_page = AsyncMock()
                
                mock_playwright.return_value.__aenter__.return_value.chromium.launch.return_value = mock_browser
                mock_browser.new_context.return_value = mock_context
                mock_context.new_page.return_value = mock_page
                
                # Start navigator
                await navigator.start()
                
                # Simulate page operations
                mock_page.goto = AsyncMock()
                mock_page.wait_for_load_state = AsyncMock()
                
                for i in range(3):
                    await mock_page.goto(f"https://example.com/context{context_id}_page{i}")
                    await mock_page.wait_for_load_state("networkidle")
                    await asyncio.sleep(0.05)  # Small delay
                
                # Close navigator
                await navigator.close()
                
                return context_id
        
        # Run multiple browser operations concurrently
        tasks = [browser_operation(i) for i in range(3)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All should complete successfully
        assert len(results) == 3
        assert all(isinstance(r, int) for r in results)

    @pytest.mark.asyncio
    async def test_race_condition_in_node_expansion(self, concurrent_config, temp_state_db):
        """Test race conditions during node expansion."""
        state = CaseLawState(temp_state_db)
        
        # Create a parent node
        parent_node = CourtNode(
            name="Parent Court",
            full_path="Parent Court",
            node_type="court",
            status=NavigationStatus.DISCOVERED
        )
        state.save_court_node(parent_node)
        
        async def expand_node(worker_id: int):
            """Simulate concurrent node expansion."""
            # Multiple workers try to expand the same node
            node = state.get_court_node("Parent Court")
            
            if node and node.status == NavigationStatus.DISCOVERED:
                # Mark as expanding
                node.status = NavigationStatus.EXPANDING
                state.save_court_node(node)
                
                # Simulate expansion work
                await asyncio.sleep(0.1)
                
                # Create child nodes
                for i in range(3):
                    child_node = CourtNode(
                        name=f"Child {worker_id}_{i}",
                        full_path=f"Parent Court/Child {worker_id}_{i}",
                        node_type="year",
                        status=NavigationStatus.DISCOVERED
                    )
                    state.save_court_node(child_node)
                
                # Mark as completed
                node.status = NavigationStatus.COMPLETED
                state.save_court_node(node)
                
                return True
            
            return False
        
        # Multiple workers try to expand the same node
        tasks = [expand_node(i) for i in range(3)]
        results = await asyncio.gather(*tasks)
        
        # Only one worker should successfully expand
        successful_expansions = sum(results)
        assert successful_expansions == 1
        
        # Parent should be completed
        parent = state.get_court_node("Parent Court")
        assert parent.status == NavigationStatus.COMPLETED
        
        # Should have child nodes from only one worker
        all_nodes = state.get_all_court_nodes()
        child_nodes = [n for n in all_nodes if n.full_path.startswith("Parent Court/Child")]
        assert len(child_nodes) == 3

    @pytest.mark.asyncio
    async def test_concurrent_error_handling(self, concurrent_config, temp_state_db):
        """Test error handling in concurrent operations."""
        state = CaseLawState(temp_state_db)
        
        async def operation_with_errors(worker_id: int):
            """Operation that may encounter errors."""
            try:
                if worker_id % 2 == 0:
                    # Even workers succeed
                    node = CourtNode(
                        name=f"Success Node {worker_id}",
                        full_path=f"Success Node {worker_id}",
                        node_type="court",
                        status=NavigationStatus.COMPLETED
                    )
                    state.save_court_node(node)
                    return "success"
                else:
                    # Odd workers fail
                    raise Exception(f"Simulated error from worker {worker_id}")
            
            except Exception as e:
                # Handle error gracefully
                error_node = CourtNode(
                    name=f"Error Node {worker_id}",
                    full_path=f"Error Node {worker_id}",
                    node_type="court",
                    status=NavigationStatus.FAILED
                )
                state.save_court_node(error_node)
                return f"error: {str(e)}"
        
        # Run operations with mixed success/failure
        tasks = [operation_with_errors(i) for i in range(6)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Should have mix of success and error results
        success_count = sum(1 for r in results if r == "success")
        error_count = sum(1 for r in results if isinstance(r, str) and r.startswith("error"))
        
        assert success_count == 3  # Even workers (0, 2, 4)
        assert error_count == 3   # Odd workers (1, 3, 5)
        
        # Verify nodes were saved correctly
        all_nodes = state.get_all_court_nodes()
        success_nodes = [n for n in all_nodes if n.status == NavigationStatus.COMPLETED]
        failed_nodes = [n for n in all_nodes if n.status == NavigationStatus.FAILED]
        
        assert len(success_nodes) == 3
        assert len(failed_nodes) == 3

    @pytest.mark.asyncio
    async def test_resource_cleanup_in_concurrent_operations(self, concurrent_config, temp_state_db):
        """Test proper resource cleanup in concurrent operations."""
        state = CaseLawState(temp_state_db)
        
        # Track resource creation and cleanup
        created_resources = []
        cleaned_resources = []
        resource_lock = asyncio.Lock()
        
        class MockResource:
            def __init__(self, resource_id):
                self.resource_id = resource_id
                self.cleaned = False
            
            async def cleanup(self):
                self.cleaned = True
                async with resource_lock:
                    cleaned_resources.append(self.resource_id)
        
        async def operation_with_resources(worker_id: int):
            """Operation that creates and cleans up resources."""
            resources = []
            
            try:
                # Create resources
                for i in range(3):
                    resource = MockResource(f"worker{worker_id}_resource{i}")
                    resources.append(resource)
                    
                    async with resource_lock:
                        created_resources.append(resource.resource_id)
                
                # Simulate work
                await asyncio.sleep(0.1)
                
                # Simulate potential error in some workers
                if worker_id == 2:
                    raise Exception("Simulated error")
                
                return "success"
            
            finally:
                # Clean up resources
                for resource in resources:
                    await resource.cleanup()
        
        # Run concurrent operations
        tasks = [operation_with_resources(i) for i in range(4)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verify resource management
        assert len(created_resources) == 12  # 4 workers * 3 resources each
        assert len(cleaned_resources) == 12  # All resources should be cleaned up
        
        # Verify all resources were cleaned
        assert set(created_resources) == set(cleaned_resources)

    def test_thread_safety_of_state_operations(self, concurrent_config, temp_state_db):
        """Test thread safety of state operations."""
        state = CaseLawState(temp_state_db)
        
        def thread_operation(thread_id: int):
            """Operation running in separate thread."""
            for i in range(10):
                node = CourtNode(
                    court_name=f"Thread{thread_id}_Node{i}",
                    full_path=f"Thread{thread_id}_Node{i}",
                    node_type="court",
                    status=NavigationStatus.PENDING
                )
                
                # Save node
                state.save_court_node(node)
                
                # Small delay to increase chance of race conditions
                time.sleep(0.001)
                
                # Update node
                node.status = NavigationStatus.COMPLETED
                state.save_court_node(node)
        
        # Run operations in multiple threads
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(thread_operation, i) for i in range(4)]
            
            # Wait for all threads to complete
            for future in futures:
                future.result()
        
        # Verify data integrity
        all_nodes = state.get_all_court_nodes()
        assert len(all_nodes) == 40  # 4 threads * 10 nodes each
        
        # All nodes should be completed
        completed_nodes = [n for n in all_nodes if n.status == NavigationStatus.COMPLETED]
        assert len(completed_nodes) == 40
