"""Integration tests for gazette extractor."""

from __future__ import annotations

import json
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

import pytest

from gazette_scraper.extractor.app import extract_gazette
from gazette_scraper.extractor.schema import (
    DocumentMetadata,
    GeminiResponse,
    PageBlock,
    PageData,
    SectionType,
    Language,
)


class TestExtractorIntegration:
    """Test extractor integration."""
    
    @patch('gazette_scraper.extractor.gemini_client.GeminiClient')
    def test_extract_gazette_success(self, mock_client_class: Mock) -> None:
        """Test successful gazette extraction."""
        # Mock Gemini client response
        mock_response = GeminiResponse(
            doc=DocumentMetadata(
                title="Test Official Gazette",
                date_iso="2024-01-15",
                source_filename="test.pdf"
            ),
            pages=[
                PageData(
                    page_index=0,
                    blocks=[
                        PageBlock(
                            section=SectionType.TITLE,
                            text="Official Gazette Title"
                        ),
                        PageBlock(
                            section=SectionType.ARTICLE,
                            lang=Language.ENGLISH,
                            article_no=1,
                            article_title="First Article",
                            text="This is the content of the first article."
                        )
                    ]
                ),
                PageData(
                    page_index=1,
                    blocks=[
                        PageBlock(
                            section=SectionType.ARTICLE,
                            lang=Language.FRENCH,
                            article_no=1,
                            text="Ceci est le contenu du premier article."
                        ),
                        PageBlock(
                            section=SectionType.TABLE,
                            table_html="<table><tr><th>Name</th><th>Value</th></tr><tr><td>Test</td><td>123</td></tr></table>"
                        )
                    ]
                )
            ]
        )
        
        # Configure mock client
        mock_client = Mock()
        mock_client.extract_pages.return_value = mock_response
        mock_client_class.return_value = mock_client
        
        # Create temporary files
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            pdf_path = temp_path / "test.pdf"
            output_path = temp_path / "output"
            
            # Create dummy PDF file
            pdf_path.write_bytes(b"dummy pdf content")
            
            # Run extraction
            from click.testing import CliRunner
            runner = CliRunner()
            
            result = runner.invoke(extract_gazette, [
                '--pdf', str(pdf_path),
                '--out', str(output_path),
                '--batch-pages', '2',
                '--api-key', 'test-key',
                '--model', 'gemini-2.0-flash-exp'
            ])
            
            # Check command succeeded
            assert result.exit_code == 0
            
            # Check output files were created
            assert (output_path / "gazette.json").exists()
            assert (output_path / "preview.html").exists()
            assert (output_path / "run_report.json").exists()
            assert (output_path / "tables").exists()
            
            # Check gazette.json content
            with open(output_path / "gazette.json") as f:
                gazette_data = json.load(f)
            
            assert gazette_data["document"]["title"] == "Test Official Gazette"
            assert len(gazette_data["articles"]) == 1
            assert gazette_data["articles"][0]["article_no"] == 1
            assert gazette_data["articles"][0]["en"]["text"] == "This is the content of the first article."
            assert gazette_data["articles"][0]["fr"]["text"] == "Ceci est le contenu du premier article."
            
            # Check table was processed
            assert len(gazette_data["tables_index"]) == 1
            table_info = gazette_data["tables_index"][0]
            
            csv_path = output_path / table_info["csv"]
            html_path = output_path / table_info["html"]
            
            assert csv_path.exists()
            assert html_path.exists()
            
            # Check CSV content
            with open(csv_path) as f:
                csv_content = f.read()
                assert "Name,Value" in csv_content
                assert "Test,123" in csv_content
            
            # Check run report
            with open(output_path / "run_report.json") as f:
                report_data = json.load(f)
            
            assert report_data["total_pages"] == 2
            assert report_data["articles_by_language"]["en"] == 1
            assert report_data["articles_by_language"]["fr"] == 1
            assert report_data["tables_extracted"] == 1
    
    @patch('gazette_scraper.extractor.gemini_client.GeminiClient')
    def test_extract_gazette_api_error(self, mock_client_class: Mock) -> None:
        """Test extraction with API error."""
        # Configure mock client to raise error
        mock_client = Mock()
        mock_client.extract_pages.side_effect = RuntimeError("API Error")
        mock_client_class.return_value = mock_client
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            pdf_path = temp_path / "test.pdf"
            output_path = temp_path / "output"
            
            # Create dummy PDF file
            pdf_path.write_bytes(b"dummy pdf content")
            
            # Run extraction
            from click.testing import CliRunner
            runner = CliRunner()
            
            result = runner.invoke(extract_gazette, [
                '--pdf', str(pdf_path),
                '--out', str(output_path),
                '--api-key', 'test-key'
            ])
            
            # Check command failed
            assert result.exit_code != 0
            assert "API Error" in result.output
    
    def test_extract_gazette_missing_pdf(self) -> None:
        """Test extraction with missing PDF file."""
        from click.testing import CliRunner
        runner = CliRunner()
        
        result = runner.invoke(extract_gazette, [
            '--pdf', '/nonexistent/file.pdf',
            '--out', './output'
        ])
        
        # Check command failed due to missing file
        assert result.exit_code != 0
    
    @patch('gazette_scraper.extractor.gemini_client.GeminiClient')
    def test_extract_gazette_minimal_response(self, mock_client_class: Mock) -> None:
        """Test extraction with minimal response."""
        # Mock minimal response
        mock_response = GeminiResponse(
            doc=DocumentMetadata(source_filename="minimal.pdf"),
            pages=[
                PageData(
                    page_index=0,
                    blocks=[
                        PageBlock(
                            section=SectionType.OTHER,
                            text="Some unstructured content"
                        )
                    ]
                )
            ]
        )
        
        mock_client = Mock()
        mock_client.extract_pages.return_value = mock_response
        mock_client_class.return_value = mock_client
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            pdf_path = temp_path / "minimal.pdf"
            output_path = temp_path / "output"
            
            pdf_path.write_bytes(b"dummy pdf content")
            
            from click.testing import CliRunner
            runner = CliRunner()
            
            result = runner.invoke(extract_gazette, [
                '--pdf', str(pdf_path),
                '--out', str(output_path),
                '--api-key', 'test-key'
            ])
            
            assert result.exit_code == 0
            
            # Check output files
            assert (output_path / "gazette.json").exists()
            assert (output_path / "preview.html").exists()
            assert (output_path / "run_report.json").exists()
            
            # Check content
            with open(output_path / "gazette.json") as f:
                gazette_data = json.load(f)
            
            assert gazette_data["document"]["source_filename"] == "minimal.pdf"
            assert len(gazette_data["articles"]) == 0  # No articles found
            assert len(gazette_data["tables_index"]) == 0  # No tables found
            assert gazette_data["run_stats"]["pages"] == 1
    
    def test_cli_help(self) -> None:
        """Test CLI help output."""
        from click.testing import CliRunner
        runner = CliRunner()
        
        result = runner.invoke(extract_gazette, ['--help'])
        
        assert result.exit_code == 0
        assert "Extract structured content" in result.output
        assert "--pdf" in result.output
        assert "--out" in result.output
        assert "--batch-pages" in result.output
        assert "--api-key" in result.output
        assert "--model" in result.output


class TestMainCLIIntegration:
    """Test integration with main CLI."""
    
    def test_extract_command_available(self) -> None:
        """Test that extract command is available in main CLI."""
        from gazette_scraper.__main__ import cli
        from click.testing import CliRunner
        
        runner = CliRunner()
        result = runner.invoke(cli, ['--help'])
        
        assert result.exit_code == 0
        assert "extract" in result.output
    
    def test_extract_command_help(self) -> None:
        """Test extract command help."""
        from gazette_scraper.__main__ import cli
        from click.testing import CliRunner
        
        runner = CliRunner()
        result = runner.invoke(cli, ['extract', '--help'])
        
        assert result.exit_code == 0
        assert "Extract structured content" in result.output
