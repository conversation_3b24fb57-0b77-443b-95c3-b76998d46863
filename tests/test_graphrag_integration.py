"""Tests for GraphRAG integration components."""

import json
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

import pandas as pd
import pytest

from gazette_scraper.extractor.schema import ExtractedDocument, DocumentMetadata, ExtractedArticle, LanguageText, RunStats
from gazette_scraper.graphrag.byog_mapper import B<PERSON><PERSON><PERSON>apper
from gazette_scraper.graphrag.models import BYOGData, TextUnit, Entity, Relationship


class TestBYOGMapper:
    """Test BYOG data mapper functionality."""
    
    def test_chunk_creation(self):
        """Test text chunking functionality."""
        mapper = BYOGMapper(chunk_size=50, chunk_overlap=10)
        
        # Mock encoding
        with patch.object(mapper.encoding, 'encode') as mock_encode, \
             patch.object(mapper.encoding, 'decode') as mock_decode:
            
            # Simulate tokens
            mock_encode.return_value = list(range(100))  # 100 tokens
            mock_decode.side_effect = lambda tokens: f"chunk_{len(tokens)}"
            
            chunks = mapper._create_chunks("test text")
            
            # Should create overlapping chunks
            assert len(chunks) > 1
            assert all("chunk_" in chunk for chunk in chunks)
    
    def test_entity_extraction(self):
        """Test entity extraction from text."""
        mapper = BYOGMapper()
        
        # Test article references
        text = "According to Article 5 and Ingingo ya 10, the Ministry of Justice shall..."
        entities = mapper._extract_entities(text, "en", "test_unit_id")
        
        # Should extract article and institution entities
        article_entities = [e for e in entities if e.type == "Article"]
        institution_entities = [e for e in entities if e.type == "Institution"]
        
        assert len(article_entities) >= 1
        assert len(institution_entities) >= 1
        assert all(e.text_unit_ids == ["test_unit_id"] for e in entities)
    
    def test_process_document(self):
        """Test processing a complete document."""
        mapper = BYOGMapper(chunk_size=100, chunk_overlap=20)
        
        # Create test document
        doc = ExtractedDocument(
            document=DocumentMetadata(
                title="Test Gazette",
                date_iso="2024-01-01",
                source_filename="test.pdf"
            ),
            articles=[
                ExtractedArticle(
                    article_no=1,
                    en=LanguageText(text="Article 1: This is a test article about cooperatives.", pages=[1]),
                    fr=LanguageText(text="Article 1: Ceci est un article test sur les coopératives.", pages=[1]),
                    rw=LanguageText(text="Ingingo ya 1: Iyi ni ingingo y'ikizamini ku bwiyunge.", pages=[1]),
                    pages=[1]
                )
            ],
            tables_index=[],
            run_stats=RunStats(pages=1, articles_detected=1)
        )
        
        # Process document
        byog_data = mapper._process_document(doc, "test_tenant")
        
        # Verify results
        assert len(byog_data.text_units) >= 3  # At least one per language
        assert len(byog_data.entities) > 0
        assert all(tu.metadata["tenant_id"] == "test_tenant" for tu in byog_data.text_units)
        assert all(tu.metadata["language"] in ["en", "fr", "rw"] for tu in byog_data.text_units)
    
    def test_translation_relationships(self):
        """Test creation of translation relationships."""
        mapper = BYOGMapper()
        
        # Create test articles with entities
        articles = [
            Mock(article_no=1),
            Mock(article_no=2)
        ]
        
        # Create BYOG data with entities
        byog_data = BYOGData()
        
        # Add entities for the same article in different languages
        entity_en = Entity(
            title="Article 1",
            description="English version",
            type="Article",
            metadata={"language": "en", "article_number": "1"}
        )
        entity_fr = Entity(
            title="Article 1",
            description="French version",
            type="Article",
            metadata={"language": "fr", "article_number": "1"}
        )
        
        byog_data.entities = [entity_en, entity_fr]
        
        # Add translation relationships
        mapper._add_translation_relationships(articles, byog_data)
        
        # Should create translation relationship
        translation_rels = [r for r in byog_data.relationships 
                          if r.metadata.get("relationship_type") == "same_as_translation"]
        
        assert len(translation_rels) == 1
        assert translation_rels[0].weight == 2.0  # RELATIONSHIP_WEIGHTS["same_as_translation"]


class TestVoyageEmbedder:
    """Test Voyage embedder functionality."""
    
    @patch('gazette_scraper.graphrag.embeddings.voyageai.Client')
    @patch('gazette_scraper.graphrag.embeddings.MongoClient')
    def test_embedder_initialization(self, mock_mongo, mock_voyage):
        """Test embedder initialization."""
        from gazette_scraper.graphrag.embeddings import VoyageEmbedder
        
        # Mock clients
        mock_voyage_instance = Mock()
        mock_voyage.return_value = mock_voyage_instance
        
        mock_mongo_instance = Mock()
        mock_mongo.return_value = mock_mongo_instance
        
        # Initialize embedder
        embedder = VoyageEmbedder(
            voyage_api_key="test_key",
            mongodb_uri="mongodb://test",
            model="voyage-context-3",
            output_dimension=1024
        )
        
        assert embedder.model == "voyage-context-3"
        assert embedder.output_dimension == 1024
        mock_voyage.assert_called_once_with(api_key="test_key")
        mock_mongo.assert_called_once_with("mongodb://test")
    
    @patch('gazette_scraper.graphrag.embeddings.voyageai.Client')
    @patch('gazette_scraper.graphrag.embeddings.MongoClient')
    def test_contextualized_embeddings(self, mock_mongo, mock_voyage):
        """Test contextualized embedding generation."""
        from gazette_scraper.graphrag.embeddings import VoyageEmbedder
        
        # Mock Voyage response
        mock_result = Mock()
        mock_result.embeddings = [[0.1, 0.2, 0.3], [0.4, 0.5, 0.6]]
        
        mock_response = Mock()
        mock_response.results = [mock_result]
        
        mock_voyage_instance = Mock()
        mock_voyage_instance.contextualized_embed.return_value = mock_response
        mock_voyage.return_value = mock_voyage_instance
        
        # Mock MongoDB
        mock_mongo.return_value = Mock()
        
        embedder = VoyageEmbedder(
            voyage_api_key="test_key",
            mongodb_uri="mongodb://test"
        )
        
        # Test embedding
        documents_chunks = [["chunk1", "chunk2"]]
        result = embedder._get_contextualized_embeddings(documents_chunks)
        
        assert len(result) == 1
        assert len(result[0]) == 2
        assert result[0][0] == [0.1, 0.2, 0.3]
        
        # Verify API call
        mock_voyage_instance.contextualized_embed.assert_called_once_with(
            inputs=documents_chunks,
            model="voyage-context-3",
            input_type="document",
            output_dimension=1024
        )


class TestGraphRAGConfig:
    """Test GraphRAG configuration functionality."""
    
    def test_project_initialization(self):
        """Test GraphRAG project initialization."""
        from gazette_scraper.graphrag.config import GraphRAGConfig
        
        with tempfile.TemporaryDirectory() as temp_dir:
            project_root = Path(temp_dir) / "test_project"
            config = GraphRAGConfig(project_root)
            
            # Initialize project
            config.initialize_project()
            
            # Verify files created
            assert (project_root / "settings.yaml").exists()
            assert (project_root / ".env").exists()
            assert (project_root / "output").exists()
            
            # Verify settings content
            import yaml
            with open(project_root / "settings.yaml") as f:
                settings = yaml.safe_load(f)
            
            assert "models" in settings
            assert "workflows" in settings
            assert "create_communities" in settings["workflows"]
            assert "create_community_reports" in settings["workflows"]
    
    def test_vector_store_enablement(self):
        """Test enabling vector store configuration."""
        from gazette_scraper.graphrag.config import GraphRAGConfig
        
        with tempfile.TemporaryDirectory() as temp_dir:
            project_root = Path(temp_dir) / "test_project"
            config = GraphRAGConfig(project_root)
            
            # Initialize and enable vector store
            config.initialize_project()
            config.enable_vector_store("lancedb")
            
            # Verify vector store configuration
            import yaml
            with open(project_root / "settings.yaml") as f:
                settings = yaml.safe_load(f)
            
            assert "vector_store" in settings
            assert settings["vector_store"]["type"] == "lancedb"
            assert "generate_text_embeddings" in settings["workflows"]


class TestGraphRAGQueryService:
    """Test GraphRAG query service functionality."""
    
    @patch('gazette_scraper.graphrag.query_service.load_config')
    @patch('gazette_scraper.graphrag.query_service.pd.read_parquet')
    def test_query_service_initialization(self, mock_read_parquet, mock_load_config):
        """Test query service initialization."""
        from gazette_scraper.graphrag.query_service import GraphRAGQueryService
        from gazette_scraper.graphrag.embeddings import VoyageEmbedder
        
        # Mock configuration
        mock_config = Mock()
        mock_load_config.return_value = mock_config
        
        # Mock parquet data
        mock_read_parquet.side_effect = [
            pd.DataFrame({"id": ["e1"], "title": ["Entity 1"]}),  # entities
            pd.DataFrame({"id": ["r1"], "source": ["e1"]}),      # relationships
            pd.DataFrame({"id": ["c1"], "title": ["Community 1"]}),  # communities
            pd.DataFrame({"id": ["cr1"], "content": ["Report 1"]})   # community_reports
        ]
        
        # Mock embedder
        mock_embedder = Mock(spec=VoyageEmbedder)
        
        with tempfile.TemporaryDirectory() as temp_dir:
            project_root = Path(temp_dir)
            (project_root / "output").mkdir()
            
            # Initialize service
            service = GraphRAGQueryService(
                graphrag_project_root=project_root,
                voyage_embedder=mock_embedder
            )
            
            assert service.project_root == project_root
            assert service.voyage_embedder == mock_embedder
            assert len(service.entities) == 1
            assert len(service.relationships) == 1


@pytest.fixture
def sample_gazette_json():
    """Create sample gazette JSON for testing."""
    return {
        "document": {
            "title": "Official Gazette n° 1 of 01/01/2024",
            "date_iso": "2024-01-01",
            "source_filename": "test_gazette.pdf"
        },
        "articles": [
            {
                "article_no": 1,
                "en": {
                    "text": "Article 1: This law establishes the framework for cooperative registration.",
                    "pages": [1]
                },
                "fr": {
                    "text": "Article 1: Cette loi établit le cadre pour l'enregistrement des coopératives.",
                    "pages": [1]
                },
                "rw": {
                    "text": "Ingingo ya 1: Iri tegeko rishyiraho urwego rw'iyandikisha ry'ubwiyunge.",
                    "pages": [1]
                },
                "pages": [1]
            }
        ],
        "tables_index": [],
        "run_stats": {
            "pages": 1,
            "articles_detected": 1,
            "tables_detected": 0,
            "anomalies": []
        }
    }


def test_end_to_end_byog_processing(sample_gazette_json):
    """Test end-to-end BYOG processing with sample data."""
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create input directory with sample gazette
        input_dir = Path(temp_dir) / "input"
        input_dir.mkdir()
        
        gazette_file = input_dir / "gazette.json"
        with open(gazette_file, 'w', encoding='utf-8') as f:
            json.dump(sample_gazette_json, f)
        
        # Create output directory
        output_dir = Path(temp_dir) / "output"
        
        # Process with BYOG mapper
        mapper = BYOGMapper(chunk_size=100, chunk_overlap=20)
        byog_data = mapper.process_gazette_files(
            input_dir=input_dir,
            output_dir=output_dir,
            tenant_id="test_tenant"
        )
        
        # Verify results
        assert len(byog_data.text_units) >= 3  # At least one per language
        assert len(byog_data.entities) > 0
        assert len(byog_data.relationships) >= 0
        
        # Verify parquet files created
        assert (output_dir / "text_units.parquet").exists()
        assert (output_dir / "entities.parquet").exists()
        assert (output_dir / "relationships.parquet").exists()
        
        # Verify parquet content
        text_units_df = pd.read_parquet(output_dir / "text_units.parquet")
        assert len(text_units_df) >= 3
        assert "tenant_id" in text_units_df.columns
        assert all(text_units_df["tenant_id"] == "test_tenant")


if __name__ == "__main__":
    pytest.main([__file__])
