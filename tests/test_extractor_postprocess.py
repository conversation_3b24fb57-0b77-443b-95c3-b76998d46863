"""Tests for extractor post-processing."""

from __future__ import annotations

import tempfile
from pathlib import Path
from unittest.mock import Mock

import pytest

from gazette_scraper.extractor.postprocess import PostProcessor
from gazette_scraper.extractor.schema import (
    ArticleBlock,
    DocumentMetadata,
    GeminiResponse,
    Language,
    PageBlock,
    PageData,
    SectionType,
)


class TestPostProcessor:
    """Test PostProcessor functionality."""
    
    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.processor = PostProcessor(similarity_threshold=0.9)
    
    def test_strip_headers_footers(self) -> None:
        """Test header and footer stripping."""
        # Create mock pages with common headers/footers
        pages = [
            Mock(blocks=[
                Mock(section=SectionType.TITLE, text="Official Gazette Header\nActual content\nPage 1 Footer"),
                Mock(section=SectionType.ARTICLE, text="Official Gazette Header\nArticle content\nPage 1 Footer")
            ]),
            <PERSON><PERSON>(blocks=[
                Mock(section=SectionType.TITLE, text="Official Gazette Header\nDifferent content\nPage 2 Footer"),
                <PERSON>ck(section=SectionType.ARTICLE, text="Official Gazette Header\nMore article content\nPage 2 Footer")
            ]),
            Mock(blocks=[
                Mock(section=SectionType.TITLE, text="Official Gazette Header\nThird content\nPage 3 Footer"),
                Mock(section=SectionType.ARTICLE, text="Official Gazette Header\nFinal article content\nPage 3 Footer")
            ])
        ]
        
        cleaned_pages = self.processor._strip_headers_footers(pages)
        
        # Check that common strings are removed
        assert len(cleaned_pages) == 3
        for page in cleaned_pages:
            for block in page.blocks:
                if hasattr(block, 'text') and block.text:
                    assert "Official Gazette Header" not in block.text
                    assert "Footer" not in block.text
    
    def test_find_common_strings(self) -> None:
        """Test finding common strings across pages."""
        strings = [
            "Official Gazette of Rwanda",
            "Official Gazette of Rwanda",
            "Official Gazette of Rwanda",
            "Different content",
            "Official Gazette of Rwanda"
        ]
        
        common = self.processor._find_common_strings(strings)
        
        assert "Official Gazette of Rwanda" in common
        assert "Different content" not in common
    
    def test_remove_common_strings(self) -> None:
        """Test removing common strings from text."""
        text = "Official Gazette Header\nActual content line 1\nActual content line 2\nPage Footer"
        common_strings = ["Official Gazette Header", "Page Footer"]
        
        cleaned = self.processor._remove_common_strings(text, common_strings)
        
        assert "Official Gazette Header" not in cleaned
        assert "Page Footer" not in cleaned
        assert "Actual content line 1" in cleaned
        assert "Actual content line 2" in cleaned
    
    def test_extract_article_blocks(self) -> None:
        """Test extracting article blocks from pages."""
        pages = [
            PageData(
                page_index=0,
                blocks=[
                    PageBlock(
                        section=SectionType.ARTICLE,
                        lang=Language.ENGLISH,
                        article_no=1,
                        article_title="First Article",
                        text="This is the first article content."
                    ),
                    PageBlock(
                        section=SectionType.TITLE,
                        text="Some title"
                    )
                ]
            ),
            PageData(
                page_index=1,
                blocks=[
                    PageBlock(
                        section=SectionType.ARTICLE,
                        lang=Language.FRENCH,
                        article_no=1,
                        text="Ceci est le contenu du premier article."
                    ),
                    PageBlock(
                        section=SectionType.ARTICLE,
                        lang=Language.ENGLISH,
                        article_no=2,
                        text="This is the second article."
                    )
                ]
            )
        ]
        
        article_blocks = self.processor._extract_article_blocks(pages)
        
        assert len(article_blocks) == 3
        
        # Check first article block
        assert article_blocks[0].article_no == 1
        assert article_blocks[0].lang == Language.ENGLISH
        assert article_blocks[0].text == "This is the first article content."
        assert article_blocks[0].page_index == 0
        
        # Check second article block (French)
        assert article_blocks[1].article_no == 1
        assert article_blocks[1].lang == Language.FRENCH
        assert article_blocks[1].text == "Ceci est le contenu du premier article."
        assert article_blocks[1].page_index == 1
        
        # Check third article block
        assert article_blocks[2].article_no == 2
        assert article_blocks[2].lang == Language.ENGLISH
        assert article_blocks[2].text == "This is the second article."
        assert article_blocks[2].page_index == 1
    
    def test_align_articles(self) -> None:
        """Test aligning articles across languages."""
        article_blocks = [
            ArticleBlock(
                article_no=1,
                lang=Language.ENGLISH,
                text="English content for article 1",
                page_index=0
            ),
            ArticleBlock(
                article_no=1,
                lang=Language.FRENCH,
                text="Contenu français pour l'article 1",
                page_index=0
            ),
            ArticleBlock(
                article_no=1,
                lang=Language.KINYARWANDA,
                text="Ibiri mu ngingo ya mbere",
                page_index=1
            ),
            ArticleBlock(
                article_no=2,
                lang=Language.ENGLISH,
                text="English content for article 2",
                page_index=1
            )
        ]
        
        aligned_articles = self.processor._align_articles(article_blocks)
        
        assert len(aligned_articles) == 2
        
        # Check first article (should have all three languages)
        article1 = aligned_articles[0]
        assert article1.article_no == 1
        assert article1.en is not None
        assert article1.fr is not None
        assert article1.rw is not None
        assert article1.en.text == "English content for article 1"
        assert article1.fr.text == "Contenu français pour l'article 1"
        assert article1.rw.text == "Ibiri mu ngingo ya mbere"
        assert set(article1.pages) == {0, 1}
        
        # Check second article (should have only English)
        article2 = aligned_articles[1]
        assert article2.article_no == 2
        assert article2.en is not None
        assert article2.fr is None
        assert article2.rw is None
        assert article2.en.text == "English content for article 2"
        assert article2.pages == [1]
    
    def test_table_to_csv(self) -> None:
        """Test converting HTML table to CSV."""
        from bs4 import BeautifulSoup
        
        html = """
        <table>
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Age</th>
                    <th>City</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>John Doe</td>
                    <td>30</td>
                    <td>Kigali</td>
                </tr>
                <tr>
                    <td>Jane Smith</td>
                    <td>25</td>
                    <td>Butare</td>
                </tr>
            </tbody>
        </table>
        """
        
        soup = BeautifulSoup(html, 'html.parser')
        table = soup.find('table')
        
        csv_content = self.processor._table_to_csv(table)
        
        lines = csv_content.strip().split('\n')
        assert len(lines) == 3  # Header + 2 data rows
        
        # Check header
        assert "Name,Age,City" in lines[0]
        
        # Check data rows
        assert "John Doe,30,Kigali" in lines[1]
        assert "Jane Smith,25,Butare" in lines[2]
    
    def test_table_with_colspan(self) -> None:
        """Test table with colspan handling."""
        from bs4 import BeautifulSoup
        
        html = """
        <table>
            <tr>
                <td colspan="2">Merged Cell</td>
                <td>Normal Cell</td>
            </tr>
            <tr>
                <td>Cell 1</td>
                <td>Cell 2</td>
                <td>Cell 3</td>
            </tr>
        </table>
        """
        
        soup = BeautifulSoup(html, 'html.parser')
        table = soup.find('table')
        
        csv_content = self.processor._table_to_csv(table)
        
        lines = csv_content.strip().split('\n')
        assert len(lines) == 2
        
        # First row should have empty cell for colspan
        assert "Merged Cell,,Normal Cell" in lines[0]
        assert "Cell 1,Cell 2,Cell 3" in lines[1]
    
    def test_process_response_integration(self) -> None:
        """Test full response processing integration."""
        # Create a mock response
        doc = DocumentMetadata(
            title="Test Gazette",
            source_filename="test.pdf"
        )
        
        pages = [
            PageData(
                page_index=0,
                blocks=[
                    PageBlock(
                        section=SectionType.ARTICLE,
                        lang=Language.ENGLISH,
                        article_no=1,
                        text="English article content"
                    ),
                    PageBlock(
                        section=SectionType.TABLE,
                        table_html="<table><tr><td>Test</td></tr></table>"
                    )
                ]
            )
        ]
        
        response = GeminiResponse(doc=doc, pages=pages)
        
        # Process with temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            output_dir = Path(temp_dir)
            
            document = self.processor.process_response(response, output_dir)
            
            # Check document structure
            assert document.document.title == "Test Gazette"
            assert len(document.articles) == 1
            assert len(document.tables_index) == 1
            
            # Check article
            article = document.articles[0]
            assert article.article_no == 1
            assert article.en is not None
            assert article.en.text == "English article content"
            
            # Check table files were created
            tables_dir = output_dir / "tables"
            assert tables_dir.exists()
            
            table_info = document.tables_index[0]
            csv_path = output_dir / table_info.csv
            html_path = output_dir / table_info.html
            
            assert csv_path.exists()
            assert html_path.exists()
            
            # Check CSV content
            with open(csv_path) as f:
                csv_content = f.read()
                assert "Test" in csv_content
