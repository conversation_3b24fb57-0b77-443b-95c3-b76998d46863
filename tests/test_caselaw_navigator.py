"""Tests for case-law navigation engine."""

import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch
from pathlib import Path

from gazette_scraper.caselaw.models import CaseLawConfig, CourtNode, NodeType, NavigationStatus
from gazette_scraper.caselaw.navigator import Case<PERSON>awNavigator


@pytest.fixture
def caselaw_config():
    """Test configuration for case-law scraping."""
    return CaseLawConfig(
        output_dir=Path("/tmp/test_caselaw"),
        browser_timeout=5000,
        headless=True,
        debug_mode=False,
    )


@pytest.fixture
async def mock_navigator(caselaw_config):
    """Mock navigator with playwright mocked."""
    with patch('gazette_scraper.caselaw.navigator.async_playwright') as mock_playwright:
        navigator = CaseLawNavigator(caselaw_config)

        # Mock playwright components
        mock_playwright_instance = AsyncMock()
        mock_browser = AsyncMock()
        mock_context = AsyncMock()
        mock_page = AsyncMock()

        mock_playwright.return_value.start.return_value = mock_playwright_instance
        mock_playwright_instance.chromium.launch.return_value = mock_browser
        mock_browser.new_context.return_value = mock_context
        mock_context.new_page.return_value = mock_page

        navigator.playwright = mock_playwright_instance
        navigator.browser = mock_browser
        navigator.contexts = [mock_context]

        yield navigator, mock_page


@pytest.mark.asyncio
class TestCaseLawNavigator:
    """Test cases for the case-law navigator."""

    async def test_navigator_initialization(self, caselaw_config):
        """Test navigator initialization."""
        navigator = CaseLawNavigator(caselaw_config)
        assert navigator.config == caselaw_config
        assert navigator.playwright is None
        assert navigator.browser is None
        assert len(navigator.contexts) == 0

    async def test_start_and_close(self, caselaw_config):
        """Test navigator start and close lifecycle."""
        with patch('gazette_scraper.caselaw.navigator.async_playwright') as mock_playwright:
            navigator = CaseLawNavigator(caselaw_config)

            # Mock playwright
            mock_playwright_instance = AsyncMock()
            mock_browser = AsyncMock()
            mock_context = AsyncMock()

            mock_playwright.return_value.start.return_value = mock_playwright_instance
            mock_playwright_instance.chromium.launch.return_value = mock_browser
            mock_browser.new_context.return_value = mock_context

            # Test start
            await navigator.start()

            assert navigator.playwright == mock_playwright_instance
            assert navigator.browser == mock_browser
            assert len(navigator.contexts) == 1

            # Test close
            await navigator.close()

            mock_context.close.assert_called_once()
            mock_browser.close.assert_called_once()
            mock_playwright_instance.stop.assert_called_once()

    async def test_discover_courts_success(self, mock_navigator):
        """Test successful court discovery."""
        navigator, mock_page = mock_navigator

        # Mock page navigation and elements
        mock_page.goto.return_value = None
        mock_page.url = "https://amategeko.gov.rw/laws/judgement/2"

        # Mock court elements
        mock_element = AsyncMock()
        mock_element.text_content.return_value = "Supreme Court (15)"
        mock_page.query_selector_all.return_value = [mock_element]

        with patch.object(navigator, '_extract_node_text', return_value="Supreme Court"):
            with patch.object(navigator, '_extract_document_count', return_value=15):
                with patch.object(navigator, '_get_element_selector', return_value=".court-node"):
                    courts = await navigator._discover_courts(mock_page)

        assert len(courts) == 1
        assert courts[0].court_name == "Supreme Court"
        assert courts[0].document_count == 15
        assert courts[0].node_type == NodeType.COURT

    async def test_discover_years_success(self, mock_navigator):
        """Test successful year discovery."""
        navigator, mock_page = mock_navigator

        # Create test court node
        court = CourtNode(
            court_name="Supreme Court",
            node_type=NodeType.COURT,
            full_path="Supreme Court",
        )

        # Mock year elements
        mock_element = AsyncMock()
        mock_element.text_content.return_value = "2023 (10)"
        mock_page.query_selector_all.return_value = [mock_element]

        with patch.object(navigator, '_extract_node_text', return_value="2023"):
            with patch.object(navigator, '_extract_document_count', return_value=10):
                with patch.object(navigator, '_get_element_selector', return_value=".year-node"):
                    years = await navigator._discover_years(mock_page, court)

        assert len(years) == 1
        assert years[0].year == 2023
        assert years[0].document_count == 10
        assert years[0].node_type == NodeType.YEAR
        assert years[0].court_name == "Supreme Court"

    async def test_expand_node_success(self, mock_navigator):
        """Test successful node expansion."""
        navigator, mock_page = mock_navigator

        # Create test node
        node = CourtNode(
            court_name="Supreme Court",
            node_type=NodeType.COURT,
            full_path="Supreme Court",
            click_selector=".court-node",
        )

        # Mock expand element
        mock_expand_element = AsyncMock()
        mock_page.wait_for_selector.return_value = mock_expand_element

        with patch.object(navigator, '_verify_node_expanded', return_value=True):
            result = await navigator._expand_node(mock_page, node)

        assert result is True
        assert node.status == NavigationStatus.EXPANDED
        mock_expand_element.click.assert_called_once()

    async def test_expand_node_failure(self, mock_navigator):
        """Test node expansion failure."""
        navigator, mock_page = mock_navigator

        # Create test node
        node = CourtNode(
            court_name="Supreme Court",
            node_type=NodeType.COURT,
            full_path="Supreme Court",
            click_selector=".court-node",
        )

        # Mock no expand element found
        mock_page.wait_for_selector.return_value = None

        result = await navigator._expand_node(mock_page, node)

        assert result is False
        assert node.status == NavigationStatus.FAILED

    async def test_extract_case_list_success(self, mock_navigator):
        """Test successful case list extraction."""
        navigator, mock_page = mock_navigator

        # Create test day node
        day_node = CourtNode(
            court_name="Supreme Court",
            year=2023,
            month=1,
            day=15,
            node_type=NodeType.DAY,
            full_path="Supreme Court/2023/January/Day_15",
        )

        # Mock case elements
        mock_case_element = AsyncMock()
        mock_case_element.get_attribute.return_value = "/case/123"
        mock_page.query_selector_all.return_value = [mock_case_element]
        mock_page.url = "https://amategeko.gov.rw/laws/judgement/2"

        with patch.object(navigator, '_extract_node_text', return_value="Test Case v. Example"):
            with patch.object(navigator, '_generate_filename', return_value="test_case.pdf"):
                cases = await navigator._extract_case_list(mock_page, day_node)

        assert len(cases) == 1
        assert cases[0].case_title == "Test Case v. Example"
        assert cases[0].court_name == "Supreme Court"
        assert cases[0].year == 2023
        assert cases[0].month == 1
        assert cases[0].day == 15

    async def test_navigate_to_node_basic(self, mock_navigator):
        """Test basic navigation to node."""
        navigator, mock_page = mock_navigator

        # Create test node
        node = CourtNode(
            court_name="Supreme Court",
            year=2023,
            month=1,
            day=15,
            node_type=NodeType.DAY,
            full_path="Supreme Court/2023/January/Day_15",
        )

        result = await navigator._navigate_to_node(mock_page, node)

        # Should return True for basic implementation
        assert result is True

    async def test_generate_session_id(self, caselaw_config):
        """Test session ID generation."""
        navigator = CaseLawNavigator(caselaw_config)
        session_id = navigator._generate_session_id()

        assert len(session_id) == 8
        assert session_id.isalnum()

    async def test_resolve_url_absolute(self, caselaw_config):
        """Test URL resolution for absolute URLs."""
        navigator = CaseLawNavigator(caselaw_config)
        base_url = "https://amategeko.gov.rw/laws/judgement/2"

        # Test absolute URL
        absolute_url = "https://example.com/file.pdf"
        result = navigator._resolve_url(absolute_url, base_url)
        assert result == absolute_url

    async def test_resolve_url_relative(self, caselaw_config):
        """Test URL resolution for relative URLs."""
        navigator = CaseLawNavigator(caselaw_config)
        base_url = "https://amategeko.gov.rw/laws/judgement/2"

        # Test root-relative URL
        root_relative = "/files/case.pdf"
        result = navigator._resolve_url(root_relative, base_url)
        assert result == "https://amategeko.gov.rw/files/case.pdf"

        # Test relative URL
        relative = "case.pdf"
        result = navigator._resolve_url(relative, base_url)
        assert result == "https://amategeko.gov.rw/laws/judgement/case.pdf"

    async def test_parse_month_names(self, caselaw_config):
        """Test month name parsing."""
        navigator = CaseLawNavigator(caselaw_config)

        test_cases = [
            ("January", 1),
            ("Feb", 2),
            ("mar", 3),
            ("APRIL", 4),
            ("05", 5),
            ("12", 12),
            ("invalid", None),
        ]

        for month_text, expected in test_cases:
            result = navigator._parse_month(month_text)
            assert result == expected

    async def test_generate_filename(self, caselaw_config):
        """Test filename generation."""
        navigator = CaseLawNavigator(caselaw_config)

        day_node = CourtNode(
            court_name="Supreme Court",
            year=2023,
            month=1,
            day=15,
            node_type=NodeType.DAY,
            full_path="Supreme Court/2023/January/Day_15",
        )

        case_title = "Test Case v. Example Corp."
        filename = navigator._generate_filename(case_title, day_node)

        assert filename.endswith(".pdf")
        assert "Supreme_Court" in filename
        assert "2023" in filename
        assert "01" in filename
        assert "15" in filename
        assert "Test_Case" in filename


@pytest.mark.asyncio
class TestCaseLawNavigatorIntegration:
    """Integration tests for case-law navigator."""

    async def test_full_discovery_workflow_mock(self, mock_navigator):
        """Test the full discovery workflow with mocked components."""
        navigator, mock_page = mock_navigator

        # Mock the discovery methods to return test data
        test_courts = [
            CourtNode(
                court_name="Supreme Court",
                node_type=NodeType.COURT,
                full_path="Supreme Court",
                document_count=10,
            )
        ]

        test_years = [
            CourtNode(
                court_name="Supreme Court",
                year=2023,
                node_type=NodeType.YEAR,
                full_path="Supreme Court/2023",
                document_count=5,
            )
        ]

        test_months = [
            CourtNode(
                court_name="Supreme Court",
                year=2023,
                month=1,
                node_type=NodeType.MONTH,
                full_path="Supreme Court/2023/January",
                document_count=3,
            )
        ]

        test_days = [
            CourtNode(
                court_name="Supreme Court",
                year=2023,
                month=1,
                day=15,
                node_type=NodeType.DAY,
                full_path="Supreme Court/2023/January/Day_15",
                document_count=2,
            )
        ]

        with patch.object(navigator, '_discover_courts', return_value=test_courts):
            with patch.object(navigator, '_discover_years', return_value=test_years):
                with patch.object(navigator, '_discover_months', return_value=test_months):
                    with patch.object(navigator, '_discover_days', return_value=test_days):
                        with patch.object(navigator, '_expand_node', return_value=True):
                            nodes = await navigator.discover_court_tree()

        # Verify all nodes were discovered
        assert len(nodes) == 4  # 1 court + 1 year + 1 month + 1 day

        court_nodes = [n for n in nodes if n.node_type == NodeType.COURT]
        year_nodes = [n for n in nodes if n.node_type == NodeType.YEAR]
        month_nodes = [n for n in nodes if n.node_type == NodeType.MONTH]
        day_nodes = [n for n in nodes if n.node_type == NodeType.DAY]

        assert len(court_nodes) == 1
        assert len(year_nodes) == 1
        assert len(month_nodes) == 1
        assert len(day_nodes) == 1

    async def test_error_handling_in_discovery(self, mock_navigator):
        """Test error handling during discovery."""
        navigator, mock_page = mock_navigator

        # Mock courts discovery to succeed
        test_courts = [
            CourtNode(
                court_name="Supreme Court",
                node_type=NodeType.COURT,
                full_path="Supreme Court",
            )
        ]

        # Mock years discovery to fail
        async def failing_discover_years(page, court):
            raise Exception("Network error")

        with patch.object(navigator, '_discover_courts', return_value=test_courts):
            with patch.object(navigator, '_discover_years', side_effect=failing_discover_years):
                with patch.object(navigator, '_expand_node', return_value=True):
                    nodes = await navigator.discover_court_tree()

        # Should still return the successfully discovered courts
        assert len(nodes) == 1
        assert nodes[0].node_type == NodeType.COURT