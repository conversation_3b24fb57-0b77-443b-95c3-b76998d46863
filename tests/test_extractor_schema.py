"""Tests for extractor schema validation."""

from __future__ import annotations

import pytest
from pydantic import Validation<PERSON>rror

from gazette_scraper.extractor.schema import (
    ArticleBlock,
    DocumentMetadata,
    ExtractedArticle,
    ExtractedDocument,
    GeminiResponse,
    Language,
    LanguageText,
    PageBlock,
    PageData,
    RunStats,
    SectionType,
    TableInfo,
)


class TestPageBlock:
    """Test PageBlock schema."""
    
    def test_valid_page_block(self) -> None:
        """Test valid page block creation."""
        block = PageBlock(
            section=SectionType.ARTICLE,
            lang=Language.ENGLISH,
            article_no=1,
            article_title="Test Article",
            text="This is test content.",
            uncertain=False
        )
        
        assert block.section == SectionType.ARTICLE
        assert block.lang == Language.ENGLISH
        assert block.article_no == 1
        assert block.article_title == "Test Article"
        assert block.text == "This is test content."
        assert block.uncertain is False
    
    def test_table_block(self) -> None:
        """Test table block creation."""
        block = PageBlock(
            section=SectionType.TABLE,
            table_html="<table><tr><td>Test</td></tr></table>"
        )
        
        assert block.section == SectionType.TABLE
        assert block.table_html == "<table><tr><td>Test</td></tr></table>"
        assert block.lang is None
        assert block.text is None
    
    def test_minimal_block(self) -> None:
        """Test minimal block with only required fields."""
        block = PageBlock(section=SectionType.OTHER)
        
        assert block.section == SectionType.OTHER
        assert block.lang is None
        assert block.article_no is None
        assert block.text is None
        assert block.uncertain is False


class TestPageData:
    """Test PageData schema."""
    
    def test_valid_page_data(self) -> None:
        """Test valid page data creation."""
        blocks = [
            PageBlock(section=SectionType.TITLE, text="Title"),
            PageBlock(section=SectionType.ARTICLE, lang=Language.ENGLISH, article_no=1, text="Content")
        ]
        
        page = PageData(page_index=0, blocks=blocks)
        
        assert page.page_index == 0
        assert len(page.blocks) == 2
        assert page.errors == []
    
    def test_page_with_errors(self) -> None:
        """Test page data with errors."""
        page = PageData(
            page_index=1,
            blocks=[],
            errors=["Failed to parse section", "Missing language detection"]
        )
        
        assert page.page_index == 1
        assert len(page.errors) == 2


class TestGeminiResponse:
    """Test GeminiResponse schema."""
    
    def test_valid_response(self) -> None:
        """Test valid Gemini response."""
        doc = DocumentMetadata(
            title="Test Gazette",
            date_iso="2024-01-15",
            source_filename="test.pdf"
        )
        
        pages = [
            PageData(page_index=0, blocks=[
                PageBlock(section=SectionType.TITLE, text="Title")
            ])
        ]
        
        response = GeminiResponse(doc=doc, pages=pages)
        
        assert response.doc.title == "Test Gazette"
        assert len(response.pages) == 1
    
    def test_response_without_doc(self) -> None:
        """Test response without document metadata."""
        pages = [PageData(page_index=0, blocks=[])]
        response = GeminiResponse(pages=pages)
        
        assert response.doc is None
        assert len(response.pages) == 1


class TestExtractedArticle:
    """Test ExtractedArticle schema."""
    
    def test_trilingual_article(self) -> None:
        """Test article with all three languages."""
        article = ExtractedArticle(
            article_no=1,
            rw=LanguageText(text="Ingingo ya mbere", pages=[0, 1]),
            en=LanguageText(text="Article One", pages=[0, 1]),
            fr=LanguageText(text="Article Premier", pages=[0, 1]),
            pages=[0, 1]
        )
        
        assert article.article_no == 1
        assert article.rw.text == "Ingingo ya mbere"
        assert article.en.text == "Article One"
        assert article.fr.text == "Article Premier"
        assert article.pages == [0, 1]
    
    def test_partial_language_article(self) -> None:
        """Test article with only some languages."""
        article = ExtractedArticle(
            article_no=2,
            en=LanguageText(text="English only", pages=[2]),
            pages=[2]
        )
        
        assert article.article_no == 2
        assert article.en.text == "English only"
        assert article.rw is None
        assert article.fr is None


class TestExtractedDocument:
    """Test ExtractedDocument schema."""
    
    def test_complete_document(self) -> None:
        """Test complete document structure."""
        doc_meta = DocumentMetadata(
            title="Official Gazette",
            date_iso="2024-01-15",
            source_filename="gazette.pdf"
        )
        
        articles = [
            ExtractedArticle(
                article_no=1,
                en=LanguageText(text="Test article", pages=[0])
            )
        ]
        
        tables = [
            TableInfo(
                table_id="table_001",
                pages=[1],
                csv="tables/table_001.csv",
                html="tables/table_001.html"
            )
        ]
        
        stats = RunStats(
            pages=2,
            articles_detected=1,
            tables_detected=1
        )
        
        document = ExtractedDocument(
            document=doc_meta,
            articles=articles,
            tables_index=tables,
            run_stats=stats
        )
        
        assert document.document.title == "Official Gazette"
        assert len(document.articles) == 1
        assert len(document.tables_index) == 1
        assert document.run_stats.pages == 2


class TestValidationErrors:
    """Test schema validation errors."""
    
    def test_invalid_section_type(self) -> None:
        """Test invalid section type."""
        with pytest.raises(ValidationError):
            PageBlock(section="invalid_section")  # type: ignore
    
    def test_invalid_language(self) -> None:
        """Test invalid language."""
        with pytest.raises(ValidationError):
            PageBlock(section=SectionType.ARTICLE, lang="invalid_lang")  # type: ignore
    
    def test_missing_required_field(self) -> None:
        """Test missing required field."""
        with pytest.raises(ValidationError):
            DocumentMetadata()  # Missing source_filename
    
    def test_negative_article_number(self) -> None:
        """Test negative article number."""
        # This should be valid as per current schema
        article = ExtractedArticle(article_no=-1)
        assert article.article_no == -1


class TestLanguageText:
    """Test LanguageText schema."""
    
    def test_language_text_with_pages(self) -> None:
        """Test language text with page references."""
        lang_text = LanguageText(
            text="Sample text content",
            pages=[0, 1, 2]
        )
        
        assert lang_text.text == "Sample text content"
        assert lang_text.pages == [0, 1, 2]
    
    def test_language_text_minimal(self) -> None:
        """Test minimal language text."""
        lang_text = LanguageText(text="Minimal text")
        
        assert lang_text.text == "Minimal text"
        assert lang_text.pages == []


class TestRunStats:
    """Test RunStats schema."""
    
    def test_stats_with_anomalies(self) -> None:
        """Test run stats with anomalies."""
        stats = RunStats(
            pages=10,
            articles_detected=5,
            tables_detected=2,
            anomalies=["Page 3: No content found", "Article 2: Missing French translation"]
        )
        
        assert stats.pages == 10
        assert stats.articles_detected == 5
        assert stats.tables_detected == 2
        assert len(stats.anomalies) == 2
    
    def test_minimal_stats(self) -> None:
        """Test minimal run stats."""
        stats = RunStats(pages=1, articles_detected=0)
        
        assert stats.pages == 1
        assert stats.articles_detected == 0
        assert stats.tables_detected == 0
        assert stats.anomalies == []
