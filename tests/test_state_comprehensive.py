"""Comprehensive tests for state management edge cases."""

from __future__ import annotations

import sqlite3
import tempfile
from pathlib import Path

from gazette_scraper.models import DiscoveryType, GazetteFile
from gazette_scraper.state import ScrapingState, calculate_sha256


class TestScrapingStateEdgeCases:
    """Test edge cases and error scenarios for ScrapingState."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.db_path = self.temp_dir / "test.db"
        self.state = ScrapingState(self.db_path)

    def teardown_method(self):
        """Clean up test fixtures."""
        import shutil

        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)

    def test_duplicate_file_different_paths(self):
        """Test same PDF found via different discovery paths."""
        # Create same file discovered from two different listings
        file1 = GazetteFile(
            title="Same Gazette",
            filename="gazette.pdf",
            issue_title="Same Gazette",
            download_url="https://example.com/gazette.pdf",
            source_url="https://example.com/gazette.pdf",
            listing_url="https://example.com/folder1",
            year=2024,
            month=1,
        )

        file2 = GazetteFile(
            title="Same Gazette",
            filename="gazette.pdf",
            issue_title="Same Gazette",
            download_url="https://example.com/gazette.pdf",  # Same download URL
            source_url="https://example.com/gazette.pdf",
            listing_url="https://example.com/folder2",  # Different listing URL
            year=2024,
            month=1,
        )

        # Record discoveries
        self.state.record_file_discovery(file1)
        self.state.record_file_discovery(file2)

        # Record download once
        local_path = Path("/tmp/gazette.pdf")
        sha256 = "a1b2c3d4e5f67890123456789012345678901234567890123456789012345678"
        self.state.record_download(file1, local_path, sha256)

        # Check both files are marked as downloaded (same download URL)
        assert self.state.is_already_downloaded(file1)
        assert self.state.is_already_downloaded(file2)

        # Check we have two discovery records but one download
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM file_discoveries")
            assert cursor.fetchone()[0] == 2

            cursor = conn.execute("SELECT COUNT(*) FROM downloaded_files")
            assert cursor.fetchone()[0] == 1

    def test_seen_file_discovery_vs_download(self):
        """Test seen_file logic with both discovery and download tables."""
        file = GazetteFile(
            title="Test",
            filename="test.pdf",
            issue_title="Test",
            download_url="https://example.com/test.pdf",
            source_url="https://example.com/test.pdf",
            listing_url="https://example.com/folder",
            year=2024,
            month=1,
        )

        # Initially not seen
        assert not self.state.seen_file(str(file.download_url))

        # Record discovery only
        self.state.record_file_discovery(file)
        assert self.state.seen_file(str(file.download_url))

        # Now download it
        local_path = Path("/tmp/test.pdf")
        sha256 = "test123456789012345678901234567890123456789012345678901234567890"
        self.state.record_download(file, local_path, sha256)

        # Should still be seen
        assert self.state.seen_file(str(file.download_url))

    def test_record_discovery_page_tracking(self):
        """Test page discovery tracking."""
        # Record different types of discoveries
        self.state.record_discovery("http://test.com/years", DiscoveryType.YEAR, 5)
        self.state.record_discovery("http://test.com/months", DiscoveryType.MONTH, 12)
        self.state.record_discovery("http://test.com/files", DiscoveryType.FILE, 25)

        # Check that records were created
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM page_discoveries")
            assert cursor.fetchone()[0] == 3

            # Check specific record
            cursor = conn.execute(
                "SELECT page_type, item_count FROM page_discoveries WHERE url = ?",
                ("http://test.com/years",),
            )
            result = cursor.fetchone()
            assert result[0] == "year"
            assert result[1] == 5

    def test_failure_tracking_incremental(self):
        """Test incremental failure counting."""
        file = GazetteFile(
            title="Failing File",
            filename="fail.pdf",
            issue_title="Failing File",
            download_url="https://example.com/fail.pdf",
            source_url="https://example.com/fail.pdf",
            listing_url="https://example.com/folder",
            year=2024,
            month=1,
        )

        # Record multiple failures
        self.state.record_failure(file, "Error 1", 404)
        assert self.state.get_failed_attempts(file) == 1

        self.state.record_failure(file, "Error 2", 403)
        assert self.state.get_failed_attempts(file) == 2

        self.state.record_failure(file, "Error 3", 500)
        assert self.state.get_failed_attempts(file) == 3

        # Check that latest error is stored
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT error_message, http_status FROM failed_downloads WHERE download_url = ?",
                (str(file.download_url),),
            )
            result = cursor.fetchone()
            assert result[0] == "Error 3"
            assert result[1] == 500

    def test_successful_download_clears_failures(self):
        """Test that successful download removes failure records."""
        file = GazetteFile(
            title="Recovery File",
            filename="recovery.pdf",
            issue_title="Recovery File",
            download_url="https://example.com/recovery.pdf",
            source_url="https://example.com/recovery.pdf",
            listing_url="https://example.com/folder",
            year=2024,
            month=1,
        )

        # Record failure first
        self.state.record_failure(file, "Initial failure", 403)
        assert self.state.get_failed_attempts(file) == 1

        # Then successful download
        local_path = Path("/tmp/recovery.pdf")
        sha256 = "recovery123456789012345678901234567890123456789012345678901234567"
        self.state.record_download(file, local_path, sha256)

        # Failure count should be reset
        assert self.state.get_failed_attempts(file) == 0

        # Check failure record was deleted
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT COUNT(*) FROM failed_downloads WHERE download_url = ?",
                (str(file.download_url),),
            )
            assert cursor.fetchone()[0] == 0

    def test_robots_check_caching(self):
        """Test robots.txt check caching."""
        url = "https://example.com"
        content = "User-agent: *\nDisallow: /private"

        # Initially no check
        assert self.state.get_robots_check(url) is None

        # Record allowed
        self.state.record_robots_check(url, content, True)
        assert self.state.get_robots_check(url) == 1  # SQLite returns 1 for True

        # Update to disallowed
        self.state.record_robots_check(url, content, False)
        assert self.state.get_robots_check(url) == 0  # SQLite returns 0 for False

        # Check that content hash is calculated
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT content_sha256 FROM robots_checks WHERE url = ?", (url,)
            )
            result = cursor.fetchone()
            assert result[0] is not None
            assert len(result[0]) == 64  # SHA256 hex length

    def test_get_stats_comprehensive(self):
        """Test comprehensive statistics generation."""
        # Create test data across multiple years and months
        files_data = [
            (2024, 1, "test1.pdf"),
            (2024, 1, "test2.pdf"),
            (2024, 2, "test3.pdf"),
            (2023, 12, "test4.pdf"),
            (2023, 11, "test5.pdf"),
        ]

        for year, month, filename in files_data:
            file = GazetteFile(
                title=f"Test {filename}",
                filename=filename,
                issue_title=f"Test {filename}",
                download_url=f"https://example.com/{filename}",
                source_url=f"https://example.com/{filename}",
                listing_url="https://example.com/folder",
                year=year,
                month=month,
            )

            # Record discovery
            self.state.record_file_discovery(file)

            # Download some files
            if filename in ["test1.pdf", "test3.pdf", "test5.pdf"]:
                local_path = Path(f"/tmp/{filename}")
                sha256 = (
                    f"{filename}123456789012345678901234567890123456789012345678901234"
                )
                self.state.record_download(file, local_path, sha256)

            # Fail some downloads
            if filename in ["test2.pdf", "test4.pdf"]:
                self.state.record_failure(file, f"Failed {filename}", 404)

        # Get comprehensive stats
        stats = self.state.get_stats()

        # Check basic counts
        assert stats["total_discovered"] == 5
        assert stats["total_downloaded"] == 3
        assert stats["total_failed"] == 2

        # Check by year
        assert stats["downloaded_by_year"][2024] == 2  # test1, test3
        assert stats["downloaded_by_year"][2023] == 1  # test5
        assert stats["discovered_by_year"][2024] == 3  # test1, test2, test3
        assert stats["discovered_by_year"][2023] == 2  # test4, test5

        # Check by month
        assert stats["downloaded_by_month"]["2024-01"] == 1  # test1
        assert stats["downloaded_by_month"]["2024-02"] == 1  # test3
        assert stats["discovered_by_month"]["2024-01"] == 2  # test1, test2

        # Check error summary
        assert stats["errors_by_status"][404] == 2

    def test_get_downloaded_files_ordering(self):
        """Test downloaded files are returned in correct order."""
        # Create files with different timestamps
        files_data = [
            ("old.pdf", 2023, 1),
            ("new.pdf", 2024, 2),
            ("middle.pdf", 2024, 1),
        ]

        for filename, year, month in files_data:
            file = GazetteFile(
                title=filename,
                filename=filename,
                issue_title=filename,
                download_url=f"https://example.com/{filename}",
                source_url=f"https://example.com/{filename}",
                listing_url="https://example.com/folder",
                year=year,
                month=month,
            )

            local_path = Path(f"/tmp/{filename}")
            sha256 = f"{filename}456789012345678901234567890123456789012345678901234567"
            self.state.record_download(file, local_path, sha256)

        # Get files - should be ordered by year DESC, month DESC, downloaded_at DESC
        files = self.state.get_downloaded_files()
        assert len(files) == 3

        # Check ordering (newest first)
        assert files[0]["filename"] == "new.pdf"  # 2024-02
        assert files[1]["filename"] == "middle.pdf"  # 2024-01
        assert files[2]["filename"] == "old.pdf"  # 2023-01

    def test_database_indexes_created(self):
        """Test that database indexes are created properly."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%'"
            )
            indexes = [row[0] for row in cursor.fetchall()]

            expected_indexes = [
                "idx_sha256",
                "idx_download_url",
                "idx_year_month",
                "idx_discovery_source",
                "idx_discovery_listing",
                "idx_page_type",
                "idx_failed_url",
            ]

            for expected in expected_indexes:
                assert expected in indexes, f"Missing index: {expected}"

    def test_invalid_sha256_handling(self):
        """Test handling of invalid SHA256 values."""
        # Test with None (should be allowed)
        assert not self.state.has_sha256("")

        # Test with valid SHA256
        valid_sha256 = (
            "a1b2c3d4e5f67890123456789012345678901234567890123456789012345678"
        )
        assert not self.state.has_sha256(valid_sha256)  # Not in DB yet


class TestCalculateSha256EdgeCases:
    """Test SHA256 calculation edge cases."""

    def test_calculate_sha256_large_file(self):
        """Test SHA256 calculation on larger file."""
        with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
            # Write a larger content
            content = "A" * 10000  # 10KB of A's
            f.write(content)
            temp_path = Path(f.name)

        try:
            sha256 = calculate_sha256(temp_path)
            # Verify it's a valid SHA256 (64 hex chars)
            assert len(sha256) == 64
            assert all(c in "0123456789abcdef" for c in sha256)

            # Verify consistency
            sha256_2 = calculate_sha256(temp_path)
            assert sha256 == sha256_2
        finally:
            temp_path.unlink()

    def test_calculate_sha256_nonexistent_file(self):
        """Test SHA256 calculation on non-existent file."""
        nonexistent = Path("/tmp/does_not_exist_12345.pdf")

        try:
            calculate_sha256(nonexistent)
            raise AssertionError("Should have raised FileNotFoundError")
        except FileNotFoundError:
            pass  # Expected

    def test_calculate_sha256_directory(self):
        """Test SHA256 calculation on directory (should fail)."""
        temp_dir = Path(tempfile.mkdtemp())

        try:
            try:
                calculate_sha256(temp_dir)
                raise AssertionError("Should have raised IsADirectoryError")
            except IsADirectoryError:
                pass  # Expected
        finally:
            temp_dir.rmdir()
