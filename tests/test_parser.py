"""Tests for HTML parser."""

from __future__ import annotations

from gazette_scraper.models import FolderType
from gazette_scraper.parser import GazetteParser


class TestGazetteParser:
    """Test GazetteParser class with new multi-level API."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = GazetteParser()

    def test_parse_year_page_empty(self):
        """Test parsing empty HTML returns no folders."""
        html = "<html><body><p>No links here</p></body></html>"
        folders = self.parser.parse_year_page(html, "https://example.com")
        assert folders == []

    def test_parse_year_page_with_year_links(self):
        """Test parsing HTML with year folder links."""
        html = """
        <html><body>
            <a href="?tx_filelist_filelist[path]=Official%20Gazette/2024">2024</a>
            <a href="?tx_filelist_filelist[path]=Official%20Gazette/2023">2023</a>
            <a href="?tx_filelist[path]=Other/Path">Other</a>
            <a href="regular-link.html">Regular Link</a>
        </body></html>
        """

        folders = self.parser.parse_year_page(html, "https://example.com/base")

        assert len(folders) == 2  # Only the proper year folders
        year_folders = [f for f in folders if f.folder_type == FolderType.YEAR]
        assert len(year_folders) == 2

        # Check 2024 folder
        folder_2024 = next((f for f in year_folders if f.year == 2024), None)
        assert folder_2024 is not None
        assert folder_2024.name == "2024"

        # Check 2023 folder
        folder_2023 = next((f for f in year_folders if f.year == 2023), None)
        assert folder_2023 is not None
        assert folder_2023.name == "2023"

    def test_parse_month_page_empty(self):
        """Test parsing empty month page."""
        html = "<html><body><table></table></body></html>"
        folders = self.parser.parse_month_page(html, "https://example.com", 2024)
        assert folders == []

    def test_parse_month_page_with_month_folders(self):
        """Test parsing month page with month folders."""
        html = """
        <html><body>
            <table>
                <tr><th>Name</th><th>Size</th><th>Modified</th></tr>
                <tr>
                    <td><img src="folder.png"/><a href="?tx_filelist_filelist[path]=Official%20Gazette/2024/01">January</a></td>
                    <td>-</td>
                    <td>15/01/2024</td>
                </tr>
                <tr>
                    <td><img src="folder.png"/><a href="?tx_filelist_filelist[path]=Official%20Gazette/2024/02">February</a></td>
                    <td>-</td>
                    <td>20/02/2024</td>
                </tr>
                <tr>
                    <td><a href="index.php?eID=dumpFile&f=123&t=f&token=abc">file.pdf</a></td>
                    <td>1.2 MB</td>
                    <td>25/01/2024</td>
                </tr>
            </table>
        </body></html>
        """

        folders = self.parser.parse_month_page(html, "https://example.com/2024", 2024)

        assert len(folders) == 2

        jan_folder = next((f for f in folders if f.month == 1), None)
        assert jan_folder is not None
        assert jan_folder.name == "January"
        assert jan_folder.folder_type == FolderType.MONTH
        assert jan_folder.year == 2024

        feb_folder = next((f for f in folders if f.month == 2), None)
        assert feb_folder is not None
        assert feb_folder.name == "February"
        assert feb_folder.folder_type == FolderType.MONTH
        assert feb_folder.year == 2024

    def test_parse_file_page_empty(self):
        """Test parsing empty file page."""
        html = "<html><body><table></table></body></html>"
        files = self.parser.parse_file_page(html, "https://example.com")
        assert files == []

    def test_parse_file_page_with_pdf_files(self):
        """Test parsing file page with PDF files."""
        html = """
        <html><body>
            <table>
                <tr><th>Name</th><th>Size</th><th>Modified</th></tr>
                <tr>
                    <td><a href="index.php?eID=dumpFile&f=123&t=f&token=abc">gazette_01.pdf</a></td>
                    <td>1.2 MB</td>
                    <td>15/01/2024</td>
                </tr>
                <tr>
                    <td><a href="index.php?eID=dumpFile&f=456&t=f&token=def">gazette_02.pdf</a></td>
                    <td>800 KB</td>
                    <td>20/01/2024</td>
                </tr>
                <tr>
                    <td><a href="regular-link.html">not_a_pdf.txt</a></td>
                    <td>10 KB</td>
                    <td>25/01/2024</td>
                </tr>
            </table>
        </body></html>
        """

        files = self.parser.parse_file_page(html, "https://example.com/2024/01")

        assert len(files) >= 2  # Should find PDF files

        # Check that PDF files are found
        pdf_files = [f for f in files if f.title.endswith(".pdf")]
        assert len(pdf_files) >= 2

        # Check first PDF
        gazette_01 = next((f for f in pdf_files if "gazette_01" in f.title), None)
        assert gazette_01 is not None
        assert gazette_01.size_str == "1.2 MB"
        assert "dumpFile" in gazette_01.href

    def test_infer_month_from_name(self):
        """Test month inference from folder names."""
        test_cases = [
            ("January", 1),
            ("February", 2),
            ("March", 3),
            ("April", 4),
            ("May", 5),
            ("June", 6),
            ("July", 7),
            ("August", 8),
            ("September", 9),
            ("October", 10),
            ("November", 11),
            ("December", 12),
            ("01", 1),
            ("02", 2),
            ("12", 12),
            ("Unknown", None),
        ]

        for name, expected in test_cases:
            result = self.parser._infer_month_from_name(name)
            assert result == expected, f"Expected {expected} for '{name}', got {result}"

    def test_is_folder_link(self):
        """Test folder link detection."""
        from bs4 import BeautifulSoup

        # Test with folder icon
        html_with_icon = """
        <tr>
            <td><img src="folder.png"/><a href="link">Folder</a></td>
        </tr>
        """
        soup = BeautifulSoup(html_with_icon, "html.parser")
        row = soup.find("tr")
        link = soup.find("a")

        assert self.parser._is_folder_link(row, link) is True

        # Test without folder icon
        html_no_icon = """
        <tr>
            <td><a href="file.pdf">File</a></td>
        </tr>
        """
        soup = BeautifulSoup(html_no_icon, "html.parser")
        row = soup.find("tr")
        link = soup.find("a")

        # Should still be able to detect folders based on other criteria
        result = self.parser._is_folder_link(row, link)
        assert isinstance(result, bool)

    def test_extract_file_info(self):
        """Test file information extraction."""
        from bs4 import BeautifulSoup

        html = """
        <tr>
            <td><a href="index.php?eID=dumpFile&f=123&t=f&token=abc">Official Gazette No. 01.pdf</a></td>
            <td>1.5 MB</td>
            <td>15/01/2024</td>
        </tr>
        """

        soup = BeautifulSoup(html, "html.parser")
        row = soup.find("tr")

        file_info = self.parser._extract_file_info(row, "https://example.com/listing")

        assert file_info is not None
        assert file_info.title == "Official Gazette No. 01.pdf"
        assert file_info.size_str == "1.5 MB"
        assert "dumpFile" in file_info.href
        assert file_info.listing_url == "https://example.com/listing"

    def test_has_next_page(self):
        """Test next page detection."""
        # HTML with next page
        html_with_next = """
        <html><body>
            <div class="pagination">
                <a href="?page=2">Next</a>
            </div>
        </body></html>
        """
        assert self.parser.has_next_page(html_with_next) is True

        # HTML without next page
        html_no_next = "<html><body><p>No pagination</p></body></html>"
        assert self.parser.has_next_page(html_no_next) is False

    def test_get_page_signature(self):
        """Test page signature generation."""
        html = """
        <html><body>
            <table>
                <tr><td><a href="link1">Item 1</a></td></tr>
                <tr><td><a href="link2">Item 2</a></td></tr>
            </table>
        </body></html>
        """

        signature = self.parser.get_page_signature(html)
        assert isinstance(signature, str)
        assert len(signature) > 0

        # Same HTML should produce same signature
        signature2 = self.parser.get_page_signature(html)
        assert signature == signature2

        # Different HTML should produce different signature
        different_html = "<html><body><p>Different content</p></body></html>"
        different_signature = self.parser.get_page_signature(different_html)
        assert signature != different_signature

    def test_parse_size_to_bytes(self):
        """Test size string parsing to bytes."""
        test_cases = [
            ("1.2 MB", 1258291),  # 1.2 * 1024 * 1024
            ("800 KB", 819200),  # 800 * 1024
            ("1.5 GB", 1610612736),  # 1.5 * 1024 * 1024 * 1024
            ("invalid", None),
            ("", None),
        ]

        for size_str, expected in test_cases:
            result = self.parser._parse_size_to_bytes(size_str)
            if expected is None:
                assert result is None
            else:
                # Allow some tolerance for rounding
                assert abs(result - expected) < expected * 0.1

    def test_extract_year_folder_info_invalid(self):
        """Test year folder extraction with invalid input."""
        from bs4 import BeautifulSoup

        # Link without year
        html = '<a href="?tx_filelist[path]=Other">Other</a>'
        soup = BeautifulSoup(html, "html.parser")
        link = soup.find("a")

        result = self.parser._extract_year_folder_info(
            "?tx_filelist[path]=Other", "https://example.com", link
        )
        assert result is None

    def test_extract_month_folder_info_invalid(self):
        """Test month folder extraction with invalid input."""
        from bs4 import BeautifulSoup

        # Link without recognizable month
        html = '<a href="?tx_filelist[path]=Other">Other</a>'
        soup = BeautifulSoup(html, "html.parser")
        link = soup.find("a")

        result = self.parser._extract_month_folder_info(
            "?tx_filelist[path]=Other", "https://example.com", link, 2024
        )
        # Should still create a folder, but month might be None
        assert result is not None
        assert result.year == 2024

    def test_is_date_like(self):
        """Test date pattern recognition."""
        assert self.parser._is_date_like("15/01/2024") is True
        assert self.parser._is_date_like("2024-01-15") is True
        assert self.parser._is_date_like("15 Jan 2024") is True
        assert self.parser._is_date_like("15 January 2024") is True
        assert self.parser._is_date_like("not a date") is False
        assert self.parser._is_date_like("123456") is False

    def test_looks_like_pdf(self):
        """Test PDF detection."""
        assert self.parser._looks_like_pdf("document.pdf", "test.pdf") is True
        assert self.parser._looks_like_pdf("Official Gazette", "gazette_url") is True
        assert self.parser._looks_like_pdf("Igazeti ya Leta", "og_url") is True
        assert self.parser._looks_like_pdf("random text", "random_url") is False

    def test_get_next_page_url(self):
        """Test getting next page URL."""
        html = '<html><body><a href="?page=2">Next</a></body></html>'
        current_url = "https://example.com/gazette?page=1"

        next_url = self.parser.get_next_page_url(html, current_url)
        assert next_url == "https://example.com/gazette?page=2"

        # Test with no next page
        html_no_next = "<html><body><p>Last page</p></body></html>"
        next_url = self.parser.get_next_page_url(html_no_next, current_url)
        assert next_url is None

    def test_extract_current_page(self):
        """Test current page extraction."""
        url_with_page = "https://example.com?currentPage=5"
        assert self.parser.extract_current_page(url_with_page) == 5

        url_without_page = "https://example.com"
        assert self.parser.extract_current_page(url_without_page) == 1

    def test_build_next_page_url(self):
        """Test building next page URL."""
        base_url = "https://example.com/gazette"
        next_url = self.parser.build_next_page_url(base_url, 2)
        assert "currentPage=2" in next_url

        # Test with existing parameters
        base_url_with_params = "https://example.com/gazette?foo=bar"
        next_url = self.parser.build_next_page_url(base_url_with_params, 3)
        assert "currentPage=3" in next_url
        assert "foo=bar" in next_url

    def test_parse_date_from_text(self):
        """Test date parsing from various text formats."""
        from datetime import datetime

        test_cases = [
            ("15/01/2024", datetime(2024, 1, 15)),
            ("2024-01-15", datetime(2024, 1, 15)),
            ("15 Jan 2024", datetime(2024, 1, 15)),
            ("invalid date", None),
            ("", None),
        ]

        for date_str, expected in test_cases:
            result = self.parser.parse_date_from_text(date_str)
            if expected is None:
                assert result is None
            else:
                assert result is not None
                assert result.year == expected.year
                assert result.month == expected.month
                assert result.day == expected.day

    def test_normalize_title(self):
        """Test title normalization."""
        assert (
            self.parser.normalize_title("Official Gazette [Igazeti ya Leta]")
            == "Official Gazette"
        )
        assert (
            self.parser.normalize_title("Title   with   spaces") == "Title with spaces"
        )
        assert self.parser.normalize_title("Normal Title") == "Normal Title"
