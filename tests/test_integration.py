"""Integration tests for end-to-end functionality."""

from __future__ import annotations

import tempfile
from pathlib import Path

from gazette_scraper.models import (
    FileItem,
    Folder,
    FolderType,
    Manifest,
    ScrapingConfig,
)
from gazette_scraper.pipeline import GazettePipeline


class TestDryRunIntegration:
    """Test end-to-end dry-run functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())

    def test_pipeline_dry_run_basic(self):
        """Basic test of pipeline dry-run functionality."""
        config = ScrapingConfig(
            output_dir=self.temp_dir,
            dry_run=True,
            since_year=2024,
            max_threads=1,
            rate_limit=10.0,
        )

        # Test that pipeline can be instantiated and configured for dry-run
        pipeline = GazettePipeline(config)
        assert pipeline.config.dry_run is True
        assert pipeline.config.output_dir == self.temp_dir

    def test_manifest_structure(self):
        """Test manifest data structure creation."""
        from datetime import datetime

        # Create a test manifest
        manifest = Manifest(
            total_discovered=5,
            by_year={2024: 3, 2023: 2},
            by_month={"2024-01": 2, "2024-02": 1, "2023-12": 2},
        )

        assert manifest.total_discovered == 5
        assert manifest.by_year[2024] == 3
        assert manifest.by_month["2024-01"] == 2
        assert isinstance(manifest.discovered_at, datetime)

    def test_config_loading(self):
        """Test configuration loading from file."""
        # Create test config (use TOML format which is expected)
        config_path = self.temp_dir / "test_config.toml"
        config_content = """
        base_url = "https://test.example.com"
        rate_limit = 2.0
        max_threads = 8
        since_year = 2020
        """

        with open(config_path, "w") as f:
            f.write(config_content)

        # Test that config loads without error
        from gazette_scraper.config import load_config

        config = load_config(config_path)

        assert config.base_url == "https://test.example.com"
        assert config.rate_limit == 2.0
        assert config.max_threads == 8
        assert config.since_year == 2020

    def teardown_method(self):
        """Clean up test fixtures."""
        import shutil

        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)


class TestDryRunFeatures:
    """Test specific dry-run related features."""

    def test_scrape_result_structure(self):
        """Test ScrapeResult data structure."""
        from datetime import datetime

        from gazette_scraper.models import ScrapeResult

        start_time = datetime.now()
        result = ScrapeResult(
            start_time=start_time,
            total_discovered=10,
            downloaded=0,  # Dry run - no downloads
            skipped=5,
            errors=0,
        )

        assert result.start_time == start_time
        assert result.total_discovered == 10
        assert result.downloaded == 0
        assert result.skipped == 5
        assert result.errors == 0
        assert result.end_time is None

    def test_file_item_structure(self):
        """Test FileItem data structure."""
        file_item = FileItem(
            title="Test Gazette.pdf",
            href="https://example.com/dumpFile?f=123",
            size_str="1.5 MB",
            size_bytes=1572864,
            modified="15/01/2024",
            listing_url="https://example.com/folder",
        )

        assert file_item.title == "Test Gazette.pdf"
        assert file_item.size_str == "1.5 MB"
        assert file_item.size_bytes == 1572864
        assert "dumpFile" in file_item.href

    def test_folder_structure(self):
        """Test Folder data structure."""
        year_folder = Folder(
            name="2024",
            href="https://example.com/2024",
            folder_type=FolderType.YEAR,
            year=2024,
        )

        month_folder = Folder(
            name="January",
            href="https://example.com/2024/01",
            folder_type=FolderType.MONTH,
            year=2024,
            month=1,
        )

        assert year_folder.folder_type == FolderType.YEAR
        assert year_folder.year == 2024
        assert year_folder.month is None

        assert month_folder.folder_type == FolderType.MONTH
        assert month_folder.year == 2024
        assert month_folder.month == 1
