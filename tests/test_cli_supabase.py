"""Tests for Supabase-related CLI commands."""

import tempfile
from pathlib import Path
from unittest.mock import MagicMock, patch

from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from gazette_scraper.__main__ import cli
from gazette_scraper.models import ScrapingConfig


class TestSyncSupabaseCLI:
    """Test cases for sync-supabase CLI command."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.runner = CliRunner()

    def teardown_method(self):
        """Clean up test fixtures."""
        import shutil

        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)

    @patch("gazette_scraper.__main__.load_config")
    @patch("gazette_scraper.supabase_client.sync_unsynced_files")
    def test_sync_supabase_success(self, mock_sync_unsynced, mock_load_config):
        """Test successful sync-supabase command execution."""
        # Setup
        mock_config = MagicMock(spec=ScrapingConfig)
        mock_config.supabase_url = "https://test.supabase.co"
        mock_config.supabase_key = "test_key"
        mock_config.db_path = self.temp_dir / "test.db"
        mock_load_config.return_value = mock_config

        mock_sync_unsynced.return_value = {
            "synced": 5,
            "failed": 0,
            "skipped": 0
        }

        # Execute
        result = self.runner.invoke(cli, ["sync-supabase"])

        # Assert
        assert result.exit_code == 0
        assert "5 files successfully synced" in result.output
        assert "Supabase Sync Summary" in result.output
        mock_sync_unsynced.assert_called_once_with(str(mock_config.db_path))

    @patch("gazette_scraper.__main__.load_config")
    def test_sync_supabase_no_config(self, mock_load_config):
        """Test sync-supabase when Supabase is not configured."""
        # Setup
        mock_config = MagicMock(spec=ScrapingConfig)
        mock_config.supabase_url = None
        mock_config.supabase_key = None
        mock_load_config.return_value = mock_config

        # Execute
        result = self.runner.invoke(cli, ["sync-supabase"])

        # Assert
        assert result.exit_code == 1
        assert "Supabase is not configured" in result.output
        assert "SUPABASE_SERVICE_ROLE_KEY" in result.output

    @patch("gazette_scraper.__main__.load_config")
    @patch("gazette_scraper.supabase_client.sync_unsynced_files")
    def test_sync_supabase_with_failures(self, mock_sync_unsynced, mock_load_config):
        """Test sync-supabase command with some failures."""
        # Setup
        mock_config = MagicMock(spec=ScrapingConfig)
        mock_config.supabase_url = "https://test.supabase.co"
        mock_config.supabase_key = "test_key"
        mock_config.db_path = self.temp_dir / "test.db"
        mock_load_config.return_value = mock_config

        mock_sync_unsynced.return_value = {
            "synced": 3,
            "failed": 2,
            "skipped": 0
        }

        # Execute
        result = self.runner.invoke(cli, ["sync-supabase"])

        # Assert
        assert result.exit_code == 1  # Should exit with error code due to failures
        assert "3 files successfully synced" in result.output
        assert "2 files failed to sync" in result.output
        assert "Supabase Sync Summary" in result.output

    @patch("gazette_scraper.__main__.load_config")
    @patch("gazette_scraper.supabase_client.sync_unsynced_files")
    def test_sync_supabase_no_files_to_sync(self, mock_sync_unsynced, mock_load_config):
        """Test sync-supabase when no files need syncing."""
        # Setup
        mock_config = MagicMock(spec=ScrapingConfig)
        mock_config.supabase_url = "https://test.supabase.co"
        mock_config.supabase_key = "test_key"
        mock_config.db_path = self.temp_dir / "test.db"
        mock_load_config.return_value = mock_config

        mock_sync_unsynced.return_value = {
            "synced": 0,
            "failed": 0,
            "skipped": 0
        }

        # Execute
        result = self.runner.invoke(cli, ["sync-supabase"])

        # Assert
        assert result.exit_code == 0
        assert "No files to sync (all already synced)" in result.output
        assert "Supabase Sync Summary" in result.output

    @patch("gazette_scraper.__main__.load_config")
    @patch("gazette_scraper.supabase_client.sync_unsynced_files")
    def test_sync_supabase_exception_handling(self, mock_sync_unsynced, mock_load_config):
        """Test sync-supabase command exception handling."""
        # Setup
        mock_config = MagicMock(spec=ScrapingConfig)
        mock_config.supabase_url = "https://test.supabase.co"
        mock_config.supabase_key = "test_key"
        mock_config.db_path = self.temp_dir / "test.db"
        mock_load_config.return_value = mock_config

        mock_sync_unsynced.side_effect = Exception("Database connection failed")

        # Execute
        result = self.runner.invoke(cli, ["sync-supabase"])

        # Assert
        assert result.exit_code == 1
        assert "Sync failed: Database connection failed" in result.output

    @patch("gazette_scraper.__main__.load_config")
    @patch("gazette_scraper.supabase_client.sync_unsynced_files")
    def test_sync_supabase_verbose_mode(self, mock_sync_unsynced, mock_load_config):
        """Test sync-supabase command with verbose flag."""
        # Setup
        mock_config = MagicMock(spec=ScrapingConfig)
        mock_config.supabase_url = "https://test.supabase.co"
        mock_config.supabase_key = "test_key"
        mock_config.db_path = self.temp_dir / "test.db"
        mock_load_config.return_value = mock_config

        mock_sync_unsynced.side_effect = Exception("Database connection failed")

        # Execute with verbose flag
        result = self.runner.invoke(cli, ["--verbose", "sync-supabase"])

        # Assert
        assert result.exit_code == 1
        assert "Sync failed: Database connection failed" in result.output
        # Note: verbose mode would show exception traceback, but that's handled by Rich
        # and is difficult to test directly in CLI tests

    @patch("gazette_scraper.__main__.load_config")
    @patch("gazette_scraper.supabase_client.sync_unsynced_files")
    def test_sync_supabase_table_output_format(self, mock_sync_unsynced, mock_load_config):
        """Test that sync-supabase produces properly formatted table output."""
        # Setup
        mock_config = MagicMock(spec=ScrapingConfig)
        mock_config.supabase_url = "https://test.supabase.co"
        mock_config.supabase_key = "test_key"
        mock_config.db_path = self.temp_dir / "test.db"
        mock_load_config.return_value = mock_config

        mock_sync_unsynced.return_value = {
            "synced": 10,
            "failed": 3,
            "skipped": 0
        }

        # Execute
        result = self.runner.invoke(cli, ["sync-supabase"])

        # Assert table structure is present - should fail due to failed syncs
        assert result.exit_code == 1  # Exits with 1 due to failures
        assert "Supabase Sync Summary" in result.output
        assert "Successfully synced" in result.output
        assert "Failed to sync" in result.output
        assert "Skipped" in result.output
        assert "10" in result.output  # synced count
        assert "3" in result.output   # failed count
        assert "0" in result.output   # skipped count

    @patch("gazette_scraper.__main__.load_config")
    @patch("gazette_scraper.supabase_client.sync_unsynced_files")
    def test_sync_supabase_mixed_results_status_reporting(self, mock_sync_unsynced, mock_load_config):
        """Test status reporting for mixed success/failure scenarios."""
        # Setup
        mock_config = MagicMock(spec=ScrapingConfig)
        mock_config.supabase_url = "https://test.supabase.co"
        mock_config.supabase_key = "test_key"
        mock_config.db_path = self.temp_dir / "test.db"
        mock_load_config.return_value = mock_config

        # Mixed results: some success, some failures
        mock_sync_unsynced.return_value = {
            "synced": 7,
            "failed": 2,
            "skipped": 0
        }

        # Execute
        result = self.runner.invoke(cli, ["sync-supabase"])

        # Assert
        assert result.exit_code == 1  # Exit code should be 1 due to failures

        # Should show both success and failure messages
        assert "7 files successfully synced" in result.output
        assert "2 files failed to sync" in result.output

        # Should show summary table
        assert "Supabase Sync Summary" in result.output
        assert "Successfully synced" in result.output and "7" in result.output
        assert "Failed to sync" in result.output and "2" in result.output
        assert "Skipped" in result.output and "0" in result.output


class TestCLIIntegrationWithSupabase:
    """Test CLI integration points with Supabase functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.runner = CliRunner()

    def teardown_method(self):
        """Clean up test fixtures."""
        import shutil

        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)

    def test_fetch_command_includes_supabase_config(self):
        """Test that fetch command recognizes Supabase configuration."""
        # This is more of a smoke test to ensure Supabase config doesn't break other commands
        config_content = f"""
        [general]
        base_url = "https://www.minijust.gov.rw"
        output_dir = "{self.temp_dir}"

        [supabase]
        url = "https://test.supabase.co"
        service_role_key = "test_key"
        """

        config_file = self.temp_dir / "test_config.toml"
        config_file.write_text(config_content)

        # This should not crash due to Supabase config presence
        with patch("gazette_scraper.pipeline.GazettePipeline.run") as mock_run:
            mock_result = MagicMock()
            mock_result.errors = 0
            mock_result.total_discovered = 5
            mock_run.return_value = mock_result
            result = self.runner.invoke(cli, [
                "--config", str(config_file),
                "fetch",
                "--dry-run"
            ])

        # Should execute without errors
        assert result.exit_code == 0
