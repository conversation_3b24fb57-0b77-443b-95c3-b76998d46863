"""Tests for configuration management."""

import os
import tempfile
from pathlib import Path

import pytest

from gazette_scraper.config import load_config
from gazette_scraper.models import ScrapingConfig


class TestLoadConfig:
    """Test configuration loading."""

    def test_load_default_config(self):
        """Test loading default configuration when no file exists."""
        config = load_config(Path("nonexistent.toml"))

        assert isinstance(config, ScrapingConfig)
        assert config.base_url == "https://www.minijust.gov.rw"
        assert config.rate_limit == 1.0
        assert config.max_retries == 3
        assert config.output_dir == Path("./data")
        assert config.max_threads == 4

    def test_load_config_from_file(self):
        """Test loading configuration from TOML file."""
        config_content = """
        base_url = "https://custom.gov.rw"
        rate_limit = 2.5
        max_threads = 8
        output_dir = "/custom/path"
        since_year = 2020
        """

        with tempfile.NamedTemporaryFile(mode="w", suffix=".toml", delete=False) as f:
            f.write(config_content)
            config_path = Path(f.name)

        try:
            config = load_config(config_path)

            assert config.base_url == "https://custom.gov.rw"
            assert config.rate_limit == 2.5
            assert config.max_threads == 8
            assert config.output_dir == Path("/custom/path")
            assert config.since_year == 2020
        finally:
            config_path.unlink()

    def test_load_config_with_env_overrides(self):
        """Test that environment variables override config file."""
        config_content = """
        rate_limit = 1.0
        max_threads = 4
        """

        with tempfile.NamedTemporaryFile(mode="w", suffix=".toml", delete=False) as f:
            f.write(config_content)
            config_path = Path(f.name)

        # Set environment variables
        os.environ["GAZETTE_RATE_LIMIT"] = "3.0"
        os.environ["GAZETTE_MAX_THREADS"] = "12"
        os.environ["GAZETTE_OUTPUT_DIR"] = "/env/path"

        try:
            config = load_config(config_path)

            # Should use environment values
            assert config.rate_limit == 3.0
            assert config.max_threads == 12
            assert config.output_dir == Path("/env/path")
        finally:
            config_path.unlink()
            # Clean up environment
            for key in [
                "GAZETTE_RATE_LIMIT",
                "GAZETTE_MAX_THREADS",
                "GAZETTE_OUTPUT_DIR",
            ]:
                if key in os.environ:
                    del os.environ[key]

    def test_load_config_env_only(self):
        """Test loading configuration from environment variables only."""
        # Set environment variables
        os.environ["GAZETTE_RATE_LIMIT"] = "0.5"
        os.environ["GAZETTE_SINCE_YEAR"] = "2022"
        os.environ["GAZETTE_GCS_BUCKET"] = "test-bucket"

        try:
            config = load_config()

            assert config.rate_limit == 0.5
            assert config.since_year == 2022
            assert config.gcs_bucket == "test-bucket"
        finally:
            # Clean up environment
            for key in [
                "GAZETTE_RATE_LIMIT",
                "GAZETTE_SINCE_YEAR",
                "GAZETTE_GCS_BUCKET",
            ]:
                if key in os.environ:
                    del os.environ[key]

    def test_load_config_invalid_toml(self):
        """Test loading configuration with invalid TOML."""
        config_content = """
        invalid toml content [[[
        """

        with tempfile.NamedTemporaryFile(mode="w", suffix=".toml", delete=False) as f:
            f.write(config_content)
            config_path = Path(f.name)

        try:
            # Should raise an exception or handle gracefully
            with pytest.raises((ValueError, KeyError, TypeError)):
                load_config(config_path)
        finally:
            config_path.unlink()

    def test_load_config_type_conversion(self):
        """Test that environment variables are properly converted to correct types."""
        os.environ["GAZETTE_RATE_LIMIT"] = "2.5"  # float
        os.environ["GAZETTE_MAX_THREADS"] = "6"  # int
        os.environ["GAZETTE_SINCE_YEAR"] = "2021"  # int
        os.environ["GAZETTE_OUTPUT_DIR"] = "/test/path"  # Path
        os.environ["GAZETTE_DB_PATH"] = "/test/db.sqlite"  # Path

        try:
            config = load_config()

            assert isinstance(config.rate_limit, float)
            assert config.rate_limit == 2.5
            assert isinstance(config.max_threads, int)
            assert config.max_threads == 6
            assert isinstance(config.since_year, int)
            assert config.since_year == 2021
            assert isinstance(config.output_dir, Path)
            assert config.output_dir == Path("/test/path")
            assert isinstance(config.db_path, Path)
            assert config.db_path == Path("/test/db.sqlite")
        finally:
            # Clean up environment
            env_vars = [
                "GAZETTE_RATE_LIMIT",
                "GAZETTE_MAX_THREADS",
                "GAZETTE_SINCE_YEAR",
                "GAZETTE_OUTPUT_DIR",
                "GAZETTE_DB_PATH",
            ]
            for key in env_vars:
                if key in os.environ:
                    del os.environ[key]
