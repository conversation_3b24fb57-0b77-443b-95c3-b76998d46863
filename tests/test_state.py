"""Tests for state management."""

import sqlite3
import tempfile
from pathlib import Path

from gazette_scraper.models import GazetteFile
from gazette_scraper.state import ScrapingState, calculate_sha256


class TestScrapingState:
    """Test ScrapingState class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.db_path = self.temp_dir / "test.db"
        self.state = ScrapingState(self.db_path)

    def teardown_method(self):
        """Clean up test fixtures."""
        if self.db_path.exists():
            self.db_path.unlink()
        self.temp_dir.rmdir()

    def test_init_db_creates_tables(self):
        """Test that database initialization creates required tables."""
        # Check that tables exist
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]

        assert "downloaded_files" in tables
        assert "failed_downloads" in tables

    def test_is_already_downloaded_false_for_new_file(self):
        """Test that new files are not marked as downloaded."""
        file = GazetteFile(
            title="Test",
            filename="test.pdf",
            issue_title="Test",
            download_url="https://example.com/test.pdf",
            source_url="https://example.com/folder",
            listing_url="https://example.com/folder",
            year=2024,
            month=1,
        )

        assert not self.state.is_already_downloaded(file)

    def test_record_and_check_download(self):
        """Test recording a download and checking it."""
        file = GazetteFile(
            title="Test Gazette",
            filename="test.pdf",
            issue_title="Test Gazette",
            download_url="https://example.com/test.pdf",
            source_url="https://example.com/folder",
            listing_url="https://example.com/folder",
            year=2024,
            month=1,
        )

        local_path = Path("/tmp/test.pdf")
        sha256 = "abc123def456"

        # Record the download
        self.state.record_download(file, local_path, sha256)

        # Check it's marked as downloaded
        assert self.state.is_already_downloaded(file)
        assert self.state.has_sha256(sha256)

    def test_record_failure(self):
        """Test recording a failed download."""
        file = GazetteFile(
            title="Test",
            filename="test.pdf",
            issue_title="Test",
            download_url="https://example.com/test.pdf",
            source_url="https://example.com/folder",
            listing_url="https://example.com/folder",
            year=2024,
            month=1,
        )

        error_msg = "Network timeout"
        self.state.record_failure(file, error_msg)

        # Check failure count
        assert self.state.get_failed_attempts(file) == 1

        # Record another failure
        self.state.record_failure(file, "Another error")
        assert self.state.get_failed_attempts(file) == 2

    def test_get_downloaded_files_empty(self):
        """Test getting downloaded files when none exist."""
        files = self.state.get_downloaded_files()
        assert files == []

    def test_get_downloaded_files_with_data(self):
        """Test getting downloaded files with data."""
        file = GazetteFile(
            title="Test Gazette",
            filename="test.pdf",
            issue_title="Test Gazette",
            download_url="https://example.com/test.pdf",
            source_url="https://example.com/folder",
            listing_url="https://example.com/folder",
            year=2024,
            month=1,
        )

        local_path = Path("/tmp/test.pdf")
        sha256 = "a1b2c3d4e5f67890123456789012345678901234567890123456789012345678"

        self.state.record_download(file, local_path, sha256)

        files = self.state.get_downloaded_files()
        assert len(files) == 1
        assert files[0]["filename"] == "test.pdf"
        assert (
            files[0]["sha256"]
            == "a1b2c3d4e5f67890123456789012345678901234567890123456789012345678"
        )
        assert files[0]["year"] == 2024
        assert files[0]["month"] == 1

    def test_get_stats(self):
        """Test getting scraping statistics."""
        # Initially empty
        stats = self.state.get_stats()
        assert stats["total_downloaded"] == 0
        assert stats["total_failed"] == 0
        assert stats["downloaded_by_year"] == {}

        # Add some data
        file1 = GazetteFile(
            title="Test 1",
            filename="test1.pdf",
            issue_title="Test 1",
            download_url="https://example.com/test1.pdf",
            source_url="https://example.com/folder",
            listing_url="https://example.com/folder",
            year=2024,
            month=1,
        )

        file2 = GazetteFile(
            title="Test 2",
            filename="test2.pdf",
            issue_title="Test 2",
            download_url="https://example.com/test2.pdf",
            source_url="https://example.com/folder",
            listing_url="https://example.com/folder",
            year=2023,
            month=12,
        )

        self.state.record_download(file1, Path("/tmp/test1.pdf"), "hash1")
        self.state.record_download(file2, Path("/tmp/test2.pdf"), "hash2")
        self.state.record_failure(file1, "Test error")

        stats = self.state.get_stats()
        assert stats["total_downloaded"] == 2
        assert stats["total_failed"] == 1
        assert stats["downloaded_by_year"][2024] == 1
        assert stats["downloaded_by_year"][2023] == 1

    def test_has_sha256(self):
        """Test checking if SHA256 exists."""
        sha256 = "test_hash_123"

        # Should not exist initially
        assert not self.state.has_sha256(sha256)

        # Add a file with this hash
        file = GazetteFile(
            title="Test",
            filename="test.pdf",
            issue_title="Test",
            download_url="https://example.com/test.pdf",
            source_url="https://example.com/folder",
            listing_url="https://example.com/folder",
            year=2024,
            month=1,
        )

        self.state.record_download(file, Path("/tmp/test.pdf"), sha256)

        # Should exist now
        assert self.state.has_sha256(sha256)


class TestCalculateSha256:
    """Test SHA256 calculation function."""

    def test_calculate_sha256(self):
        """Test calculating SHA256 of a file."""
        with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
            f.write("Hello, World!")
            temp_path = Path(f.name)

        try:
            sha256 = calculate_sha256(temp_path)
            # Known SHA256 of "Hello, World!"
            expected = (
                "dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f"
            )
            assert sha256 == expected
        finally:
            temp_path.unlink()

    def test_calculate_sha256_empty_file(self):
        """Test calculating SHA256 of an empty file."""
        with tempfile.NamedTemporaryFile(delete=False) as f:
            temp_path = Path(f.name)

        try:
            sha256 = calculate_sha256(temp_path)
            # Known SHA256 of empty file
            expected = (
                "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"
            )
            assert sha256 == expected
        finally:
            temp_path.unlink()
