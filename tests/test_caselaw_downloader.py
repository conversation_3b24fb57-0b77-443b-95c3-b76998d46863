"""Tests for case-law PDF downloader."""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from pathlib import Path
from datetime import datetime

from gazette_scraper.caselaw.models import (
    CaseLawConfig, CaseLawFile, CaseMetadata, CourtLevel
)
from gazette_scraper.caselaw.downloader import CaseLawDownloader
from gazette_scraper.caselaw.state import CaseLawState


@pytest.fixture
def caselaw_config():
    """Test configuration for case-law scraping."""
    return CaseLawConfig(
        output_dir=Path("/tmp/test_caselaw"),
        verify_pdf_content=True,
        min_pdf_size=1024,
        screenshot_on_error=False,
    )


@pytest.fixture
def mock_state():
    """Mock case-law state."""
    state = MagicMock(spec=CaseLawState)
    state.is_case_already_downloaded.return_value = False
    state.mark_case_downloaded.return_value = None
    state.mark_case_failed.return_value = None
    return state


@pytest.fixture
def sample_case_file():
    """Sample case file for testing."""
    return CaseLawFile(
        title="Test Case v. Example Corp.",
        filename="test_case.pdf",
        case_title="Test Case v. Example Corp.",
        court_name="Supreme Court",
        court_level=CourtLevel.SUPREME,
        year=2023,
        month=1,
        day=15,
        download_url="https://example.com/download/case.pdf",
        case_page_url="https://example.com/case/123",
        listing_url="https://example.com/cases/2023/01/15",
        navigation_path="Supreme Court/2023/January/Day_15",
    )


@pytest.fixture
async def mock_downloader(caselaw_config, mock_state):
    """Mock downloader with HTTP session mocked."""
    downloader = CaseLawDownloader(caselaw_config, mock_state)

    # Mock aiohttp session
    mock_session = AsyncMock()
    downloader.session = mock_session

    return downloader, mock_session


@pytest.mark.asyncio
class TestCaseLawDownloader:
    """Test cases for the case-law downloader."""

    async def test_downloader_initialization(self, caselaw_config, mock_state):
        """Test downloader initialization."""
        downloader = CaseLawDownloader(caselaw_config, mock_state)
        assert downloader.config == caselaw_config
        assert downloader.state == mock_state
        assert downloader.session is None

    async def test_start_and_close(self, caselaw_config, mock_state):
        """Test downloader start and close lifecycle."""
        downloader = CaseLawDownloader(caselaw_config, mock_state)

        await downloader.start()
        assert downloader.session is not None

        await downloader.close()

    async def test_find_direct_pdf_links(self, mock_downloader):
        """Test finding direct PDF links."""
        downloader, _ = mock_downloader

        # Mock page with PDF link
        mock_page = AsyncMock()
        mock_element = AsyncMock()
        mock_element.get_attribute.return_value = "https://example.com/file.pdf"
        mock_page.query_selector_all.return_value = [mock_element]

        result = await downloader._find_direct_pdf_links(mock_page)
        assert result == "https://example.com/file.pdf"

    async def test_find_direct_pdf_links_no_results(self, mock_downloader):
        """Test finding direct PDF links when none exist."""
        downloader, _ = mock_downloader

        # Mock page with no PDF links
        mock_page = AsyncMock()
        mock_page.query_selector_all.return_value = []

        result = await downloader._find_direct_pdf_links(mock_page)
        assert result is None

    async def test_find_download_buttons(self, mock_downloader):
        """Test finding download buttons."""
        downloader, _ = mock_downloader

        # Mock page with download button
        mock_page = AsyncMock()
        mock_element = AsyncMock()
        mock_element.get_attribute.side_effect = lambda attr: {
            "href": "https://example.com/download.pdf",
            "data-url": None,
            "data-href": None,
            "onclick": None,
        }.get(attr)
        mock_page.query_selector_all.return_value = [mock_element]

        result = await downloader._find_download_buttons(mock_page)
        assert result == "https://example.com/download.pdf"

    async def test_extract_case_metadata(self, mock_downloader):
        """Test case metadata extraction."""
        downloader, _ = mock_downloader

        # Mock page with case metadata
        mock_page = AsyncMock()
        mock_page.text_content.return_value = """
        Case No: CIV/123/2023
        This is a Civil case involving Contract disputes.
        Parties: Plaintiff v. Defendant
        """

        mock_element = AsyncMock()
        mock_element.text_content.return_value = "CIV/123/2023"
        mock_page.query_selector.return_value = mock_element

        metadata = await downloader._extract_case_metadata(mock_page)

        assert metadata.case_number == "CIV/123/2023"
        assert metadata.case_type == "civil"
        assert "contract" in metadata.keywords

    async def test_download_pdf_file_success(self, mock_downloader, sample_case_file):
        """Test successful PDF download."""
        downloader, mock_session = mock_downloader

        # Mock successful HTTP response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.read.return_value = b"%PDF-1.4\n%Test PDF content"

        mock_session.get.return_value.__aenter__.return_value = mock_response

        result = await downloader.download_pdf_file(sample_case_file, "https://example.com/file.pdf")

        assert result is not None
        content, sha256 = result
        assert content.startswith(b"%PDF-1.4")
        assert len(sha256) == 64  # SHA256 hash length

    async def test_download_pdf_file_http_error(self, mock_downloader, sample_case_file):
        """Test PDF download with HTTP error."""
        downloader, mock_session = mock_downloader

        # Mock HTTP error response
        mock_response = AsyncMock()
        mock_response.status = 404

        mock_session.get.return_value.__aenter__.return_value = mock_response

        result = await downloader.download_pdf_file(sample_case_file, "https://example.com/file.pdf")

        assert result is None

    async def test_download_pdf_file_invalid_content(self, mock_downloader, sample_case_file):
        """Test PDF download with invalid content."""
        downloader, mock_session = mock_downloader

        # Mock response with non-PDF content
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.read.return_value = b"<html>Error page</html>"

        mock_session.get.return_value.__aenter__.return_value = mock_response

        result = await downloader.download_pdf_file(sample_case_file, "https://example.com/file.pdf")

        assert result is None

    async def test_save_pdf_file_success(self, mock_downloader, sample_case_file, tmp_path):
        """Test successful PDF file saving."""
        downloader, _ = mock_downloader
        downloader.config.output_dir = tmp_path

        pdf_content = b"%PDF-1.4\n%Test PDF content"
        sha256 = "test_hash"

        result = await downloader.save_pdf_file(sample_case_file, pdf_content, sha256)

        assert result is not None
        assert result.exists()
        assert result.read_bytes() == pdf_content

    async def test_save_pdf_file_existing_same_hash(self, mock_downloader, sample_case_file, tmp_path):
        """Test saving PDF when file already exists with same hash."""
        downloader, _ = mock_downloader
        downloader.config.output_dir = tmp_path

        # Create existing file
        output_dir = tmp_path / "Supreme_Court" / "2023" / "01"
        output_dir.mkdir(parents=True)
        existing_file = output_dir / "test_case.pdf"
        existing_file.write_bytes(b"%PDF-1.4\n%Test content")

        # Mock hash calculation to return same hash
        sha256 = "same_hash"
        with patch.object(downloader, '_calculate_file_hash', return_value=sha256):
            result = await downloader.save_pdf_file(sample_case_file, b"%PDF-1.4\n%Test content", sha256)

        assert result == existing_file

    async def test_process_case_file_success(self, mock_downloader, sample_case_file):
        """Test successful case file processing."""
        downloader, _ = mock_downloader

        # Mock browser context
        mock_context = AsyncMock()

        # Mock the processing steps
        with patch.object(downloader, 'extract_pdf_url_from_case_page', return_value="https://example.com/test.pdf"):
            with patch.object(downloader, 'download_pdf_file', return_value=(b"%PDF-1.4\n%Test", "hash123")):
                with patch.object(downloader, 'save_pdf_file', return_value=Path("/tmp/test.pdf")):
                    result = await downloader.process_case_file(mock_context, sample_case_file)

        assert result is True
        assert sample_case_file.sha256 == "hash123"
        assert sample_case_file.local_path == Path("/tmp/test.pdf")

    async def test_process_case_file_already_downloaded(self, mock_downloader, sample_case_file):
        """Test processing case file that's already downloaded."""
        downloader, _ = mock_downloader

        # Mock state to return already downloaded
        downloader.state.is_case_already_downloaded.return_value = True

        mock_context = AsyncMock()
        result = await downloader.process_case_file(mock_context, sample_case_file)

        assert result is True

    async def test_process_case_file_no_pdf_url(self, mock_downloader, sample_case_file):
        """Test processing case file when PDF URL can't be extracted."""
        downloader, _ = mock_downloader

        mock_context = AsyncMock()

        # Mock extraction to fail
        with patch.object(downloader, 'extract_pdf_url_from_case_page', return_value=None):
            result = await downloader.process_case_file(mock_context, sample_case_file)

        assert result is False
        downloader.state.mark_case_failed.assert_called_once()

    async def test_is_valid_pdf_valid_content(self, mock_downloader):
        """Test PDF validation with valid content."""
        downloader, _ = mock_downloader

        valid_pdf = b"%PDF-1.4\n%Valid PDF content that is long enough"
        result = downloader._is_valid_pdf(valid_pdf)
        assert result is True

    async def test_is_valid_pdf_invalid_content(self, mock_downloader):
        """Test PDF validation with invalid content."""
        downloader, _ = mock_downloader

        # Test various invalid contents
        invalid_contents = [
            b"<html>Error page</html>",  # HTML content
            b"Too short",  # Too small
            b"Invalid header content",  # Wrong header
        ]

        for content in invalid_contents:
            result = downloader._is_valid_pdf(content)
            assert result is False

    async def test_is_pdf_url_detection(self, mock_downloader):
        """Test PDF URL detection."""
        downloader, _ = mock_downloader

        test_cases = [
            ("https://example.com/file.pdf", True),
            ("https://example.com/download?file=doc.pdf", True),
            ("https://example.com/file.doc", False),
            ("", False),
            (None, False),
        ]

        for url, expected in test_cases:
            result = downloader._is_pdf_url(url)
            assert result == expected

    async def test_sanitize_filename(self, mock_downloader):
        """Test filename sanitization."""
        downloader, _ = mock_downloader

        test_cases = [
            ("normal_file.pdf", "normal_file.pdf"),
            ("file with spaces.pdf", "file with spaces.pdf"),
            ("file<with>bad:chars.pdf", "file_with_bad_chars.pdf"),
            ("very_" + "long_" * 50 + "filename.pdf", "very_" + "long_" * 30 + ".pdf"),  # Truncated
        ]

        for input_filename, expected in test_cases:
            result = downloader._sanitize_filename(input_filename)
            assert len(result) <= 200
            if expected.endswith(".pdf"):
                assert result.endswith(".pdf")

    async def test_extract_pdf_url_from_case_page_success(self, mock_downloader, sample_case_file):
        """Test successful PDF URL extraction from case page."""
        downloader, _ = mock_downloader

        # Mock browser context and page
        mock_context = AsyncMock()
        mock_page = AsyncMock()
        mock_context.new_page.return_value = mock_page

        # Mock successful PDF URL finding
        with patch.object(downloader, '_find_pdf_download_url', return_value="https://example.com/case.pdf"):
            with patch.object(downloader, '_extract_case_metadata', return_value=CaseMetadata()):
                result = await downloader.extract_pdf_url_from_case_page(mock_context, sample_case_file)

        assert result == "https://example.com/case.pdf"
        mock_page.goto.assert_called_once()
        mock_page.close.assert_called_once()

    async def test_extract_pdf_url_from_case_page_failure(self, mock_downloader, sample_case_file):
        """Test PDF URL extraction failure."""
        downloader, _ = mock_downloader

        # Mock browser context and page
        mock_context = AsyncMock()
        mock_page = AsyncMock()
        mock_context.new_page.return_value = mock_page

        # Mock failed PDF URL finding
        with patch.object(downloader, '_find_pdf_download_url', return_value=None):
            with patch.object(downloader, '_extract_case_metadata', return_value=CaseMetadata()):
                result = await downloader.extract_pdf_url_from_case_page(mock_context, sample_case_file)

        assert result is None
        mock_page.close.assert_called_once()


@pytest.mark.asyncio
class TestCaseLawDownloaderIntegration:
    """Integration tests for case-law downloader."""

    async def test_full_download_workflow_mock(self, mock_downloader, sample_case_file, tmp_path):
        """Test full download workflow with mocked components."""
        downloader, mock_session = mock_downloader
        downloader.config.output_dir = tmp_path

        # Mock browser context
        mock_context = AsyncMock()
        mock_page = AsyncMock()
        mock_context.new_page.return_value = mock_page

        # Mock HTTP response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.read.return_value = b"%PDF-1.4\n%Complete test PDF content for integration test"
        mock_session.get.return_value.__aenter__.return_value = mock_response

        # Mock PDF URL extraction
        with patch.object(downloader, '_find_pdf_download_url', return_value="https://example.com/test.pdf"):
            with patch.object(downloader, '_extract_case_metadata', return_value=CaseMetadata(case_number="TEST/123")):
                result = await downloader.process_case_file(mock_context, sample_case_file)

        assert result is True
        assert sample_case_file.local_path is not None
        assert sample_case_file.local_path.exists()
        assert sample_case_file.sha256 is not None
        assert len(sample_case_file.sha256) == 64

        # Verify file was saved correctly
        content = sample_case_file.local_path.read_bytes()
        assert content.startswith(b"%PDF-1.4")

    async def test_error_recovery_workflow(self, mock_downloader, sample_case_file):
        """Test error recovery in download workflow."""
        downloader, _ = mock_downloader

        mock_context = AsyncMock()

        # Test network error handling
        with patch.object(downloader, 'extract_pdf_url_from_case_page', side_effect=Exception("Network error")):
            result = await downloader.process_case_file(mock_context, sample_case_file)

        assert result is False
        downloader.state.mark_case_failed.assert_called_once()

        # Verify error message was recorded
        call_args = downloader.state.mark_case_failed.call_args
        assert "Network error" in call_args[0][1]  # Error message argument