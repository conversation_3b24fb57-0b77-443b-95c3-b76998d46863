"""High-yield pipeline tests for multi-page discovery (year→month→files)."""

from __future__ import annotations

import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

from gazette_scraper.models import ScrapingConfig
from gazette_scraper.pipeline import GazettePipeline


class TestPipelinePagination:
    """Test pipeline pagination through year→month→files discovery."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.config = ScrapingConfig(
            output_dir=self.temp_dir,
            dry_run=True,
            since_year=2024,
            max_threads=1,
            rate_limit=10.0,
            crawler_depth=2,  # Enable month-level traversal for this test
        )

    def teardown_method(self):
        """Clean up test fixtures."""
        import shutil

        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)

    def test_full_pipeline_year_month_files_discovery(self):
        """Test complete pipeline discovery from year→month→files."""
        pipeline = GazettePipeline(self.config)

        # Mock the pipeline methods to avoid real HTTP calls
        from gazette_scraper.models import FileItem, Folder, FolderType

        # Mock year folders
        year_folders = [
            Folder(
                name="2024",
                href="http://test.com/2024",
                folder_type=FolderType.YEAR,
                year=2024,
            )
        ]

        # Mock month folders
        month_folders = [
            Folder(
                name="January",
                href="http://test.com/2024/January",
                folder_type=FolderType.MONTH,
                year=2024,
                month=1,
            ),
            Folder(
                name="February",
                href="http://test.com/2024/February",
                folder_type=FolderType.MONTH,
                year=2024,
                month=2,
            ),
        ]

        # Mock file items
        file_items = [
            FileItem(
                title="Gazette_15-01-2024.pdf",
                href="https://minijust.gov.rw/dumpFile?f=123&t=Gazette_15-01-2024.pdf",
                size_str="1.2 MB",
                listing_url="https://minijust.gov.rw/2024/January",
            ),
            FileItem(
                title="Extraordinary_Gazette_20-01-2024.pdf",
                href="https://minijust.gov.rw/dumpFile?f=124&t=Extraordinary_Gazette_20-01-2024.pdf",
                size_str="856 KB",
                listing_url="https://minijust.gov.rw/2024/January",
            ),
        ]

        with patch.object(pipeline, "discover_years", return_value=year_folders):
            with patch.object(pipeline, "discover_months", return_value=month_folders):
                with patch.object(pipeline, "discover_files", return_value=file_items):
                    result = pipeline.run(dry_run=True)

        # Verify results - should discover files (2 files × 2 months = 4 total)
        assert result.total_discovered == 4
        assert result.downloaded == 0  # Dry run
        assert result.errors == 0
        assert result.skipped == 4  # All files skipped in dry run

    @patch("gazette_scraper.client.GazetteHTTPClient")
    def test_pipeline_handles_empty_months(self, mock_client_class):
        """Test pipeline handles months with no files gracefully."""
        mock_client = Mock()
        mock_client_class.return_value = mock_client

        # Mock responses - empty month page
        empty_month_html = "<html><body><p>No files found</p></body></html>"

        mock_response = Mock()
        mock_response.text = empty_month_html
        mock_response.status_code = 200
        mock_client.get.return_value = mock_response

        pipeline = GazettePipeline(self.config)

        # Test discover_months with empty response
        from gazette_scraper.models import Folder, FolderType

        year_folder = Folder(
            name="2024",
            href="http://test.com/2024",
            folder_type=FolderType.YEAR,
            year=2024,
        )
        months = pipeline.discover_months(year_folder)
        assert months == []

    @patch("gazette_scraper.client.GazetteHTTPClient")
    def test_pipeline_file_conversion_and_validation(self, mock_client_class):
        """Test pipeline file item conversion and validation."""
        mock_client = Mock()
        mock_client_class.return_value = mock_client

        pipeline = GazettePipeline(self.config)

        # Test file validation
        from gazette_scraper.models import FileItem, Folder, FolderType

        valid_file = FileItem(
            title="Official_Gazette_15-01-2024.pdf",
            href="dumpFile?f=123&t=Official_Gazette_15-01-2024.pdf",
            size_str="1.2 MB",
            listing_url="http://test.com/2024/01",
        )

        month_folder = Folder(
            name="January",
            href="http://test.com/2024/01",
            folder_type=FolderType.MONTH,
            year=2024,
            month=1,
        )

        # Test conversion
        gazette_file = pipeline._convert_file_item(valid_file, month_folder)

        if gazette_file:  # Conversion might filter out invalid files
            assert gazette_file.year == 2024
            assert gazette_file.month == 1
            assert "dumpFile" in str(gazette_file.download_url)

    @patch("gazette_scraper.client.GazetteHTTPClient")
    def test_pipeline_error_handling_during_discovery(self, mock_client_class):
        """Test pipeline error handling during multi-level discovery."""
        mock_client = Mock()
        mock_client_class.return_value = mock_client

        # Mock HTTP error during discovery
        mock_client.get.side_effect = Exception("Network timeout")

        pipeline = GazettePipeline(self.config)

        # Test error handling in discovery methods
        years = pipeline.discover_years("http://test.com")
        assert years == []  # Should return empty list on error

        from gazette_scraper.models import Folder, FolderType

        year_folder = Folder(
            name="2024",
            href="http://test.com/2024",
            folder_type=FolderType.YEAR,
            year=2024,
        )
        months = pipeline.discover_months(year_folder)
        assert months == []  # Should return empty list on error

    @patch("gazette_scraper.client.GazetteHTTPClient")
    def test_pipeline_manifest_building(self, mock_client_class):
        """Test pipeline manifest building from discovered files."""
        mock_client = Mock()
        mock_client_class.return_value = mock_client

        pipeline = GazettePipeline(self.config)

        # Mock empty discovery to test manifest building
        with patch.object(pipeline, "discover_years", return_value=[]):
            manifest = pipeline.build_manifest(
                dry_run=True, since_year=2024, out_dir=self.temp_dir
            )

            assert manifest.total_discovered == 0
            assert manifest.by_year == {}
            assert manifest.by_month == {}

    def test_pipeline_date_extraction_edge_cases(self):
        """Test pipeline date extraction from various filename formats."""
        pipeline = GazettePipeline(self.config)

        # Test various date formats
        test_cases = [
            ("gazette_15-01-2024.pdf", 2024, 1, (2024, 1, 15)),
            ("Extraordinary_Gazette_03-02-2024.pdf", 2024, 2, (2024, 2, 3)),
            ("official_gazette_28-12-2023.pdf", 2023, 12, (2023, 12, 28)),
            ("simple_gazette.pdf", 2024, 3, (2024, 3, 1)),  # Fallback to 1st
            ("", 2024, 6, (2024, 6, 1)),  # Empty filename fallback
        ]

        for filename, year, month, expected in test_cases:
            date = pipeline._extract_pub_date_from_filename(filename, year, month)
            if date:
                assert (date.year, date.month, date.day) == expected

    @patch("gazette_scraper.client.GazetteHTTPClient")
    def test_pipeline_dry_run_vs_live_mode_switching(self, mock_client_class):
        """Test pipeline behavior differences between dry-run and live mode."""
        mock_client = Mock()
        mock_client_class.return_value = mock_client

        # Mock empty responses
        mock_response = Mock()
        mock_response.text = "<html><body></body></html>"
        mock_response.status_code = 200
        mock_client.get.return_value = mock_response

        pipeline = GazettePipeline(self.config)

        # Test dry run mode
        result_dry = pipeline.run(dry_run=True)
        assert result_dry.downloaded == 0  # No downloads in dry run

        # Test config switching affects behavior
        self.config.dry_run = False
        result_live = pipeline.run(dry_run=False)
        assert result_live.downloaded == 0  # Still 0 since no files discovered

        # Verify dry_run parameter overrides config
        self.config.dry_run = False
        result_override = pipeline.run(dry_run=True)
        assert result_override.downloaded == 0  # Override to dry run
