"""Tests for edge error paths including 410 -> refresh -> success scenarios."""

from __future__ import annotations

import tempfile
from pathlib import Path
from unittest.mock import MagicMock, patch

from gazette_scraper.models import ScrapingConfig
from gazette_scraper.pipeline import GazettePipeline


class TestEdgeErrorPaths:
    """Test edge cases and error recovery scenarios."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())

    def teardown_method(self):
        """Clean up test fixtures."""
        import shutil

        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)

    def test_http_410_error_handling(self):
        """Test handling of 410 Gone responses."""
        from gazette_scraper.client import GazetteHTTPClient

        client = GazetteHTTPClient(rate_limit=10.0)

        # Test that client handles 410 gracefully without crashing
        assert client is not None
        assert client.rate_limit == 10.0

    @patch("gazette_scraper.client.GazetteHTTPClient.get")
    def test_download_retry_after_410(self, mock_get):
        """Test download retry logic after receiving 410 Gone."""
        # Mock 410 response for first attempt
        response_410 = MagicMock()
        response_410.status_code = 410
        response_410.raise_for_status.side_effect = Exception("410 Gone")

        # Mock successful response for retry
        response_success = MagicMock()
        response_success.status_code = 200
        response_success.iter_content.return_value = [b"PDF content after retry"]
        response_success.headers = {"content-length": "500"}

        # Return 410 first, then success
        mock_get.side_effect = [response_410, response_success]

        ScrapingConfig(
            output_dir=self.temp_dir,
            dry_run=False,
            since_year=2024,
            max_threads=1,
        )

        # Test the client's retry behavior specifically
        from gazette_scraper.client import GazetteHTTPClient

        client = GazetteHTTPClient(rate_limit=10.0)

        # This should trigger retry logic in the client
        try:
            client.get("https://test.com/file?f=123")
        except Exception as e:
            # First call should fail with 410
            assert "410" in str(e)

        # Second call should succeed
        response = client.get("https://test.com/file?f=123")
        assert response.status_code == 200

    @patch("gazette_scraper.client.GazetteHTTPClient.get")
    def test_network_timeout_retry(self, mock_get):
        """Test network timeout handling and retry."""
        # Mock timeout for first few attempts
        mock_get.side_effect = [
            Exception("Connection timeout"),
            Exception("Read timeout"),
            MagicMock(status_code=200, text="Success after retries"),
        ]

        config = ScrapingConfig(
            output_dir=self.temp_dir,
            dry_run=True,
            since_year=2024,
            max_threads=1,
        )

        GazettePipeline(config)

        # Test client retry behavior
        from gazette_scraper.client import GazetteHTTPClient

        client = GazetteHTTPClient(rate_limit=1.0, max_retries=3)

        # Should eventually succeed after retries
        try:
            response = client.get("https://test.com/file")
            assert response.status_code == 200
        except Exception:
            # If all retries fail, should raise the last exception
            pass

    def test_malformed_html_recovery(self):
        """Test recovery from malformed HTML responses."""
        from gazette_scraper.parser import GazetteParser

        malformed_html = """
        <html>
        <body>
            <div class="filelist">
                <table>
                    <tbody>
                        <tr>
                            <td><a href="broken-link">No closing tag
                            <td>1.2 MB</td>
        <!-- Missing closing tags -->
        """

        valid_html = """
        <html>
        <body>
            <div class="filelist">
                <table>
                    <tbody>
                        <tr>
                            <td><a href="index.php?eID=dumpFile&f=123&t=f&token=abc">valid_file.pdf</a></td>
                            <td>1.2 MB</td>
                            <td>15/01/2024</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </body>
        </html>
        """

        parser = GazetteParser()

        # Should handle malformed HTML without crashing
        try:
            files1 = parser.parse_file_page(malformed_html, "https://test.com")
            # Malformed HTML might return empty list or partial results
            assert isinstance(files1, list)
        except Exception:
            # Parser might raise exception for severely malformed HTML
            pass

        # Should successfully parse valid HTML
        files2 = parser.parse_file_page(valid_html, "https://test.com")
        assert len(files2) == 1
        assert files2[0].title == "valid_file.pdf"

    @patch("gazette_scraper.client.GazetteHTTPClient.get")
    def test_partial_download_recovery(self, mock_get):
        """Test recovery from partial/corrupted downloads."""
        # Mock response with content that gets cut off
        partial_response = MagicMock()
        partial_response.status_code = 200
        partial_response.headers = {"content-length": "1000"}
        # Return less content than expected
        partial_response.iter_content.return_value = [b"partial"]

        complete_response = MagicMock()
        complete_response.status_code = 200
        complete_response.headers = {"content-length": "100"}
        complete_response.iter_content.return_value = [
            b"complete PDF content that matches expected length"
        ]

        mock_get.side_effect = [partial_response, complete_response]

        ScrapingConfig(
            output_dir=self.temp_dir,
            dry_run=False,
            since_year=2024,
            max_threads=1,
        )

        # Test download validation in storage
        from gazette_scraper.storage import LocalStorage

        storage = LocalStorage(self.temp_dir)

        # First download - partial content
        try:
            content1 = b"partial"  # Simulated partial download
            # This might fail validation if size checking is implemented
            path1 = self.temp_dir / "partial.pdf"
            storage.save_pdf(content1, path1)

            # Check if the file size validation works
            expected_size = 1000  # From content-length header
            actual_size = len(content1)
            if actual_size != expected_size:
                # Should trigger retry logic
                pass
        except Exception:
            # Partial download might be rejected
            pass

        # Second download - complete content
        content2 = b"complete PDF content that matches expected length"
        path2 = self.temp_dir / "complete.pdf"
        sha256 = storage.save_pdf(content2, path2)

        assert path2.exists()
        assert len(sha256) == 64  # Valid SHA256

    @patch("gazette_scraper.client.GazetteHTTPClient.get")
    def test_rate_limit_exceeded_recovery(self, mock_get):
        """Test recovery from rate limit exceeded errors."""
        # Mock 429 Too Many Requests responses
        rate_limit_response = MagicMock()
        rate_limit_response.status_code = 429
        rate_limit_response.headers = {"retry-after": "1"}
        rate_limit_response.raise_for_status.side_effect = Exception(
            "429 Too Many Requests"
        )

        success_response = MagicMock()
        success_response.status_code = 200
        success_response.text = "Success after rate limit"

        # Return rate limit error, then success
        mock_get.side_effect = [rate_limit_response, success_response]

        # Test with aggressive rate limiting
        from gazette_scraper.client import GazetteHTTPClient

        client = GazetteHTTPClient(rate_limit=0.1)  # Very fast rate

        # Should handle rate limiting gracefully
        try:
            response = client.get("https://test.com/api")
            # If retry logic is implemented, should eventually succeed
            if response:
                assert response.status_code == 200
        except Exception as e:
            # Should contain rate limit information
            assert "429" in str(e) or "rate" in str(e).lower()

    def test_disk_space_exhaustion_handling(self):
        """Test handling of disk space exhaustion during downloads."""
        ScrapingConfig(
            output_dir=self.temp_dir,
            dry_run=False,
            since_year=2024,
            max_threads=1,
        )

        from gazette_scraper.storage import LocalStorage

        storage = LocalStorage(self.temp_dir)

        # Simulate very large content that might exhaust disk space
        large_content = b"x" * (10 * 1024 * 1024)  # 10MB of data

        try:
            # This should normally succeed unless disk is actually full
            test_path = self.temp_dir / "large_file.pdf"
            sha256 = storage.save_pdf(large_content, test_path)

            assert test_path.exists()
            assert len(sha256) == 64

            # Clean up large file
            test_path.unlink()

        except OSError as e:
            # Handle actual disk space issues gracefully
            if "No space left" in str(e):
                # This is expected if disk is actually full
                pass
            else:
                raise

    def test_database_initialization(self):
        """Test database initialization works correctly."""
        from gazette_scraper.state import ScrapingState

        db_path = self.temp_dir / "test.db"
        state = ScrapingState(db_path)

        # Should create database without errors
        assert db_path.exists()

        # Should be able to get stats
        stats = state.get_stats()
        assert isinstance(stats, dict)
        assert "total_discovered" in stats
