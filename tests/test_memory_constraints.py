"""
Memory and Resource Constraints Tests

Tests to validate the scraper handles Cloud Run memory limits (4Gi) 
and doesn't leak memory during long-running operations.
"""

import asyncio
import gc
import logging
import psutil
import pytest
import tempfile
import tracemalloc
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

from gazette_scraper.caselaw.models import CaseLawConfig, CourtNode, NavigationStatus, CaseLawFile
from gazette_scraper.caselaw.navigator import CaseLawNavigator
from gazette_scraper.caselaw.downloader import CaseLawDownloader
from gazette_scraper.caselaw.pipeline import CaseLawPipeline
from gazette_scraper.caselaw.state import CaseLawState

logger = logging.getLogger(__name__)


class MemoryTracker:
    """Track memory usage during tests."""
    
    def __init__(self):
        self.process = psutil.Process()
        self.initial_memory = self.get_memory_mb()
        self.peak_memory = self.initial_memory
        self.snapshots = []
        
    def get_memory_mb(self):
        """Get current memory usage in MB."""
        return self.process.memory_info().rss / 1024 / 1024
    
    def take_snapshot(self, label: str = ""):
        """Take a memory snapshot."""
        current_memory = self.get_memory_mb()
        self.peak_memory = max(self.peak_memory, current_memory)
        self.snapshots.append((label, current_memory))
        return current_memory
    
    def get_memory_increase(self):
        """Get total memory increase since start."""
        return self.get_memory_mb() - self.initial_memory
    
    def get_peak_increase(self):
        """Get peak memory increase."""
        return self.peak_memory - self.initial_memory


class TestMemoryConstraints:
    """Test memory and resource constraint handling."""

    @pytest.fixture
    def memory_optimized_config(self):
        """Configuration optimized for memory efficiency."""
        return CaseLawConfig(
            browser_timeout=30000,
            navigation_delay=1.0,
            page_load_timeout=15000,
            element_timeout=10000,
            max_retry_attempts=3,
            retry_delay=5.0,
            headless=True,
            screenshot_on_error=False,  # Disable to save memory
            debug_mode=False,
            max_concurrent_downloads=1,  # Single download to limit memory
            max_browser_contexts=1,      # Single context
        )

    @pytest.fixture
    def temp_state_db(self):
        """Temporary state database for testing."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = Path(f.name)
        yield db_path
        if db_path.exists():
            db_path.unlink()

    @pytest.fixture
    def memory_tracker(self):
        """Memory tracking fixture."""
        tracker = MemoryTracker()
        tracker.take_snapshot("test_start")
        yield tracker
        tracker.take_snapshot("test_end")
        
        # Log memory usage summary
        logger.info(f"Memory usage summary:")
        logger.info(f"  Initial: {tracker.initial_memory:.2f} MB")
        logger.info(f"  Peak: {tracker.peak_memory:.2f} MB")
        logger.info(f"  Final: {tracker.get_memory_mb():.2f} MB")
        logger.info(f"  Peak increase: {tracker.get_peak_increase():.2f} MB")

    @pytest.mark.asyncio
    async def test_memory_usage_during_navigation(self, memory_optimized_config, temp_state_db, memory_tracker):
        """Test memory usage during tree navigation."""
        state = CaseLawState(temp_state_db)
        navigator = CaseLawNavigator(memory_optimized_config)
        
        # Create multiple nodes to simulate navigation
        nodes = []
        for i in range(20):
            node = CourtNode(
                court_name=f"Court {i}",
                full_path=f"Court {i}",
                node_type="court",
                status=NavigationStatus.PENDING
            )
            nodes.append(node)
            state.save_court_node(node)
            
            # Take memory snapshot every 5 nodes
            if i % 5 == 0:
                memory_tracker.take_snapshot(f"after_node_{i}")
        
        # Memory increase should be reasonable
        memory_increase = memory_tracker.get_memory_increase()
        assert memory_increase < 100, f"Excessive memory increase: {memory_increase:.2f} MB"
        
        # Clean up and force garbage collection
        del nodes
        gc.collect()
        
        # Memory should be released (allow for reasonable variance in memory measurement)
        final_memory = memory_tracker.take_snapshot("after_cleanup")
        cleanup_memory = final_memory - memory_tracker.initial_memory
        # Allow for significant variance in memory cleanup due to garbage collection timing
        # and Python's memory management behavior in test environments
        max_allowed_memory = max(memory_increase * 2.0, 20.0)  # Allow 2x increase or 20MB, whichever is larger
        assert cleanup_memory <= max_allowed_memory, f"Excessive memory not released: {cleanup_memory:.2f}MB > {max_allowed_memory:.2f}MB"

    @pytest.mark.asyncio
    async def test_browser_memory_management(self, memory_optimized_config, temp_state_db, memory_tracker):
        """Test browser memory management and cleanup."""
        state = CaseLawState(temp_state_db)
        navigator = CaseLawNavigator(memory_optimized_config)

        # Mock browser operations
        with patch('gazette_scraper.caselaw.navigator.async_playwright') as mock_playwright:
            mock_browser = AsyncMock()
            mock_context = AsyncMock()
            mock_page = AsyncMock()

            # Create a proper async mock for playwright
            mock_playwright_instance = AsyncMock()
            mock_playwright.return_value.start = AsyncMock(return_value=mock_playwright_instance)
            mock_playwright_instance.chromium.launch.return_value = mock_browser
            mock_browser.new_context.return_value = mock_context
            mock_context.new_page.return_value = mock_page

            # Start navigator
            await navigator.start()
            memory_tracker.take_snapshot("after_browser_start")

            # Simulate multiple page operations
            for i in range(10):
                # Simulate page navigation
                mock_page.goto = AsyncMock()
                mock_page.wait_for_load_state = AsyncMock()

                await mock_page.goto(f"https://example.com/page{i}")
                await mock_page.wait_for_load_state("networkidle")

                if i % 3 == 0:
                    memory_tracker.take_snapshot(f"after_page_{i}")

            # Close navigator
            await navigator.close()
            memory_tracker.take_snapshot("after_browser_close")

            # Verify browser cleanup was called
            mock_browser.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_large_pdf_download_memory(self, memory_optimized_config, temp_state_db, memory_tracker):
        """Test memory usage during large PDF downloads."""
        state = CaseLawState(temp_state_db)
        downloader = CaseLawDownloader(memory_optimized_config, state)

        # Initialize the downloader session
        await downloader.start()

        # Mock large PDF download with valid PDF header
        large_pdf_content = b"%PDF-1.4\n" + b"PDF content" * 100000  # ~1MB of content with valid header

        with patch.object(downloader.session, 'get') as mock_get:
            mock_response = AsyncMock()
            mock_response.read.return_value = large_pdf_content
            mock_response.status = 200
            mock_response.headers = {'content-length': str(len(large_pdf_content))}

            mock_get.return_value.__aenter__.return_value = mock_response

            memory_tracker.take_snapshot("before_download")

            # Download multiple files
            for i in range(5):
                case_file = MagicMock()
                case_file.filename = f"large_file_{i}.pdf"
                case_file.title = f"Large Case {i}"
                case_file.case_title = f"Large Case {i}"
                case_file.court_name = "Test Court"
                case_file.year = 2024
                case_file.month = 1
                case_file.day = 1
                case_file.download_url = f"https://example.com/large_file_{i}.pdf"
                case_file.case_page_url = "https://example.com/case"
                case_file.listing_url = "https://example.com/listing"
                case_file.navigation_path = "Test Court/2024/1/1"

                result = await downloader.download_pdf_file(
                    case_file, f"https://example.com/large_file_{i}.pdf"
                )

                assert result is not None
                content, sha256 = result
                assert len(content) > 1000000  # Verify large content
                assert len(content) == len(large_pdf_content)

                # Clear reference to downloaded content
                del content, result
                gc.collect()

                memory_tracker.take_snapshot(f"after_download_{i}")

            # Memory increase should be reasonable despite large downloads
            memory_increase = memory_tracker.get_memory_increase()
            assert memory_increase < 200, f"Excessive memory increase: {memory_increase:.2f} MB"

        # Clean up
        await downloader.close()

    @pytest.mark.asyncio
    async def test_memory_leak_detection(self, memory_optimized_config, temp_state_db):
        """Test for memory leaks during repeated operations."""
        # Start memory tracing
        tracemalloc.start()
        
        state = CaseLawState(temp_state_db)
        pipeline = CaseLawPipeline(memory_optimized_config)
        
        # Take initial snapshot
        snapshot1 = tracemalloc.take_snapshot()
        
        # Perform repeated operations
        for iteration in range(3):
            # Create and process nodes
            nodes = []
            for i in range(10):
                node = CourtNode(
                    court_name=f"Court {iteration}_{i}",
                    full_path=f"Court {iteration}_{i}",
                    node_type="court",
                    status=NavigationStatus.PENDING
                )
                nodes.append(node)
                state.save_court_node(node)
            
            # Simulate processing
            await asyncio.sleep(0.1)
            
            # Clean up
            del nodes
            gc.collect()
        
        # Take final snapshot
        snapshot2 = tracemalloc.take_snapshot()
        
        # Compare snapshots
        top_stats = snapshot2.compare_to(snapshot1, 'lineno')
        
        # Check for significant memory increases
        total_size_diff = sum(stat.size_diff for stat in top_stats[:10])
        
        # Stop tracing
        tracemalloc.stop()
        
        # Memory difference should be minimal
        assert total_size_diff < 1024 * 1024, f"Potential memory leak detected: {total_size_diff} bytes"

    @pytest.mark.asyncio
    async def test_cloud_run_memory_limit_compliance(self, memory_optimized_config, temp_state_db, memory_tracker):
        """Test compliance with Cloud Run 4Gi memory limit."""
        state = CaseLawState(temp_state_db)
        pipeline = CaseLawPipeline(memory_optimized_config)
        
        # Simulate intensive operations
        memory_tracker.take_snapshot("baseline")
        
        # Create many nodes (simulating large court structure)
        for court_idx in range(5):
            for year in range(2020, 2025):
                for month in range(1, 13):
                    for day in range(1, 31):
                        node = CourtNode(
                            court_name=f"Court {court_idx}",
                            year=year,
                            month=month,
                            day=day,
                            full_path=f"Court {court_idx}/{year}/{month}/Day {day}",
                            node_type="day",
                            status=NavigationStatus.PENDING
                        )
                        state.save_court_node(node)
        
        memory_tracker.take_snapshot("after_node_creation")
        
        # Simulate case file creation
        for i in range(100):
            case_file = CaseLawFile(
                title=f"Case {i}",
                filename=f"case_{i}.pdf",
                case_title=f"Case {i}",
                court_name="Court 0",
                year=2024,
                month=1,
                day=(i % 30) + 1,
                download_url=f"https://example.com/case{i}.pdf",
                case_page_url=f"https://example.com/case{i}",
                listing_url=f"https://example.com/list{i}",
                navigation_path=f"Court 0/2024/1/Day {(i % 30) + 1}"
            )
            state.save_case_file(case_file)
        
        memory_tracker.take_snapshot("after_case_creation")
        
        # Check memory usage
        current_memory = memory_tracker.get_memory_mb()
        
        # Should stay well below 4Gi (4096 MB) - use 3Gi as safety margin
        assert current_memory < 3072, f"Memory usage too high: {current_memory:.2f} MB (limit: 3072 MB)"
        
        # Clean up
        gc.collect()
        memory_tracker.take_snapshot("after_cleanup")

    @pytest.mark.asyncio
    async def test_concurrent_operation_memory_usage(self, memory_optimized_config, temp_state_db, memory_tracker):
        """Test memory usage during concurrent operations."""
        state = CaseLawState(temp_state_db)
        
        async def memory_intensive_task(task_id: int):
            """Simulate memory-intensive task."""
            # Create temporary data
            data = [f"data_{task_id}_{i}" for i in range(1000)]
            
            # Simulate processing
            await asyncio.sleep(0.1)
            
            # Process data
            result = len(data)
            
            # Clean up
            del data
            return result
        
        memory_tracker.take_snapshot("before_concurrent_tasks")
        
        # Run concurrent tasks
        tasks = [memory_intensive_task(i) for i in range(10)]
        results = await asyncio.gather(*tasks)
        
        memory_tracker.take_snapshot("after_concurrent_tasks")
        
        # Verify all tasks completed
        assert len(results) == 10
        assert all(r == 1000 for r in results)
        
        # Force garbage collection
        gc.collect()
        memory_tracker.take_snapshot("after_gc")
        
        # Memory increase should be reasonable
        memory_increase = memory_tracker.get_memory_increase()
        assert memory_increase < 50, f"Excessive memory increase: {memory_increase:.2f} MB"

    def test_resource_cleanup_on_exception(self, memory_optimized_config, temp_state_db, memory_tracker):
        """Test that resources are properly cleaned up when exceptions occur."""
        state = CaseLawState(temp_state_db)
        
        class TestResource:
            def __init__(self):
                self.data = [0] * 10000  # Allocate some memory
                self.cleaned_up = False
            
            def cleanup(self):
                self.data = None
                self.cleaned_up = True
        
        resources = []
        
        try:
            # Create resources
            for i in range(5):
                resource = TestResource()
                resources.append(resource)
                memory_tracker.take_snapshot(f"resource_{i}")
            
            # Simulate exception
            raise Exception("Simulated error")
            
        except Exception:
            # Clean up resources
            for resource in resources:
                resource.cleanup()
            
            memory_tracker.take_snapshot("after_cleanup")
        
        # Verify cleanup
        assert all(r.cleaned_up for r in resources)
        
        # Force garbage collection
        del resources
        gc.collect()
        
        final_memory = memory_tracker.take_snapshot("final")
        
        # Memory should be released
        memory_increase = memory_tracker.get_memory_increase()
        assert memory_increase < 20, f"Memory not properly released: {memory_increase:.2f} MB"
