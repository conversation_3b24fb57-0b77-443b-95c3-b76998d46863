"""Final targeted tests to reach 85% coverage."""

from __future__ import annotations

import tempfile
from pathlib import Path
from unittest.mock import Mock

from gazette_scraper.models import GazetteFile, Manifest, ScrapingConfig
from gazette_scraper.pipeline import GazettePipeline


class TestPipelineFinalCoverage:
    """Final targeted tests for specific coverage gaps."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.config = ScrapingConfig(
            output_dir=self.temp_dir,
            dry_run=False,  # Test non-dry-run path
            since_year=2024,
            max_threads=1,
            rate_limit=10.0,
        )

    def teardown_method(self):
        """Clean up test fixtures."""
        import shutil
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)

    def test_download_files_no_files_to_download(self):
        """Test download_files when all files are already downloaded."""
        pipeline = GazettePipeline(self.config)

        # Mock state to return that all files are already downloaded
        pipeline.state.is_already_downloaded = Mock(return_value=True)

        from pydantic import HttpUrl
        test_file = GazetteFile(
            title="Test Gazette",
            filename="test.pdf",
            issue_title="Test Issue",
            download_url=HttpUrl("https://example.com/test.pdf"),
            source_url=HttpUrl("https://example.com/test.pdf"),
            listing_url=HttpUrl("https://example.com/listing"),
            year=2024,
            month=1,
        )

        manifest = Manifest(files=[test_file], total_discovered=1)

        downloaded, errors = pipeline.download_files(manifest)

        # Should return (0, 0) when no files need downloading
        assert downloaded == 0
        assert errors == 0

    def test_run_non_dry_run_path(self):
        """Test run method in non-dry-run mode."""
        pipeline = GazettePipeline(self.config)

        # Mock all the dependencies to avoid actual network calls
        pipeline._check_robots_txt = Mock(return_value=True)
        pipeline.build_manifest = Mock(return_value=Manifest(total_discovered=0))
        pipeline.download_files = Mock(return_value=(0, 0))

        result = pipeline.run(dry_run=False)

        # Should have called download_files instead of skipping
        pipeline.download_files.assert_called_once()
        assert result.downloaded == 0
        assert result.skipped == 0

    def test_build_manifest_year_filtering(self):
        """Test build_manifest filters years correctly."""
        pipeline = GazettePipeline(self.config)

        from gazette_scraper.models import Folder, FolderType

        # Mock discover_years to return multiple years
        year_folders = [
            Folder(name="2020", href="https://test.com/2020", folder_type=FolderType.YEAR, year=2020),
            Folder(name="2024", href="https://test.com/2024", folder_type=FolderType.YEAR, year=2024),
            Folder(name="2025", href="https://test.com/2025", folder_type=FolderType.YEAR, year=2025),
        ]
        pipeline.discover_years = Mock(return_value=year_folders)
        pipeline.discover_files = Mock(return_value=[])

        manifest = pipeline.build_manifest(
            dry_run=True,
            since_year=2024,  # Should filter to only 2024 and 2025
            out_dir=self.temp_dir
        )

        # Should have filtered years
        assert manifest.total_discovered == 0

    def test_single_file_download_non_200_status(self):
        """Test _download_single_file with non-200 HTTP status."""
        pipeline = GazettePipeline(self.config)

        # Mock state methods
        pipeline.state.get_failed_attempts = Mock(return_value=0)
        pipeline.state.record_failure = Mock()
        pipeline.storage.get_file_path = Mock(return_value=self.temp_dir / "test.pdf")

        # Mock HTTP response with error status
        mock_response = Mock()
        mock_response.status_code = 404
        pipeline.client.get = Mock(return_value=mock_response)

        from pydantic import HttpUrl
        test_file = GazetteFile(
            title="Test Gazette",
            filename="test.pdf",
            issue_title="Test Issue",
            download_url=HttpUrl("https://example.com/test.pdf"),
            source_url=HttpUrl("https://example.com/test.pdf"),
            listing_url=HttpUrl("https://example.com/listing"),
            year=2024,
            month=1,
        )

        result = pipeline._download_single_file(test_file)

        # Should fail and record the failure
        assert result is False
        pipeline.state.record_failure.assert_called_once()

    def test_gcs_upload_in_download(self):
        """Test GCS upload functionality during file download."""
        # Create config with GCS bucket
        config_with_gcs = ScrapingConfig(
            output_dir=self.temp_dir,
            gcs_bucket="test-bucket",
            dry_run=False,
        )

        pipeline = GazettePipeline(config_with_gcs)

        # Mock all the dependencies
        pipeline.state.get_failed_attempts = Mock(return_value=0)
        pipeline.state.has_sha256 = Mock(return_value=False)
        pipeline.state.record_download = Mock()

        test_path = self.temp_dir / "test.pdf"
        pipeline.storage.get_file_path = Mock(return_value=test_path)
        pipeline.storage.save_pdf = Mock(return_value="abc123" * 8)

        # Mock GCS storage
        pipeline.gcs_storage.upload_file = Mock()
        pipeline.manifest.append_file = Mock()

        # Mock successful HTTP response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.content = b"PDF content"
        pipeline.client.get = Mock(return_value=mock_response)

        from pydantic import HttpUrl
        test_file = GazetteFile(
            title="Test Gazette",
            filename="test.pdf",
            issue_title="Test Issue",
            download_url=HttpUrl("https://example.com/test.pdf"),
            source_url=HttpUrl("https://example.com/test.pdf"),
            listing_url=HttpUrl("https://example.com/listing"),
            year=2024,
            month=1,
        )

        result = pipeline._download_single_file(test_file)

        # Should succeed and upload to GCS
        assert result is True
        pipeline.gcs_storage.upload_file.assert_called_once()
