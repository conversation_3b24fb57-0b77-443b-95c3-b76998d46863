"""Tests for Supabase client integration."""

import sqlite3
from unittest.mock import MagicMock, patch

import pytest

from gazette_scraper.state import ScrapingState
from gazette_scraper.supabase_client import (
    get_supabase_client,
    log_error,
    sync_unsynced_files,
    upsert_file,
)


class TestSupabaseClient:
    """Test cases for Supabase client functionality."""

    def setup_method(self):
        """Reset global client state before each test."""
        # We need to reset the global state in the module
        import gazette_scraper.supabase_client as supabase_module
        supabase_module._client = None

    @patch("gazette_scraper.supabase_client.load_config")
    @patch("supabase.create_client")
    def test_get_supabase_client_success(self, mock_create_client, mock_load_config):
        """Test successful Supabase client initialization."""
        # Setup
        mock_config = MagicMock()
        mock_config.supabase_url = "https://test.supabase.co"
        mock_config.supabase_key = "test_key"
        mock_load_config.return_value = mock_config

        mock_client = MagicMock()
        mock_create_client.return_value = mock_client

        # Execute
        client = get_supabase_client()

        # Assert
        assert client == mock_client
        mock_create_client.assert_called_once_with(
            "https://test.supabase.co",
            "test_key"
        )

    @patch("gazette_scraper.supabase_client.load_config")
    def test_get_supabase_client_no_config(self, mock_load_config):
        """Test Supabase client when configuration is missing."""
        # Setup
        mock_config = MagicMock()
        mock_config.supabase_url = None
        mock_config.supabase_key = None
        mock_load_config.return_value = mock_config

        # Execute & Assert
        client = get_supabase_client()
        assert client is None

    @patch("gazette_scraper.supabase_client.load_config")
    @patch("supabase.create_client")
    def test_get_supabase_client_caching(self, mock_create_client, mock_load_config):
        """Test that Supabase client is cached after first creation."""
        # Setup
        mock_config = MagicMock()
        mock_config.supabase_url = "https://test.supabase.co"
        mock_config.supabase_key = "test_key"
        mock_load_config.return_value = mock_config

        mock_client = MagicMock()
        mock_create_client.return_value = mock_client

        # Execute - call twice
        client1 = get_supabase_client()
        client2 = get_supabase_client()

        # Assert - create_client should only be called once
        assert client1 == client2 == mock_client
        mock_create_client.assert_called_once()

    @patch("gazette_scraper.supabase_client.get_supabase_client")
    def test_upsert_file_success(self, mock_get_client, sample_gazette_file):
        """Test successful file upsert to Supabase."""
        # Setup
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client

        mock_table = MagicMock()
        mock_client.table.return_value = mock_table

        mock_upsert = MagicMock()
        mock_table.upsert.return_value = mock_upsert

        mock_execute = MagicMock()
        mock_upsert.execute.return_value = mock_execute
        mock_execute.data = [{"id": 1}]

        # Execute
        result = upsert_file(sample_gazette_file, "gazette_pdfs/2024/01/test.pdf", "abc123hash")

        # Assert
        assert result is True
        mock_client.table.assert_called_once_with("gazette_files")

        # Verify the upsert call with expected data structure (matching actual implementation)
        {
            "sha256": "abc123hash",
            "year": sample_gazette_file.year,
            "month": sample_gazette_file.month,
            "issue_title": sample_gazette_file.title,
            "gcs_path": "gazette_pdfs/2024/01/test.pdf",
            "size_bytes": sample_gazette_file.size_bytes or 0,
            "discovered_at": sample_gazette_file.discovered_at.isoformat(),
            "downloaded_at": mock_table.upsert.call_args[0][0]["downloaded_at"],  # Will be generated
            "filename": sample_gazette_file.filename,
            "download_url": str(sample_gazette_file.download_url),
            "listing_url": str(sample_gazette_file.listing_url),
            "pub_date": sample_gazette_file.pub_date.isoformat() if sample_gazette_file.pub_date else None,
        }
        # Check the upsert was called correctly (but ignore the timestamp which is dynamic)
        mock_table.upsert.assert_called_once()
        call_args = mock_table.upsert.call_args
        assert call_args[1]["on_conflict"] == "sha256"
        actual_data = call_args[0][0]
        assert actual_data["sha256"] == "abc123hash"
        assert actual_data["gcs_path"] == "gazette_pdfs/2024/01/test.pdf"

    @patch("gazette_scraper.supabase_client.get_supabase_client")
    def test_upsert_file_no_client(self, mock_get_client, sample_gazette_file):
        """Test file upsert when Supabase client is not available."""
        # Setup
        mock_get_client.return_value = None

        # Execute
        result = upsert_file(sample_gazette_file, "gazette_pdfs/2024/01/test.pdf", "abc123hash")

        # Assert
        assert result is False

    @patch("gazette_scraper.supabase_client.get_supabase_client")
    def test_upsert_file_exception(self, mock_get_client, sample_gazette_file):
        """Test file upsert when Supabase operation fails."""
        # Setup
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client

        mock_table = MagicMock()
        mock_client.table.return_value = mock_table
        mock_table.upsert.side_effect = Exception("Database error")

        # Execute
        result = upsert_file(sample_gazette_file, "gazette_pdfs/2024/01/test.pdf", "abc123hash")

        # Assert
        assert result is False

    @patch("gazette_scraper.supabase_client.get_supabase_client")
    def test_log_error_success(self, mock_get_client):
        """Test successful error logging to Supabase."""
        # Setup
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client

        mock_table = MagicMock()
        mock_client.table.return_value = mock_table

        mock_insert = MagicMock()
        mock_table.insert.return_value = mock_insert

        mock_execute = MagicMock()
        mock_insert.execute.return_value = mock_execute
        mock_execute.data = [{"id": 1}]

        # Execute
        result = log_error(
            "https://example.com/listing",
            "https://example.com/download",
            "404 Not Found",
            404
        )

        # Assert
        assert result is True
        mock_client.table.assert_called_once_with("download_errors")

        # Verify the insert call (structure matches actual implementation)
        mock_table.insert.assert_called_once()
        call_args = mock_table.insert.call_args[0][0]
        assert call_args["listing_url"] == "https://example.com/listing"
        assert call_args["download_url"] == "https://example.com/download"
        assert call_args["error_message"] == "404 Not Found"
        assert call_args["http_status"] == 404
        assert "occurred_at" in call_args  # Timestamp should be included

    @patch("gazette_scraper.supabase_client.get_supabase_client")
    def test_log_error_no_client(self, mock_get_client):
        """Test error logging when Supabase client is not available."""
        # Setup
        mock_get_client.return_value = None

        # Execute
        result = log_error(
            "https://example.com/listing",
            "https://example.com/download",
            "404 Not Found",
            404
        )

        # Assert
        assert result is False

    @patch("gazette_scraper.supabase_client.get_supabase_client")
    def test_log_error_exception(self, mock_get_client):
        """Test error logging when Supabase operation fails."""
        # Setup
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client

        mock_table = MagicMock()
        mock_client.table.return_value = mock_table
        mock_table.insert.side_effect = Exception("Database error")

        # Execute
        result = log_error(
            "https://example.com/listing",
            "https://example.com/download",
            "404 Not Found",
            404
        )

        # Assert
        assert result is False


class TestSyncUnsyncedFiles:
    """Test cases for sync_unsynced_files functionality."""

    def setup_method(self):
        """Reset global client state before each test."""
        # We need to reset the global state in the module
        import gazette_scraper.supabase_client as supabase_module
        supabase_module._client = None

    @pytest.fixture
    def setup_test_db(self, temp_dir):
        """Create a test SQLite database with sample data."""
        db_path = temp_dir / "test.db"
        ScrapingState(db_path)

        # Create sample files - some synced, some not
        with sqlite3.connect(db_path) as conn:
            conn.executemany("""
                INSERT INTO downloaded_files (
                    filename, sha256, download_url, listing_url, local_path,
                    year, month, title, size_bytes, size_str, pub_date,
                    discovered_at, supabase_synced
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, [
                (
                    "file1.pdf", "hash1", "https://example.com/file1",
                    "https://example.com/listing1", "/path/file1.pdf",
                    2024, 1, "File 1", 1000, "1KB", None,
                    "2024-01-01T10:00:00", 0  # Not synced
                ),
                (
                    "file2.pdf", "hash2", "https://example.com/file2",
                    "https://example.com/listing2", "/path/file2.pdf",
                    2024, 2, "File 2", 2000, "2KB", None,
                    "2024-02-01T10:00:00", 1  # Already synced
                ),
                (
                    "file3.pdf", "hash3", "https://example.com/file3",
                    "https://example.com/listing3", "/path/file3.pdf",
                    2024, 3, "File 3", 3000, "3KB", None,
                    "2024-03-01T10:00:00", 0  # Not synced
                ),
            ])

        return db_path

    @patch("gazette_scraper.supabase_client.get_supabase_client")
    def test_sync_unsynced_files_success(self, mock_get_client, setup_test_db):
        """Test successful sync of unsynced files."""
        # Setup
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client

        mock_table = MagicMock()
        mock_client.table.return_value = mock_table

        mock_upsert = MagicMock()
        mock_table.upsert.return_value = mock_upsert

        mock_execute = MagicMock()
        mock_upsert.execute.return_value = mock_execute
        mock_execute.data = [{"id": 1}]

        # Execute
        result = sync_unsynced_files(str(setup_test_db))

        # Assert - sync_unsynced_files only processes unsynced files
        # file2 was already synced (supabase_synced=1) so it won't be processed at all
        assert result["synced"] == 2  # file1 and file3
        assert result["failed"] == 0
        assert result["skipped"] == 0  # No files are skipped in this function - they're filtered by the query

        # Verify upsert was called twice (for file1 and file3)
        assert mock_table.upsert.call_count == 2

    @patch("gazette_scraper.supabase_client.get_supabase_client")
    def test_sync_unsynced_files_no_client(self, mock_get_client, setup_test_db):
        """Test sync when Supabase client is not available."""
        # Setup
        mock_get_client.return_value = None

        # Execute
        result = sync_unsynced_files(str(setup_test_db))

        # Assert
        assert result["synced"] == 0
        assert result["failed"] == 0
        assert result["skipped"] == 0  # When no client, the function returns early

    @patch("gazette_scraper.supabase_client.get_supabase_client")
    def test_sync_unsynced_files_partial_failure(self, mock_get_client, setup_test_db):
        """Test sync with some successes and some failures."""
        # Setup
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client

        mock_table = MagicMock()
        mock_client.table.return_value = mock_table

        mock_upsert = MagicMock()
        mock_table.upsert.return_value = mock_upsert

        # First call succeeds, second fails
        mock_execute_success = MagicMock()
        mock_execute_success.data = [{"id": 1}]

        mock_upsert.execute.side_effect = [
            mock_execute_success,  # First file succeeds
            Exception("Database error")  # Second file fails
        ]

        # Execute
        result = sync_unsynced_files(str(setup_test_db))

        # Assert
        assert result["synced"] == 1  # One success
        assert result["failed"] == 1  # One failure
        assert result["skipped"] == 0  # No skips in this function - file2 is filtered out by the query

    def test_sync_unsynced_files_empty_db(self, temp_dir):
        """Test sync with empty database."""
        # Setup
        db_path = temp_dir / "empty.db"
        ScrapingState(db_path)  # Creates empty database

        # Execute
        result = sync_unsynced_files(str(db_path))

        # Assert
        assert result["synced"] == 0
        assert result["failed"] == 0
        assert result["skipped"] == 0

    @patch("gazette_scraper.supabase_client.get_supabase_client")
    def test_sync_unsynced_files_nonexistent_db(self, mock_get_client, temp_dir):
        """Test sync with nonexistent database file."""
        # Setup
        mock_get_client.return_value = None  # No client configured
        db_path = temp_dir / "nonexistent.db"

        # Execute
        result = sync_unsynced_files(str(db_path))

        # Assert - should return early with no operations
        assert result["synced"] == 0
        assert result["failed"] == 0
        assert result["skipped"] == 0


class TestSupabaseIntegrationInPipeline:
    """Test Supabase integration within the download pipeline."""

    def setup_method(self):
        """Reset global client state before each test."""
        # We need to reset the global state in the module
        import gazette_scraper.supabase_client as supabase_module
        supabase_module._client = None

    @patch("gazette_scraper.pipeline.upsert_file")
    @patch("gazette_scraper.pipeline.log_error")
    def test_pipeline_successful_download_triggers_upsert(self, mock_log_error, mock_upsert_file):
        """Test that successful download triggers exactly one upsert with correct data."""

        # This would be a more complex integration test
        # For now, we verify the calls are made correctly in the pipeline
        # The actual integration test would require setting up the full pipeline
        pass  # Placeholder for integration test

    @patch("gazette_scraper.pipeline.upsert_file")
    @patch("gazette_scraper.pipeline.log_error")
    def test_pipeline_failed_download_logs_error(self, mock_log_error, mock_upsert_file):
        """Test that failed download writes to download_errors table."""

        # This would be a more complex integration test
        # For now, we verify the calls are made correctly in the pipeline
        # The actual integration test would require setting up the full pipeline
        pass  # Placeholder for integration test


class TestSupabaseClientConfiguration:
    """Test configuration handling for Supabase client."""

    def setup_method(self):
        """Reset global client state before each test."""
        # We need to reset the global state in the module
        import gazette_scraper.supabase_client as supabase_module
        supabase_module._client = None

    @patch.dict("os.environ", {"SUPABASE_SERVICE_ROLE_KEY": "env_key"})
    @patch("gazette_scraper.supabase_client.load_config")
    @patch("supabase.create_client")
    def test_environment_variable_expansion(self, mock_create_client, mock_load_config):
        """Test that environment variables in config are properly expanded."""
        # Setup
        mock_config = MagicMock()
        mock_config.supabase_url = "https://test.supabase.co"
        mock_config.supabase_key = "env_key"  # Should be expanded from ${SUPABASE_SERVICE_ROLE_KEY}
        mock_load_config.return_value = mock_config

        mock_client = MagicMock()
        mock_create_client.return_value = mock_client

        # Execute
        get_supabase_client()

        # Assert
        mock_create_client.assert_called_once_with(
            "https://test.supabase.co",
            "env_key"
        )

    @patch("gazette_scraper.supabase_client.load_config")
    @patch("supabase.create_client")
    def test_client_creation_error_handling(self, mock_create_client, mock_load_config):
        """Test error handling when Supabase client creation fails."""
        # Setup
        mock_config = MagicMock()
        mock_config.supabase_url = "https://test.supabase.co"
        mock_config.supabase_key = "test_key"
        mock_load_config.return_value = mock_config

        mock_create_client.side_effect = Exception("Connection error")

        # Execute
        result = get_supabase_client()

        # Assert - should return None when client creation fails
        assert result is None
