-- Supabase Schema Updates for Document Classification
-- Run these SQL commands in your Supabase SQL editor

-- Add new columns to gazette_files table for document classification
ALTER TABLE gazette_files ADD COLUMN IF NOT EXISTS document_type TEXT;
ALTER TABLE gazette_files ADD COLUMN IF NOT EXISTS subject_category TEXT;  
ALTER TABLE gazette_files ADD COLUMN IF NOT EXISTS keywords TEXT[];

-- Create indexes for efficient querying by document types
CREATE INDEX IF NOT EXISTS gazette_files_document_type_idx ON gazette_files(document_type);
CREATE INDEX IF NOT EXISTS gazette_files_subject_category_idx ON gazette_files(subject_category);
CREATE INDEX IF NOT EXISTS gazette_files_keywords_idx ON gazette_files USING GIN(keywords);

-- Create analytics views for document statistics

-- 1. Document Type Distribution View
CREATE OR REPLACE VIEW document_type_stats AS
SELECT 
    document_type,
    COUNT(*) as file_count,
    ROUND(COUNT(*)::decimal / (SELECT COUNT(*) FROM gazette_files) * 100, 2) as percentage,
    MIN(year) as earliest_year,
    MAX(year) as latest_year,
    SUM(size_bytes) as total_size_bytes
FROM gazette_files 
WHERE document_type IS NOT NULL
GROUP BY document_type
ORDER BY file_count DESC;

-- 2. Subject Category Distribution View  
CREATE OR REPLACE VIEW subject_category_stats AS
SELECT 
    subject_category,
    COUNT(*) as file_count,
    ROUND(COUNT(*)::decimal / (SELECT COUNT(*) FROM gazette_files) * 100, 2) as percentage,
    MIN(year) as earliest_year,
    MAX(year) as latest_year,
    AVG(size_bytes) as avg_size_bytes
FROM gazette_files 
WHERE subject_category IS NOT NULL
GROUP BY subject_category
ORDER BY file_count DESC;

-- 3. Monthly Document Type Trends View
CREATE OR REPLACE VIEW monthly_document_trends AS
SELECT 
    year,
    month,
    document_type,
    COUNT(*) as file_count,
    SUM(size_bytes) as total_size_bytes
FROM gazette_files 
WHERE document_type IS NOT NULL
GROUP BY year, month, document_type
ORDER BY year DESC, month DESC, file_count DESC;

-- 4. Keyword Analysis View
CREATE OR REPLACE VIEW keyword_analysis AS
SELECT 
    keyword,
    COUNT(*) as frequency,
    ARRAY_AGG(DISTINCT document_type) as document_types,
    ARRAY_AGG(DISTINCT subject_category) as subject_categories,
    MIN(year) as first_appearance,
    MAX(year) as last_appearance
FROM gazette_files,
     UNNEST(keywords) as keyword
WHERE keywords IS NOT NULL AND array_length(keywords, 1) > 0
GROUP BY keyword
ORDER BY frequency DESC;

-- 5. Document Production Timeline View
CREATE OR REPLACE VIEW document_timeline AS
SELECT 
    year,
    month,
    COUNT(*) as total_documents,
    COUNT(DISTINCT document_type) as unique_document_types,
    SUM(size_bytes) as total_size_bytes,
    AVG(size_bytes) as avg_document_size,
    COUNT(*) FILTER (WHERE document_type = 'names_registration') as names_docs,
    COUNT(*) FILTER (WHERE document_type = 'land_use_plans') as land_docs,
    COUNT(*) FILTER (WHERE document_type = 'cooperative') as cooperative_docs,
    COUNT(*) FILTER (WHERE document_type = 'appointments') as appointment_docs
FROM gazette_files
GROUP BY year, month
ORDER BY year DESC, month DESC;

-- 6. Legal Document Coverage Analysis
CREATE OR REPLACE VIEW coverage_analysis AS
SELECT 
    'Total Documents' as metric,
    COUNT(*)::text as value
FROM gazette_files
UNION ALL
SELECT 
    'Years Covered' as metric,
    (MAX(year) - MIN(year) + 1)::text as value
FROM gazette_files
UNION ALL  
SELECT 
    'Document Types' as metric,
    COUNT(DISTINCT document_type)::text as value
FROM gazette_files
WHERE document_type IS NOT NULL
UNION ALL
SELECT 
    'Subject Categories' as metric,
    COUNT(DISTINCT subject_category)::text as value  
FROM gazette_files
WHERE subject_category IS NOT NULL
UNION ALL
SELECT
    'Total Size (GB)' as metric,
    ROUND(SUM(size_bytes)::decimal / (1024*1024*1024), 2)::text as value
FROM gazette_files
WHERE size_bytes IS NOT NULL;

-- 7. Recent Activity Dashboard View
CREATE OR REPLACE VIEW recent_activity AS
SELECT 
    'Last 7 Days' as period,
    COUNT(*) as documents_added,
    COUNT(DISTINCT document_type) as document_types,
    SUM(size_bytes) as total_bytes
FROM gazette_files 
WHERE downloaded_at > NOW() - INTERVAL '7 days'
UNION ALL
SELECT 
    'Last 30 Days' as period,
    COUNT(*) as documents_added,
    COUNT(DISTINCT document_type) as document_types,
    SUM(size_bytes) as total_bytes
FROM gazette_files 
WHERE downloaded_at > NOW() - INTERVAL '30 days'
UNION ALL
SELECT 
    'Last 90 Days' as period,
    COUNT(*) as documents_added,
    COUNT(DISTINCT document_type) as document_types,
    SUM(size_bytes) as total_bytes
FROM gazette_files 
WHERE downloaded_at > NOW() - INTERVAL '90 days';

-- 8. Error Analysis View
CREATE OR REPLACE VIEW error_analysis AS
SELECT 
    error_message,
    http_status,
    COUNT(*) as error_count,
    COUNT(DISTINCT listing_url) as affected_pages,
    MIN(occurred_at) as first_occurrence,
    MAX(occurred_at) as last_occurrence
FROM download_errors
GROUP BY error_message, http_status
ORDER BY error_count DESC;

-- Comments for the schema
COMMENT ON TABLE gazette_files IS 'Official Gazette PDFs with document classification and metadata';
COMMENT ON COLUMN gazette_files.document_type IS 'Type of legal document (names_registration, cooperative, etc.)';
COMMENT ON COLUMN gazette_files.subject_category IS 'Subject matter category (corporate, administrative, etc.)';
COMMENT ON COLUMN gazette_files.keywords IS 'Array of keywords extracted from document title';

COMMENT ON VIEW document_type_stats IS 'Statistical breakdown of documents by type';
COMMENT ON VIEW subject_category_stats IS 'Statistical breakdown of documents by subject category';
COMMENT ON VIEW monthly_document_trends IS 'Monthly trends showing document type distribution over time';
COMMENT ON VIEW keyword_analysis IS 'Analysis of keyword frequency and associations';
COMMENT ON VIEW document_timeline IS 'Monthly document production timeline with key statistics';
COMMENT ON VIEW coverage_analysis IS 'High-level coverage and statistics of the legal document collection';
COMMENT ON VIEW recent_activity IS 'Recent scraping activity dashboard metrics';
COMMENT ON VIEW error_analysis IS 'Analysis of download errors for troubleshooting';