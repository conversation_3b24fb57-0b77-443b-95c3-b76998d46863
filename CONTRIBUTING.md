# Contributing to Rwanda Gazette Scraper

We welcome contributions to improve the Rwanda Official Gazette scraper! This document outlines our development practices, testing requirements, and contribution workflow.

## Development Setup

### Prerequisites

- Python 3.11+
- Poetry
- Git

### Initial Setup

```bash
# Clone the repository
git clone https://github.com/Jpkay/rw-laws-scraper.git
cd rw-laws-scraper

# Install dependencies
poetry install

# Set up pre-commit hooks
poetry run pre-commit install

# Activate the virtual environment
poetry shell
```

## Code Quality Standards

This project enforces strict quality gates that must pass before any code is merged:

### Quality Gates (CI Requirements)

| Gate                    | Tool              | Requirement       | Command         |
|-------------------------|-------------------|-------------------|-----------------|
| **Linting**            | Ruff              | 0 warnings        | `make lint`     |
| **Type Safety**        | Mypy (strict)     | 0 errors          | `make type`     |
| **Test Coverage**      | pytest-cov       | ≥ 70%             | `make cov`      |
| **Code Formatting**    | Ruff              | No changes needed | `make format-check` |
| **Import Organization**| Ruff TCH          | 0 violations      | (included in lint) |

### Running Quality Checks

```bash
# Run all quality gates (must pass for CI)
make all

# Individual quality checks
make lint          # Ruff linting + import organization
make type          # Mypy strict type checking
make cov           # Tests with coverage (fails under 70%)
make format-check  # Verify code formatting
```

### Code Formatting

We use Ruff for both linting and formatting:

```bash
# Auto-format code
make format

# Check formatting without making changes
make format-check
```

## Testing Guidelines

### Test Coverage Requirements

- **Minimum coverage**: 70% overall
- **New code**: Should be covered by tests
- **Critical paths**: Must have comprehensive test coverage

### Test Structure

```
tests/
├── conftest.py                    # Shared fixtures
├── test_client_retry_flow.py      # HTTP client tests
├── test_config.py                 # Configuration tests
├── test_duplicate_handling.py     # Deduplication tests
├── test_integration.py            # End-to-end integration tests
├── test_models.py                 # Data model tests
├── test_parser.py                 # HTML parsing tests
├── test_pipeline_*.py             # Pipeline component tests
├── test_state*.py                 # State management tests
└── test_storage.py                # File storage tests
```

### Test Fixture Policy

**Network Isolation**: All tests must run without internet connectivity.

- **✅ Allowed**: Mock HTTP responses using `responses` or `pytest-vcr`
- **✅ Allowed**: File system fixtures in `tests/fixtures/`
- **❌ Forbidden**: Live HTTP requests to external services
- **❌ Forbidden**: Tests that depend on external website availability

### Writing Tests

#### HTTP Mocking

Use `responses` library for HTTP mocking:

```python
import responses
from gazette_scraper.client import GazetteHTTPClient

@responses.activate
def test_client_request():
    responses.add(
        responses.GET,
        "https://minijust.gov.rw/api/data",
        json={"status": "ok"},
        status=200
    )
    
    client = GazetteHTTPClient()
    response = client.get("https://minijust.gov.rw/api/data")
    assert response.status_code == 200
```

#### File Fixtures

Create fixture files for complex test data:

```python
# tests/conftest.py
@pytest.fixture
def sample_gazette_listing_html():
    \"\"\"Sample HTML content for folder listing page.\"\"\"
    fixture_path = Path(__file__).parent / "fixtures" / "gazette_listing.html"
    return fixture_path.read_text()
```

#### Integration Test Patterns

For pipeline integration tests:

```python
@patch('gazette_scraper.client.GazetteHTTPClient.get')
def test_pipeline_end_to_end(mock_get, temp_dir):
    # Mock all HTTP responses
    mock_get.side_effect = [
        Mock(text=year_listing_html, status_code=200),
        Mock(text=month_listing_html, status_code=200),
        Mock(text=file_listing_html, status_code=200),
    ]
    
    config = ScrapingConfig(output_dir=temp_dir, dry_run=True)
    pipeline = GazettePipeline(config)
    result = pipeline.run()
    
    # Assert expected discoveries
    assert result.total_discovered >= expected_count
```

### Running Tests

```bash
# Run all tests with coverage
make cov

# Run tests without coverage (faster)
make test-quick

# Run specific test file
poetry run pytest tests/test_parser.py -v

# Run with verbose output
poetry run pytest -v

# Run tests matching pattern
poetry run pytest -k "test_duplicate" -v
```

## Branch Naming Convention

Use descriptive branch names with prefixes:

- `feature/add-gcs-integration` - New features
- `fix/handle-malformed-html` - Bug fixes  
- `refactor/client-retry-logic` - Code refactoring
- `docs/update-contributing` - Documentation updates
- `test/improve-parser-coverage` - Test improvements

## Commit Message Guidelines

We follow [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer]
```

### Types

- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

### Examples

```
feat(parser): add support for paginated file listings

- Implement pagination detection in FolderParser
- Add tests for multi-page folder parsing
- Update integration tests with pagination scenarios

Closes #42
```

```
fix(client): handle 410 Gone responses with retry logic

When a file URL returns 410 Gone, refresh the listing page
and retry with the updated URL before marking as failed.

Fixes #38
```

```
test: improve coverage for duplicate file handling

Add comprehensive tests for:
- Same PDF discovered via multiple listing URLs
- SHA256-based deduplication 
- Download state tracking across discoveries

Coverage increased from 68% to 74%
```

## Pull Request Process

### Before Opening a PR

1. **Create feature branch**: `git checkout -b feature/your-feature-name`
2. **Write tests**: Ensure new code is covered by tests
3. **Run quality gates**: `make all` must pass
4. **Update documentation**: Update README.md if needed
5. **Write clear commits**: Follow conventional commit format

### PR Checklist

- [ ] All quality gates pass (`make all`)
- [ ] Tests cover new functionality
- [ ] No live HTTP requests in tests
- [ ] Documentation updated if needed
- [ ] Commit messages follow conventional format
- [ ] PR description explains changes clearly

### PR Template

```markdown
## Description
Brief description of changes made.

## Type of Change
- [ ] Bug fix (non-breaking change that fixes an issue)
- [ ] New feature (non-breaking change that adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Tests pass locally (`make all`)
- [ ] Added tests for new functionality
- [ ] Coverage meets 70% threshold

## Screenshots/Logs (if applicable)
Add any relevant output or screenshots.
```

## Code Style Guidelines

### Python Style

- Follow PEP 8 (enforced by Ruff)
- Use type hints for all functions and methods
- Prefer explicit imports over wildcard imports
- Keep functions focused and small
- Use descriptive variable names

### Type Checking

- All code must pass `mypy --strict`
- Use `TYPE_CHECKING` blocks for imports only needed for type checking:

```python
from __future__ import annotations

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from pathlib import Path
    from .models import GazetteFile
```

### Error Handling

- Use specific exception types
- Provide meaningful error messages
- Log errors with appropriate levels
- Implement retry logic with exponential backoff

### Logging

```python
import logging

logger = logging.getLogger(__name__)

def process_file(file: GazetteFile) -> bool:
    logger.info(f"Processing file: {file.filename}")
    try:
        # Process file
        logger.debug(f"File processed successfully: {file.filename}")
        return True
    except Exception as e:
        logger.error(f"Failed to process {file.filename}: {e}")
        return False
```

## Dependency Management

- Use Poetry for dependency management
- Pin major versions in `pyproject.toml`
- Test with multiple Python versions (3.11, 3.12)
- Keep dependencies minimal and well-justified

### Adding Dependencies

```bash
# Add runtime dependency
poetry add requests

# Add development dependency  
poetry add --group dev pytest-mock

# Update lockfile
poetry lock --no-update
```

## Release Process

Releases are automated via GitHub Actions when tags are pushed:

1. **Update CHANGELOG.md** with new version
2. **Create version tag**: `git tag v0.2.0`
3. **Push tag**: `git push origin v0.2.0`
4. **GitHub Actions** will build and create release

## Getting Help

- **Issues**: Report bugs and feature requests via GitHub Issues
- **Discussions**: Ask questions in GitHub Discussions
- **Documentation**: Check README.md and code comments
- **Code Review**: Maintainers will review PRs and provide feedback

## License

By contributing, you agree that your contributions will be licensed under the MIT License.