[mypy]
# Strict type checking configuration
python_version = 3.11
strict = True

# Error reporting
show_error_codes = True
show_error_context = True
show_column_numbers = True
pretty = True

# Disallow dynamic typing
disallow_any_unimported = True
# disallow_any_expr = True  # Too restrictive for now, enable later
disallow_any_decorated = True
disallow_any_explicit = True
disallow_any_generics = True
disallow_subclassing_any = True

# Untyped definitions and calls
disallow_untyped_calls = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True

# None and Optional handling
no_implicit_optional = True
strict_optional = True

# Configuring warnings
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_return_any = True
warn_unreachable = True

# Miscellaneous strictness flags
strict_equality = True
strict_concatenate = True

# Configuring error messages
ignore_missing_imports = False

# Cache
cache_dir = .mypy_cache
sqlite_cache = True

# Plugins
plugins = pydantic.mypy

# Per-module configuration
[mypy-tests.*]
# Tests can be slightly less strict
disallow_untyped_defs = False
disallow_incomplete_defs = False

[mypy-setuptools]
ignore_missing_imports = True

[mypy-bs4.*]
ignore_missing_imports = True

[mypy-requests.*]
ignore_missing_imports = True

[mypy-click.*]
ignore_missing_imports = True

[mypy-rich.*]
ignore_missing_imports = True

[mypy-tqdm.*]
ignore_missing_imports = True

[mypy-google.*]
ignore_missing_imports = True

[mypy-toml.*]
ignore_missing_imports = True

# Pydantic plugin settings
[pydantic-mypy]
init_forbid_extra = True
init_typed = True
warn_required_dynamic_aliases = True
warn_untyped_fields = True