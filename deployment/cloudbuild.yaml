# Cloud Build configuration for automated deployment
steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-f', 'deployment/docker/Dockerfile.production',
      '-t', 'gcr.io/$PROJECT_ID/case-law-scraper:$COMMIT_SHA',
      '-t', 'gcr.io/$PROJECT_ID/case-law-scraper:latest',
      '.'
    ]

  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/case-law-scraper:$COMMIT_SHA']

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/case-law-scraper:latest']

  # Run tests in the container
  - name: 'gcr.io/$PROJECT_ID/case-law-scraper:$COMMIT_SHA'
    entrypoint: 'poetry'
    args: ['run', 'pytest', 'tests/', '-v', '--tb=short']
    env:
      - 'ENVIRONMENT=test'

  # Deploy to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'run', 'deploy', 'case-law-scraper',
      '--image', 'gcr.io/$PROJECT_ID/case-law-scraper:$COMMIT_SHA',
      '--platform', 'managed',
      '--region', 'us-central1',
      '--memory', '4Gi',
      '--cpu', '2',
      '--timeout', '3600',
      '--concurrency', '1',
      '--max-instances', '3',
      '--min-instances', '0',
      '--port', '8080',
      '--set-env-vars', 'ENVIRONMENT=production,LOG_LEVEL=INFO',
      '--add-cloudsql-instances', '$PROJECT_ID:us-central1:rw-laws-db-prod',
      '--service-account', 'scraper-service-account@$PROJECT_ID.iam.gserviceaccount.com',
      '--allow-unauthenticated'
    ]

  # Update traffic to new revision
  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'run', 'services', 'update-traffic', 'case-law-scraper',
      '--to-latest',
      '--region', 'us-central1'
    ]

# Build options
options:
  machineType: 'E2_HIGHCPU_8'
  diskSizeGb: '100'
  logging: CLOUD_LOGGING_ONLY

# Timeout for the entire build
timeout: '1200s'

# Substitutions
substitutions:
  _DEPLOY_REGION: 'us-central1'
  _SERVICE_NAME: 'case-law-scraper'
