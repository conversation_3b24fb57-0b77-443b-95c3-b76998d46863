# Rwanda Case-Law Scraper - Production Deployment Guide

## Prerequisites

- Google Cloud Platform account with billing enabled
- Docker installed locally
- `gcloud` CLI configured
- Domain name for the application (optional)

## Step 1: Environment Setup

### 1.1 Create GCP Project
```bash
# Create new project
gcloud projects create rw-laws-scraper-prod --name="Rwanda Laws Scraper Production"

# Set as default project
gcloud config set project rw-laws-scraper-prod

# Enable required APIs
gcloud services enable \
    cloudbuild.googleapis.com \
    run.googleapis.com \
    storage.googleapis.com \
    sql.googleapis.com \
    scheduler.googleapis.com \
    pubsub.googleapis.com \
    monitoring.googleapis.com \
    logging.googleapis.com
```

### 1.2 Create Cloud Storage Bucket
```bash
# Create bucket for document storage
gsutil mb -p rw-laws-scraper-prod -c STANDARD -l us-central1 gs://rw-laws-documents-prod

# Create bucket for backups
gsutil mb -p rw-laws-scraper-prod -c NEARLINE -l us-central1 gs://rw-laws-backups-prod
```

### 1.3 Set up Cloud SQL Database
```bash
# Create PostgreSQL instance
gcloud sql instances create rw-laws-db-prod \
    --database-version=POSTGRES_15 \
    --tier=db-f1-micro \
    --region=us-central1 \
    --storage-type=SSD \
    --storage-size=20GB \
    --backup-start-time=02:00

# Create database
gcloud sql databases create caselaw_scraper --instance=rw-laws-db-prod

# Create user
gcloud sql users create scraper_user --instance=rw-laws-db-prod --password=SECURE_PASSWORD_HERE
```

## Step 2: Configuration Management

### 2.1 Create Production Environment File
```bash
# Create .env.production
cat > .env.production << EOF
# Database Configuration
DATABASE_URL=**************************************************************************************************************************

# Storage Configuration
GCS_BUCKET_DOCUMENTS=rw-laws-documents-prod
GCS_BUCKET_BACKUPS=rw-laws-backups-prod

# Scraper Configuration
HEADLESS_MODE=true
DEBUG_MODE=false
MAX_CONCURRENT_DOWNLOADS=3
BROWSER_TIMEOUT=45000
MAX_RETRY_ATTEMPTS=3

# Monitoring
ENABLE_METRICS=true
LOG_LEVEL=INFO
SENTRY_DSN=your_sentry_dsn_here

# Scheduling
SCRAPER_SCHEDULE="0 2 * * *"  # Daily at 2 AM
DISCOVERY_SCHEDULE="0 1 * * 0"  # Weekly on Sunday at 1 AM
EOF
```

### 2.2 Create Kubernetes Secrets
```bash
# Create secret for environment variables
kubectl create secret generic scraper-config \
    --from-env-file=.env.production \
    --dry-run=client -o yaml > k8s/secrets.yaml

# Create secret for service account
kubectl create secret generic gcp-service-account \
    --from-file=key.json=service-account-key.json \
    --dry-run=client -o yaml >> k8s/secrets.yaml
```

## Step 3: Container Build & Registry

### 3.1 Build Production Container
```bash
# Build and tag container
docker build -t gcr.io/rw-laws-scraper-prod/case-law-scraper:latest .

# Push to Google Container Registry
docker push gcr.io/rw-laws-scraper-prod/case-law-scraper:latest
```

### 3.2 Automated Build with Cloud Build
```yaml
# cloudbuild.yaml
steps:
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/case-law-scraper:$COMMIT_SHA', '.']
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/case-law-scraper:$COMMIT_SHA']
  - name: 'gcr.io/cloud-builders/gcloud'
    args: ['run', 'deploy', 'case-law-scraper', 
           '--image', 'gcr.io/$PROJECT_ID/case-law-scraper:$COMMIT_SHA',
           '--platform', 'managed',
           '--region', 'us-central1',
           '--allow-unauthenticated']
```

## Step 4: Cloud Run Deployment

### 4.1 Deploy to Cloud Run
```bash
# Deploy with environment variables
gcloud run deploy case-law-scraper \
    --image gcr.io/rw-laws-scraper-prod/case-law-scraper:latest \
    --platform managed \
    --region us-central1 \
    --memory 4Gi \
    --cpu 2 \
    --timeout 3600 \
    --concurrency 1 \
    --max-instances 3 \
    --set-env-vars "$(cat .env.production | tr '\n' ',')" \
    --add-cloudsql-instances rw-laws-scraper-prod:us-central1:rw-laws-db-prod \
    --service-account <EMAIL>
```

## Step 5: Scheduling & Automation

### 5.1 Create Cloud Scheduler Jobs
```bash
# Daily scraping job
gcloud scheduler jobs create http daily-scraper \
    --schedule="0 2 * * *" \
    --uri="https://case-law-scraper-xxx-uc.a.run.app/api/scrape" \
    --http-method=POST \
    --headers="Content-Type=application/json" \
    --message-body='{"mode": "incremental", "courts": ["all"]}' \
    --time-zone="Africa/Kigali"

# Weekly full discovery
gcloud scheduler jobs create http weekly-discovery \
    --schedule="0 1 * * 0" \
    --uri="https://case-law-scraper-xxx-uc.a.run.app/api/discover" \
    --http-method=POST \
    --headers="Content-Type=application/json" \
    --message-body='{"mode": "full_discovery"}' \
    --time-zone="Africa/Kigali"
```

## Step 6: Monitoring Setup

### 6.1 Configure Monitoring
```bash
# Create monitoring dashboard
gcloud monitoring dashboards create --config-from-file=monitoring/dashboard.json

# Set up alerting policies
gcloud alpha monitoring policies create --policy-from-file=monitoring/alerts.yaml
```

## Step 7: Backup Strategy

### 7.1 Database Backup
```bash
# Automated daily backups (already enabled in Cloud SQL)
# Manual backup command:
gcloud sql export sql rw-laws-db-prod gs://rw-laws-backups-prod/db-backup-$(date +%Y%m%d).sql \
    --database=caselaw_scraper
```

### 7.2 Document Backup
```bash
# Sync documents to backup bucket
gsutil -m rsync -r -d gs://rw-laws-documents-prod gs://rw-laws-backups-prod/documents/
```

## Step 8: CI/CD Pipeline

### 8.1 GitHub Actions Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: google-github-actions/setup-gcloud@v1
        with:
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          project_id: rw-laws-scraper-prod
      - run: gcloud builds submit --config cloudbuild.yaml
```

## Step 9: Health Checks & Monitoring

### 9.1 Health Check Endpoint
```python
# Add to main application
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow(),
        "version": "1.0.0",
        "database": await check_database_connection(),
        "storage": await check_storage_access()
    }
```

## Step 10: Security Considerations

### 10.1 IAM Roles
```bash
# Create service account with minimal permissions
gcloud iam service-accounts create scraper-service-account

# Grant necessary permissions
gcloud projects add-iam-policy-binding rw-laws-scraper-prod \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/cloudsql.client"

gcloud projects add-iam-policy-binding rw-laws-scraper-prod \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/storage.objectAdmin"
```

## Deployment Checklist

- [ ] GCP project created and configured
- [ ] Cloud SQL database set up
- [ ] Storage buckets created
- [ ] Environment variables configured
- [ ] Container built and pushed
- [ ] Cloud Run service deployed
- [ ] Scheduler jobs created
- [ ] Monitoring configured
- [ ] Backup strategy implemented
- [ ] CI/CD pipeline set up
- [ ] Health checks working
- [ ] Security policies applied
